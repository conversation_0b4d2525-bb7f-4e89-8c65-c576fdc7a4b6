import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { createInterface } from 'readline';

dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function getQuestion(questionId) {
  const { data, error } = await supabase
    .from('questions')
    .select('*')
    .eq('id', questionId)
    .single();

  if (error) {
    console.error('Error fetching question:', error);
    return null;
  }

  return data;
}

async function updateQuestionCorrectAnswer(questionId, correctAnswer) {
  const { data, error } = await supabase
    .from('questions')
    .update({ correct_answer: correctAnswer })
    .eq('id', questionId)
    .select();

  if (error) {
    console.error(`Error updating question ${questionId}:`, error);
    return false;
  }

  return true;
}

async function main() {
  try {
    const readline = createInterface({
      input: process.stdin,
      output: process.stdout
    });

    const questionId = await new Promise(resolve => {
      readline.question('Enter question ID: ', resolve);
    });

    const question = await getQuestion(questionId);

    if (!question) {
      console.log('Question not found.');
      readline.close();
      return;
    }

    console.log('\n' + '-'.repeat(80));
    console.log(`QUESTION: ${question.question_text}`);

    // Display all options
    console.log('\nOPTIONS:');
    const options = question.options;
    let optionKeys = [];

    if (typeof options === 'object') {
      // Handle both numeric and letter keys
      if ('A' in options || 'B' in options) {
        // Letter keys (A, B, C, D)
        optionKeys = Object.keys(options).filter(key => ['A', 'B', 'C', 'D'].includes(key));
      } else {
        // Numeric keys (0, 1, 2, 3)
        optionKeys = Object.keys(options).filter(key => ['0', '1', '2', '3'].includes(key));
      }
    }

    for (const key of optionKeys) {
      const isCurrentAnswer = key === question.correct_answer;
      const marker = isCurrentAnswer ? '(Current)' : '';
      console.log(`  ${key}: ${options[key]} ${marker}`);
    }

    console.log(`\nCURRENT ANSWER: ${question.correct_answer} - "${options[question.correct_answer]}"`);
    console.log(`EXPLANATION: ${question.explanation}`);

    const newAnswer = await new Promise(resolve => {
      readline.question(`\nEnter new correct answer (${optionKeys.join(', ')}) or press Enter to keep current: `, resolve);
    });

    if (newAnswer && optionKeys.includes(newAnswer)) {
      const success = await updateQuestionCorrectAnswer(questionId, newAnswer);
      console.log(success ? 'Updated successfully!' : 'Update failed.');
    } else if (newAnswer) {
      console.log(`Invalid answer key. Must be one of: ${optionKeys.join(', ')}`);
    } else {
      console.log('No changes made.');
    }

    readline.close();

  } catch (error) {
    console.error('Error:', error);
  }
}

main();
