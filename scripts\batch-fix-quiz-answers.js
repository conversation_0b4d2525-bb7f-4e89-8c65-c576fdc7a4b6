import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { createInterface } from 'readline';

dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Topic ID for CISSP Fundamentals
const CISSP_TOPIC_ID = '1de31e9d-97e5-4ee7-9e89-968615011645';

// Confidence threshold for automatic updates (high, medium, low)
const CONFIDENCE_THRESHOLD = 'medium';

async function getQuestionsForTopic(topicId) {
  const { data, error } = await supabase
    .from('questions')
    .select('*')
    .eq('topic_id', topicId);

  if (error) {
    console.error('Error fetching questions:', error);
    return [];
  }

  return data;
}

async function updateQuestionCorrectAnswer(questionId, correctAnswer) {
  const { data, error } = await supabase
    .from('questions')
    .update({ correct_answer: correctAnswer })
    .eq('id', questionId)
    .select();

  if (error) {
    console.error(`Error updating question ${questionId}:`, error);
    return false;
  }

  return true;
}

// Manual corrections for specific questions
// Format: questionId -> correct answer key
const MANUAL_CORRECTIONS = {
  // Add specific corrections here if needed
  // 'question-id-here': 'B',
};

function analyzeQuestion(question) {
  const { id, question_text, options, correct_answer, explanation } = question;

  // Check if we have a manual correction for this question
  if (MANUAL_CORRECTIONS[id]) {
    return {
      id,
      question_text,
      options,
      current_answer: correct_answer,
      current_answer_text: options[correct_answer],
      explanation,
      needs_review: true,
      suggested_answer: MANUAL_CORRECTIONS[id],
      suggested_answer_text: options[MANUAL_CORRECTIONS[id]],
      confidence: 'manual',
      reasoning: 'Manual correction'
    };
  }

  // Convert options to array for easier analysis
  let optionKeys = [];

  if (typeof options === 'object') {
    // Handle both numeric and letter keys
    if ('A' in options || 'B' in options) {
      // Letter keys (A, B, C, D)
      optionKeys = Object.keys(options).filter(key => ['A', 'B', 'C', 'D'].includes(key));
    } else {
      // Numeric keys (0, 1, 2, 3)
      optionKeys = Object.keys(options).filter(key => ['0', '1', '2', '3'].includes(key));
    }
  }

  // Get current answer text
  const currentOptionText = options[correct_answer];

  // Initialize result
  const result = {
    id,
    question_text,
    options,
    option_keys: optionKeys,
    current_answer: correct_answer,
    current_answer_text: currentOptionText,
    explanation,
    needs_review: false,
    suggested_answer: null,
    suggested_answer_text: null,
    confidence: 'high',
    reasoning: ''
  };

  // Advanced analysis based on explanation and question
  const explanationLower = explanation.toLowerCase();

  // For each option, check if it's strongly indicated in the explanation
  let bestMatchKey = null;
  let bestMatchScore = 0;
  let bestMatchReasoning = '';

  for (const key of optionKeys) {
    const optionText = options[key];
    if (!optionText) continue;

    const optionLower = optionText.toLowerCase();
    let matchScore = 0;
    let matchReasons = [];

    // Check for direct mention of the option text in the explanation
    if (explanationLower.includes(optionLower)) {
      matchScore += 3;
      matchReasons.push(`Option text "${optionText}" appears directly in the explanation`);
    }

    // Check for key phrases that indicate this is the correct answer
    const correctPhrases = [
      'correct answer', 'right answer', 'is correct', 'is right',
      'refers to', 'is defined as', 'is the', 'involves', 'means'
    ];

    for (const phrase of correctPhrases) {
      if (explanationLower.includes(`${phrase} ${optionLower}`) ||
          explanationLower.includes(`${optionLower} ${phrase}`)) {
        matchScore += 2;
        matchReasons.push(`Explanation contains phrase indicating this is correct: "${phrase}"`);
      }
    }

    // Check for key words from the option in the explanation
    const optionWords = optionLower.split(/\s+/).filter(word => word.length > 3);
    for (const word of optionWords) {
      if (explanationLower.includes(word)) {
        matchScore += 0.5;
        matchReasons.push(`Key word "${word}" from option appears in explanation`);
      }
    }

    // Special case: If explanation starts by repeating the option text
    if (explanationLower.startsWith(optionLower) ||
        explanationLower.startsWith(optionLower.replace(/^(a|an|the) /, ''))) {
      matchScore += 2;
      matchReasons.push('Explanation begins with the option text');
    }

    // If this option has a better match than our current best
    if (matchScore > bestMatchScore) {
      bestMatchScore = matchScore;
      bestMatchKey = key;
      bestMatchReasoning = matchReasons.join('. ');
    }
  }

  // Determine confidence level based on match score
  let confidence = 'low';
  if (bestMatchScore > 4) confidence = 'high';
  else if (bestMatchScore > 2) confidence = 'medium';

  // If we found a better match and it's different from current answer
  if (bestMatchKey && bestMatchKey !== correct_answer && bestMatchScore > 1) {
    result.needs_review = true;
    result.suggested_answer = bestMatchKey;
    result.suggested_answer_text = options[bestMatchKey];
    result.confidence = confidence;
    result.reasoning = bestMatchReasoning;
  }

  return result;
}

// CISSP-specific corrections based on domain knowledge
function applyCISSPCorrections(analysisResults) {
  // Apply specific corrections for CISSP questions
  for (const result of analysisResults) {
    // Example: If question is about defense in depth, the answer should be multiple layers
    if (result.question_text.toLowerCase().includes('defense in depth')) {
      for (const key in result.options) {
        const option = result.options[key].toLowerCase();
        if (option.includes('multiple layer') || option.includes('layer') && option.includes('security')) {
          result.needs_review = true;
          result.suggested_answer = key;
          result.suggested_answer_text = result.options[key];
          result.confidence = 'high';
          result.reasoning = 'CISSP domain knowledge: Defense in depth refers to multiple layers of security controls';
        }
      }
    }

    // Example: If question is about risk transference, the answer should be about insurance or third party
    if (result.question_text.toLowerCase().includes('risk transfer')) {
      for (const key in result.options) {
        const option = result.options[key].toLowerCase();
        if (option.includes('transfer') || option.includes('insurance') || option.includes('third party')) {
          result.needs_review = true;
          result.suggested_answer = key;
          result.suggested_answer_text = result.options[key];
          result.confidence = 'high';
          result.reasoning = 'CISSP domain knowledge: Risk transference involves transferring risk to another party';
        }
      }
    }
  }

  return analysisResults;
}

async function main() {
  try {
    console.log('Fetching CISSP Fundamentals questions...');
    const questions = await getQuestionsForTopic(CISSP_TOPIC_ID);
    console.log(`Found ${questions.length} questions.`);

    let analysisResults = questions.map(analyzeQuestion);

    // Apply CISSP-specific corrections
    analysisResults = applyCISSPCorrections(analysisResults);

    const questionsNeedingReview = analysisResults.filter(result => result.needs_review);

    console.log(`\n${questionsNeedingReview.length} questions need review.`);

    // Filter questions that meet confidence threshold for automatic update
    const confidenceLevels = { 'high': 3, 'medium': 2, 'low': 1, 'manual': 4 };
    const thresholdLevel = confidenceLevels[CONFIDENCE_THRESHOLD];

    const questionsToUpdate = questionsNeedingReview.filter(
      q => confidenceLevels[q.confidence] >= thresholdLevel
    );

    console.log(`${questionsToUpdate.length} questions meet the confidence threshold for automatic update.`);

    // Display all questions that will be updated
    for (const result of questionsToUpdate) {
      console.log('\n' + '-'.repeat(80));
      console.log(`QUESTION: ${result.question_text}`);
      console.log(`CURRENT ANSWER: ${result.current_answer} - "${result.current_answer_text}"`);
      console.log(`NEW ANSWER: ${result.suggested_answer} - "${result.suggested_answer_text}"`);
      console.log(`CONFIDENCE: ${result.confidence}`);
      console.log(`REASONING: ${result.reasoning}`);
    }

    // Ask for confirmation before batch update
    const readline = createInterface({
      input: process.stdin,
      output: process.stdout
    });

    const answer = await new Promise(resolve => {
      readline.question(`\nUpdate all ${questionsToUpdate.length} questions? (y/n): `, resolve);
    });

    if (answer.toLowerCase() === 'y') {
      console.log('Updating questions...');

      let successCount = 0;
      let failCount = 0;

      for (const result of questionsToUpdate) {
        const success = await updateQuestionCorrectAnswer(result.id, result.suggested_answer);
        if (success) {
          successCount++;
          console.log(`Updated question: ${result.id}`);
        } else {
          failCount++;
          console.log(`Failed to update question: ${result.id}`);
        }
      }

      console.log(`\nUpdate complete. ${successCount} questions updated successfully, ${failCount} failed.`);
    } else {
      console.log('Update cancelled.');
    }

    readline.close();

  } catch (error) {
    console.error('Error:', error);
  }
}

main();
