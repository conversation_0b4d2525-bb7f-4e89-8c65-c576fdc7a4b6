import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Create Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://agdyycknlxojiwhlqicq.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

const supabase = createClient(supabaseUrl, supabaseKey);

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Call the function to check for expired subscriptions
    const { data, error } = await supabase.rpc('check_expired_subscriptions');
    
    if (error) {
      console.error('Error checking expired subscriptions:', error);
      return res.status(500).json({ 
        success: false, 
        error: error.message 
      });
    }
    
    return res.status(200).json({ 
      success: true, 
      updated: data ? data.length : 0,
      data
    });
  } catch (error) {
    console.error('Error in check-expired-subscriptions API:', error);
    return res.status(500).json({ 
      success: false, 
      error: error.message || 'An unknown error occurred' 
    });
  }
}
