
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes glow {
  0% {
    box-shadow: 0 0 5px rgba(14, 165, 233, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(14, 165, 233, 0.7);
  }
  100% {
    box-shadow: 0 0 5px rgba(14, 165, 233, 0.5);
  }
}

@keyframes borderPulse {
  0% {
    border-color: rgba(139, 92, 246, 0.3);
  }
  50% {
    border-color: rgba(139, 92, 246, 0.8);
  }
  100% {
    border-color: rgba(139, 92, 246, 0.3);
  }
}

/* Mobile menu animation */
@keyframes slideDown {
  from { 
    opacity: 0;
    transform: translateY(-10px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.4s ease-out forwards;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.animate-border-pulse {
  animation: borderPulse 2s ease-in-out infinite;
}

.animate-slide-down {
  animation: slideDown 0.3s ease-out forwards;
}

/* Animation delays for staggered effects */
.delay-100 {
  animation-delay: 100ms;
}

.delay-200 {
  animation-delay: 200ms;
}

.delay-300 {
  animation-delay: 300ms;
}

.delay-400 {
  animation-delay: 400ms;
}

.delay-500 {
  animation-delay: 500ms;
}

/* Bottom Navigation Styling */
.bottom-nav-item {
  @apply flex flex-col items-center justify-center text-xs font-medium text-muted-foreground 
         transition-colors duration-200 px-3 py-2 rounded-md;
}

.bottom-nav-item.active {
  @apply text-cyber-primary bg-cyber-primary/10;
}

.bottom-nav-item:not(.active):hover {
  @apply text-foreground bg-accent/50;
}

/* Mobile Menu Animation */
.mobile-menu-enter {
  @apply animate-slide-down;
}

.mobile-menu-item {
  @apply opacity-0;
  animation: fadeIn 0.3s ease-out forwards;
}

.mobile-menu-item:nth-child(1) { animation-delay: 50ms; }
.mobile-menu-item:nth-child(2) { animation-delay: 100ms; }
.mobile-menu-item:nth-child(3) { animation-delay: 150ms; }
.mobile-menu-item:nth-child(4) { animation-delay: 200ms; }
.mobile-menu-item:nth-child(5) { animation-delay: 250ms; }

/* Custom animation for cyber theme */
.cyber-glow:hover {
  box-shadow: 0 0 15px theme('colors.blue.400' / 30%);
  transition: box-shadow 0.3s ease-in-out;
}

.cyber-grid-bg {
  background-image: 
    linear-gradient(to right, rgba(59, 130, 246, 0.05) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(59, 130, 246, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Quiz option styling */
.quiz-option {
  @apply relative p-4 rounded-lg border bg-card w-full text-left transition-all duration-300
         hover:bg-cyber-light hover:border-cyber-primary/50 hover:shadow-md;
}

.quiz-option.selected {
  @apply border-cyber-primary bg-cyber-light/50;
}

.quiz-option.correct {
  @apply border-cyber-success bg-cyber-success/10;
}

.quiz-option.incorrect {
  @apply border-cyber-error bg-cyber-error/10;
}

.progress-bar {
  @apply h-2 rounded-full bg-cyber-primary/20 overflow-hidden;
}

.progress-bar-fill {
  @apply h-full bg-cyber-primary transition-all duration-300;
}

/* Logo animation */
.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}
