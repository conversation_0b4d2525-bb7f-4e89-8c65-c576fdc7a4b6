import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';
import { supabaseConfig } from '@/config';

// Use configuration from the config file
const { url: supabaseUrl, anonKey: supabase<PERSON>nonKey } = supabaseConfig;

// Validate that environment variables are set
if (!supabaseUrl) {
  console.error(
    'Missing Supabase URL. Check your environment variables: VITE_SUPABASE_URL is not set.'
  );
  // Throw a more descriptive error
  throw new Error('Missing Supabase URL. Please set VITE_SUPABASE_URL in your environment variables.');
}

if (!supabaseAnonKey) {
  console.error(
    'Missing Supabase Anon Key. Check your environment variables: VITE_SUPABASE_ANON_KEY is not set.'
  );
  // Throw a more descriptive error
  throw new Error('Missing Supabase Anon Key. Please set VITE_SUPABASE_ANON_KEY in your environment variables.');
}

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

// Create Supabase client with proper session persistence configuration
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true, // Enable session persistence
    storageKey: 'secquiz-auth', // Custom storage key for better identification
    autoRefreshToken: true, // Automatically refresh the token
    detectSessionInUrl: true, // Detect session in URL for OAuth and magic link flows
  },
});