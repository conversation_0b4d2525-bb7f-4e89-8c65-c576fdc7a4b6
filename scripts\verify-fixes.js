import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables from the .env file in the parent directory
const envPath = join(__dirname, '..', '.env');
if (fs.existsSync(envPath)) {
  dotenv.config({ path: envPath });
} else {
  console.error('.env file not found at:', envPath);
  process.exit(1);
}

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Supabase credentials not found in environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function verifyFixes() {
  try {
    console.log('Verifying fixes for learning materials...');

    // Get the specific materials we're interested in
    const { data, error } = await supabase
      .from('learning_materials')
      .select('*')
      .in('id', ['d19b40dc-6b1b-432a-855e-641f36480af9', 'f4b448b3-94f1-465c-858b-364ff2828337']);

    if (error) {
      console.error('Error querying learning materials:', error);
      return;
    }

    // Check if the content has been fixed
    for (const material of data) {
      console.log(`\nVerifying material: ${material.title}`);

      // Check if content exists
      if (!material.content) {
        console.log('No content found for this material');
        continue;
      }

      // Check if the content still contains HTML document structure
      const hasDoctype = material.content.includes('<!DOCTYPE html>');
      const hasHtmlTag = material.content.includes('<html');
      const hasHeadTag = material.content.includes('<head>');
      const hasBodyTag = material.content.includes('<body');

      console.log('Content contains DOCTYPE:', hasDoctype);
      console.log('Content contains HTML tag:', hasHtmlTag);
      console.log('Content contains HEAD tag:', hasHeadTag);
      console.log('Content contains BODY tag:', hasBodyTag);

      if (!hasDoctype && !hasHtmlTag && !hasHeadTag && !hasBodyTag) {
        console.log('✅ Material content has been fixed successfully');
      } else {
        console.log('❌ Material content still contains HTML document structure');
      }

      // Check if content contains the placeholder text
      const hasPlaceholder = material.content.includes('This premium content is currently being updated');
      console.log('Contains placeholder text:', hasPlaceholder);

      // Check content length
      console.log('Content length:', material.content.length);
      console.log('Content preview:', material.content.substring(0, 100) + '...');
    }

    console.log('\nVerification complete');

  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the function
verifyFixes();
