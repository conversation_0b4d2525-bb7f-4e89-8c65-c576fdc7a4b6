import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import QuizCard from '@/components/QuizCard';

// Mock framer-motion to avoid animation issues in tests
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
}));

// Mock react-router-dom to avoid router context issues
vi.mock('react-router-dom', () => ({
  Link: ({ to, children, className }: any) => (
    <a href={to} className={className}>{children}</a>
  ),
}));

describe('QuizCard component', () => {
  const defaultProps = {
    id: 'quiz-1',
    title: 'CISSP Fundamentals',
    description: 'Basic cybersecurity principles and concepts',
    questionCount: 10,
    points: 100,
    difficulty: 'easy' as const,
  };

  it('renders correctly with default props', () => {
    render(<QuizCard {...defaultProps} />);

    expect(screen.getByText('CISSP Fundamentals')).toBeInTheDocument();
    expect(screen.getByText('Basic cybersecurity principles and concepts')).toBeInTheDocument();
    expect(screen.getByText('10 questions')).toBeInTheDocument();
    expect(screen.getByText('+100 points')).toBeInTheDocument();
    expect(screen.getByText('easy')).toBeInTheDocument();
    expect(screen.getByRole('link')).toHaveAttribute('href', '/quiz/quiz-1');
  });

  it('displays premium badge when isPremium is true', () => {
    render(<QuizCard {...defaultProps} isPremium={true} />);

    expect(screen.getByText('PRO')).toBeInTheDocument();
  });

  it('does not display premium badge when isPremium is false', () => {
    render(<QuizCard {...defaultProps} isPremium={false} />);

    expect(screen.queryByText('PRO')).not.toBeInTheDocument();
  });

  it('applies correct difficulty color class', () => {
    render(<QuizCard {...defaultProps} difficulty="medium" />);

    const difficultyBadge = screen.getByText('medium');
    expect(difficultyBadge).toHaveClass('bg-blue-100');
    expect(difficultyBadge).toHaveClass('text-blue-700');
  });

  it('applies correct difficulty color class for hard difficulty', () => {
    render(<QuizCard {...defaultProps} difficulty="hard" />);

    const difficultyBadge = screen.getByText('hard');
    expect(difficultyBadge).toHaveClass('bg-purple-100');
    expect(difficultyBadge).toHaveClass('text-purple-700');
  });

  it('uses custom background color when provided', () => {
    render(<QuizCard {...defaultProps} bgColor="bg-red-500" />);

    // The first div should have the custom background color
    const cardHeader = document.querySelector('.bg-red-500');
    expect(cardHeader).toBeInTheDocument();
  });

  it('renders custom icon when provided', () => {
    const customIcon = <div data-testid="custom-icon">Custom Icon</div>;
    render(<QuizCard {...defaultProps} icon={customIcon} />);

    expect(screen.getByTestId('custom-icon')).toBeInTheDocument();
  });
});
