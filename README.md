
<div align="center">
  <img src="public/secquiz-logo.svg" alt="SecQuiz Logo" width="200">
  <h1>SecQuiz</h1>
  <p><strong>Securing Knowledge, Empowering Defense</strong></p>
  <p>A modern cybersecurity education platform with comprehensive learning materials and interactive quizzes</p>
  <p><em>Learn, Practice, Master: The Complete Cybersecurity Learning Experience</em></p>

  <p>
    <a href="https://secquiz.vercel.app">View Demo</a>
    ·
    <a href="https://github.com/codeklin/quiz-guard-academy/issues">Report Bug</a>
    ·
    <a href="https://github.com/codeklin/quiz-guard-academy/issues">Request Feature</a>
  </p>
</div>

<div align="center">
  <img src="public/screenshots/home.png" alt="SecQuiz Homepage" width="80%">
</div>

## 📋 Table of Contents

- [Overview](#overview)
- [✨ Features](#-features)
- [📚 Learning Materials](#-learning-materials)
- [🛠️ Technology Stack](#️-technology-stack)
- [🚀 Getting Started](#-getting-started)
- [📱 Mobile Experience](#-mobile-experience)
- [💰 Subscription Plans](#-subscription-plans)
- [🔒 Security Features](#-security-features)
- [🧪 Testing](#-testing)
- [🤝 Contributing](#-contributing)
- [📄 License](#-license)
- [📞 Contact](#-contact)

## Overview

SecQuiz is a comprehensive cybersecurity education platform designed for tech professionals and enthusiasts. What sets SecQuiz apart is our unique **Learn-Then-Quiz** approach: we provide in-depth learning materials for each topic before testing your knowledge with interactive quizzes. This dual approach ensures you truly understand concepts before being tested on them, creating a more effective and complete learning experience. With a modern, mobile-first interface, SecQuiz makes mastering cybersecurity concepts both accessible and enjoyable.

<div align="center">
  <img src="public/screenshots/quiz.png" alt="SecQuiz Quiz Interface" width="80%">
</div>

## ✨ Features

### 📚 Comprehensive Learning System
- **In-Depth Learning Materials**: Study detailed content before taking quizzes to build a solid foundation
- **Topic-Based Learning Path**: Structured content covering network security, cryptography, and CISSP domains
- **Progressive Difficulty**: Content and questions adapt to your skill level from beginner to expert
- **Detailed Explanations**: Learn from mistakes with comprehensive answer explanations
- **Integrated Learning Experience**: Seamless transition between learning materials and practice quizzes

## 📚 Learning Materials

SecQuiz's newest feature is our comprehensive learning materials section, designed to help you master cybersecurity concepts before testing your knowledge. This feature represents our commitment to providing a complete educational experience rather than just assessment.

### Key Benefits

- **Learn Before You Quiz**: Study comprehensive materials on each topic before testing your knowledge
- **Topic-Specific Content**: Each quiz topic has dedicated learning materials that cover all testable concepts
- **Premium Deep Dives**: Access advanced learning materials with a premium subscription
- **Seamless Integration**: Direct links between related learning materials and quizzes
- **Mobile-Friendly Format**: Study on any device with our responsive design

Our learning materials are written by cybersecurity professionals and educators, ensuring you receive accurate, up-to-date, and relevant information. Whether you're preparing for certification exams or building foundational knowledge, our learning materials provide the perfect starting point for your cybersecurity education journey.

<div align="center">
  <img src="public/screenshots/learning.png" alt="SecQuiz Learning Materials" width="80%">
</div>

### 🎮 Gamification
- **Achievement System**: Unlock badges and achievements as you progress
- **Progress Tracking**: Visual representation of your learning journey
- **Performance Analytics**: Track your scores and improvement over time
- **Personalized Dashboard**: Monitor your learning progress at a glance

### 🔐 Premium Content
- **Exclusive Topics**: Access specialized cybersecurity domains with subscription
- **Advanced Questions**: Challenge yourself with expert-level content
- **Certification Preparation**: Materials specifically designed for certification exams
- **Community Access**: Join a network of cybersecurity professionals

### 💻 Technical Features
- **Responsive Design**: Seamless experience across all devices
- **Dark Theme**: Eye-friendly interface for extended study sessions
- **Real-time Updates**: Instant feedback on answers
- **Progressive Web App**: Install on your device for offline access

## 🛠️ Technology Stack

### Frontend
- **React 18** with TypeScript
- **Tailwind CSS** for styling with shadcn/ui components
- **Framer Motion** for smooth animations
- **React Router** for navigation
- **React Query** for data fetching
- **Sonner** for toast notifications

### Backend & Services
- **Supabase** for authentication and database
- **Supabase Storage** for assets
- **Paystack** for payment processing
- **Brevo SMTP** for transactional emails

### Development & Testing
- **Vite** for fast development and building
- **Vitest** for unit testing
- **ESLint** with security plugins for code quality
- **Snyk** for vulnerability scanning
- **Husky** for pre-commit hooks

## 🚀 Getting Started

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn
- Supabase account and project
- Paystack account (for payment processing)
- Brevo account (for email services)

### Installation

```bash
# Clone the repository
git clone https://github.com/codeklin/quiz-guard-academy.git

# Navigate to the project directory
cd quiz-guard-academy

# Install dependencies
npm install

# Set up environment variables
# Create a .env.development file with your Supabase credentials

# Start the development server
npm run dev
```

### Database Setup

1. Create a new Supabase project
2. Run the database migration script to set up the schema:
   - Navigate to the SQL Editor in your Supabase dashboard
   - Run the migration script: `npm run db:migrate`
   - Or manually execute the SQL in `fix-database-tables.sql`

3. Set up Row Level Security for tables:

```sql
ALTER TABLE public.admin_users ENABLE ROW LEVEL SECURITY;
```

4. Import initial quiz data:
   - Update your environment variables in `.env` with your Supabase credentials
   - Run the update scripts:

```bash
npm run update-topics
npm run update-access
```

### Admin Setup

1. Create a user account through the application
2. Make the user an admin by running the following SQL in the Supabase SQL Editor:

```sql
INSERT INTO admin_users (user_id)
VALUES ('your-user-id-here');
```

Replace `your-user-id-here` with the actual user ID from the `auth.users` table.

## 📱 Mobile Experience

SecQuiz is built with a mobile-first approach, ensuring an optimal experience on smartphones and tablets:

- **Responsive Navigation**: Intuitive hamburger menu for mobile devices
- **Touch-Friendly Interface**: Large touch targets and swipe gestures
- **Optimized Performance**: Fast loading times on mobile networks
- **Progressive Web App (PWA)**: Install on your home screen for app-like experience
- **Offline Support**: Continue learning without internet connection

<div align="center">
  <img src="public/screenshots/mobile.png" alt="SecQuiz Mobile Experience" width="50%">
</div>

## 💰 Subscription Plans

SecQuiz offers flexible subscription options to meet different learning needs:

### Basic Plan - ₦998/week
- Access to 4 quiz domains
- 400 questions weekly
- Basic learning materials
- Basic progress tracking

### Pro Plan - ₦1,979/week
- Access to all quiz domains
- Unlimited questions
- Complete learning materials library
- Free international certification
- Cancel anytime

### Elite Plan - ₦5,000 one-time
- Everything in Pro
- Advanced learning materials with expert insights
- Community access
- 24/7 Priority mentorship & support
- CV design and job readiness assistance
- Daily cybersecurity related jobs
- Referrals for job openings

<div align="center">
  <img src="public/screenshots/pricing.png" alt="SecQuiz Pricing Plans" width="80%">
</div>

## 🔒 Security Features

SecQuiz takes security seriously with several measures to protect user data:

- **Automated Security Scanning**: Regular vulnerability scanning using npm audit and Snyk
- **Code Quality Checks**: ESLint with security plugins to catch potential security issues
- **Pre-commit Hooks**: Security checks run before each commit
- **Content Security Policy**: Implemented to prevent XSS attacks
- **Secure Authentication**: Email-based authentication with Supabase Auth
- **Secure Payment Processing**: PCI-compliant payment processing with Paystack
- **Data Encryption**: All sensitive data is encrypted at rest and in transit

### Running Security Checks

```bash
# Run basic security checks
npm run security:check

# Run comprehensive security checks (requires Snyk authentication)
npm run security:check:full

# Fix automatically fixable issues
npm run security:fix
```

## 🧪 Testing

SecQuiz includes comprehensive testing to ensure reliability:

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Generate test coverage report
npm run test:coverage
```

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the Project
2. Create your Feature Branch (`git checkout -b feature/AmazingFeature`)
3. Commit your Changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the Branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

Please read our [Contributing Guidelines](CONTRIBUTING.md) for more details.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Contact

For any questions or feedback, please reach out to:

- Email: [<EMAIL>](mailto:<EMAIL>)
- WhatsApp: Chat with us on WhatsApp for quick support

---

<div align="center">
  <p>Made with ❤️ for the Nigerian cybersecurity community</p>
  <p>
    <a href="https://supabase.io/">Supabase</a> ·
    <a href="https://paystack.com/">Paystack</a> ·
    <a href="https://vercel.com/">Vercel</a> ·
    <a href="https://ui.shadcn.com/">shadcn/ui</a>
  </p>
</div>
