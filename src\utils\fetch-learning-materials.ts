import { supabase } from "@/integrations/supabase/client";

export interface LearningMaterial {
  id: string;
  topic_id: string;
  title: string;
  content: string;
  summary: string | null;
  is_premium: boolean;
  order_index: number;
  created_at: string;
  updated_at: string;
  topic_title?: string;
  topic_description?: string;
  topic_icon?: string;
}

export interface LearningMaterialForUI {
  id: string;
  topicId: string;
  title: string;
  summary: string;
  isPremium: boolean;
  topicTitle?: string;
  topicIcon?: string;
}

/**
 * Fetches all learning materials from the database
 */
export async function fetchAllLearningMaterials(): Promise<LearningMaterial[]> {
  try {
    // Fetch learning materials from Supabase with topic information
    const { data, error } = await supabase
      .from("learning_materials")
      .select(`
        *,
        topics:topic_id (
          title,
          description,
          icon
        )
      `)
      .order("order_index");

    if (error) throw error;

    if (!data || data.length === 0) {
      console.log('No learning materials found in database');
      return [];
    }

    // Transform the data to include topic information
    const materials = data.map(material => ({
      ...material,
      topic_title: material.topics?.title,
      topic_description: material.topics?.description,
      topic_icon: material.topics?.icon
    }));

    return materials;
  } catch (error) {
    console.error("Error fetching learning materials:", error);
    return [];
  }
}

/**
 * Fetches learning materials for a specific topic
 */
export async function fetchLearningMaterialsByTopic(topicId: string): Promise<LearningMaterial[]> {
  try {
    const { data, error } = await supabase
      .from("learning_materials")
      .select(`
        *,
        topics:topic_id (
          title,
          description,
          icon
        )
      `)
      .eq("topic_id", topicId)
      .order("order_index");

    if (error) throw error;

    if (!data || data.length === 0) {
      console.log(`No learning materials found for topic ${topicId}`);
      return [];
    }

    // Transform the data to include topic information
    const materials = data.map(material => ({
      ...material,
      topic_title: material.topics?.title,
      topic_description: material.topics?.description,
      topic_icon: material.topics?.icon
    }));

    return materials;
  } catch (error) {
    console.error(`Error fetching learning materials for topic ${topicId}:`, error);
    return [];
  }
}

/**
 * Fetches a single learning material by ID
 */
export async function fetchLearningMaterialById(id: string): Promise<LearningMaterial | null> {
  try {
    const { data, error } = await supabase
      .from("learning_materials")
      .select(`
        *,
        topics:topic_id (
          title,
          description,
          icon
        )
      `)
      .eq("id", id)
      .single();

    if (error) throw error;

    if (!data) {
      console.log(`Learning material ${id} not found`);
      return null;
    }

    // Transform the data to include topic information
    return {
      ...data,
      topic_title: data.topics?.title,
      topic_description: data.topics?.description,
      topic_icon: data.topics?.icon
    };
  } catch (error) {
    console.error(`Error fetching learning material ${id}:`, error);
    return null;
  }
}

/**
 * Converts database learning materials to the format expected by the UI components
 */
export function convertToUIFormat(materials: LearningMaterial[]): LearningMaterialForUI[] {
  return materials.map(material => ({
    id: material.id,
    topicId: material.topic_id,
    title: material.title,
    summary: material.summary || "",
    isPremium: material.is_premium,
    topicTitle: material.topic_title,
    topicIcon: material.topic_icon
  }));
}
