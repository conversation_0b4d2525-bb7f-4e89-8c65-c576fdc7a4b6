// Script to preview email templates
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import open from 'open';

// Set up __dirname equivalent for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Create preview directory if it doesn't exist
const previewDir = path.resolve(__dirname, '../email-previews');
if (!fs.existsSync(previewDir)) {
  fs.mkdirSync(previewDir);
}

// Extract email templates from the service file
function extractEmailTemplates() {
  const serviceFilePath = path.resolve(__dirname, '../src/services/brevo-direct-service.ts');
  const serviceFileContent = fs.readFileSync(serviceFilePath, 'utf8');
  
  // Extract verification email template
  const verificationEmailRegex = /htmlContent: `([\s\S]*?)`,\s*replyTo:/g;
  const verificationMatches = [...serviceFileContent.matchAll(verificationEmailRegex)];
  
  // Extract password reset email template
  const passwordResetEmailRegex = /htmlContent: `([\s\S]*?)`,\s*replyTo:/g;
  const passwordResetMatches = [...serviceFileContent.matchAll(passwordResetEmailRegex)];
  
  return {
    verification: verificationMatches[0] ? verificationMatches[0][1] : null,
    passwordReset: passwordResetMatches[1] ? passwordResetMatches[1][1] : null
  };
}

// Save templates to preview files
function saveTemplatePreviews(templates) {
  // Replace template variables with sample values
  const sampleVerificationUrl = 'https://secquiz.app/verify?token=sample-verification-token';
  const sampleResetUrl = 'https://secquiz.app/reset-password?token=sample-reset-token';
  
  // Save verification email preview
  if (templates.verification) {
    const verificationHtml = templates.verification
      .replace('${verificationUrl}', sampleVerificationUrl)
      .replace('${new Date().getFullYear()}', new Date().getFullYear().toString());
    
    fs.writeFileSync(
      path.resolve(previewDir, 'verification-email.html'),
      verificationHtml
    );
  }
  
  // Save password reset email preview
  if (templates.passwordReset) {
    const passwordResetHtml = templates.passwordReset
      .replace('${resetUrl}', sampleResetUrl)
      .replace('${new Date().getFullYear()}', new Date().getFullYear().toString());
    
    fs.writeFileSync(
      path.resolve(previewDir, 'password-reset-email.html'),
      passwordResetHtml
    );
  }
}

// Open previews in browser
async function openPreviews() {
  const verificationPreviewPath = path.resolve(previewDir, 'verification-email.html');
  const passwordResetPreviewPath = path.resolve(previewDir, 'password-reset-email.html');
  
  if (fs.existsSync(verificationPreviewPath)) {
    console.log('Opening verification email preview...');
    await open(verificationPreviewPath);
  }
  
  if (fs.existsSync(passwordResetPreviewPath)) {
    console.log('Opening password reset email preview...');
    await open(passwordResetPreviewPath);
  }
}

// Main function
async function main() {
  console.log('Extracting email templates...');
  const templates = extractEmailTemplates();
  
  if (!templates.verification && !templates.passwordReset) {
    console.error('No email templates found in the service file.');
    process.exit(1);
  }
  
  console.log('Saving template previews...');
  saveTemplatePreviews(templates);
  
  console.log('Opening previews in browser...');
  await openPreviews();
  
  console.log('Done!');
}

main();
