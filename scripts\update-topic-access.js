// <PERSON>ript to update topic access settings in the database
// This script ensures topics have the correct access settings

import { config } from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Initialize dotenv
config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Please check your .env file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Topic titles that should be accessible to everyone (even unauthenticated users)
const PUBLIC_TOPICS = ["CISSP Fundamentals"];

// Topic titles that should be accessible only to authenticated users (but not premium)
const AUTHENTICATED_TOPICS = ["Cybersecurity Foundation - Easy", "CIA Triad: Confidentiality, Integrity, and Availability", "ISC2 Certification"];

// Main function to run the update
async function main() {
  try {
    console.log('Starting update of topic access settings...');

    // 1. Get all topics from the database
    const { data: topics, error: topicsError } = await supabase
      .from('topics')
      .select('*');

    if (topicsError) {
      throw new Error(`Error fetching topics: ${topicsError.message}`);
    }

    console.log(`Found ${topics.length} topics in the database.`);

    // 2. Update each topic based on our access rules
    for (const topic of topics) {
      let difficulty = topic.difficulty;
      let isPremium = true; // Default to premium

      // Set difficulty based on our access rules
      if (PUBLIC_TOPICS.includes(topic.title)) {
        difficulty = 'easy';
        isPremium = false;
        console.log(`Setting "${topic.title}" as public (easy difficulty, not premium)`);
      } else if (AUTHENTICATED_TOPICS.includes(topic.title)) {
        difficulty = 'medium';
        isPremium = false;
        console.log(`Setting "${topic.title}" as authenticated-only (medium difficulty, not premium)`);
      } else {
        difficulty = 'hard';
        isPremium = true;
        console.log(`Setting "${topic.title}" as premium (hard difficulty, premium)`);
      }

      // Update the topic in the database
      const { error: updateError } = await supabase
        .from('topics')
        .update({
          difficulty: difficulty
        })
        .eq('id', topic.id);

      if (updateError) {
        console.error(`Error updating topic "${topic.title}": ${updateError.message}`);
      }
    }

    console.log('\nTopic access settings updated successfully!');
    console.log('\nAccess summary:');
    console.log('- Public topics (accessible to everyone):', PUBLIC_TOPICS.join(', '));
    console.log('- Authenticated topics (accessible to logged-in users):', AUTHENTICATED_TOPICS.join(', '));
    console.log('- Premium topics (accessible to premium users):', topics
      .filter(t => !PUBLIC_TOPICS.includes(t.title) && !AUTHENTICATED_TOPICS.includes(t.title))
      .map(t => t.title)
      .join(', '));

  } catch (error) {
    console.error('Error during update:', error);
    process.exit(1);
  }
}

// Run the main function
main().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
