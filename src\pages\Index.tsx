
import { useState, useEffect } from "react";
import BottomNavigation from "@/components/BottomNavigation";
import HeroSection from "@/components/HeroSection";
import Navbar from "@/components/Navbar";
import QuizList from "@/components/QuizList";
import LearningList from "@/components/LearningList";
import PricingSection from "@/components/PricingSection";
import { useAuth } from "@/hooks/use-auth";
import { useHashScroll } from "@/hooks/use-hash-scroll";
import { Loader2 } from "lucide-react";
import { fetchHomePageTopics } from "@/utils/fetch-topics";
import { fetchAllLearningMaterials, convertToUIFormat, LearningMaterialForUI } from "@/utils/fetch-learning-materials";
import { Link } from "react-router-dom";

const Index = () => {
  const { isLoading: authLoading } = useAuth();
  const [popularTopics, setPopularTopics] = useState([]);
  const [featuredTopics, setFeaturedTopics] = useState([]);
  const [learningMaterials, setLearningMaterials] = useState<LearningMaterialForUI[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Use the hash scroll hook to handle scrolling to the pricing section
  useHashScroll();

  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);

        // Load topics
        const { popularTopics: popular, featuredTopics: featured } = await fetchHomePageTopics();
        setPopularTopics(popular);
        setFeaturedTopics(featured);

        // Load learning materials
        const allMaterials = await fetchAllLearningMaterials();
        const uiMaterials = convertToUIFormat(allMaterials);

        // Get the 3 most recent learning materials
        const recentMaterials = [...uiMaterials].slice(0, 3);
        setLearningMaterials(recentMaterials);
      } catch (error) {
        console.error("Error loading data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  if (authLoading || isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-indigo-900">
        <Loader2 className="h-8 w-8 text-white animate-spin" />
        <span className="ml-2 text-white">Loading topics...</span>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-indigo-900">
      <Navbar />
      <main className="flex-1">
        <HeroSection featuredTopics={featuredTopics} />

        <div className="container px-4 mx-auto">
          {learningMaterials.length > 0 && (
            <LearningList title="Learning Materials" materials={learningMaterials} showViewAll={true} />
          )}

          {popularTopics.length > 0 && (
            <QuizList title="Popular Quizzes" quizzes={popularTopics} />
          )}
        </div>

        {/* Pricing Section */}
        <PricingSection />

        {/* Modern Bottom Section */}
        <div className="mt-24 mb-12 bg-[#F8F9FA] py-20 rounded-3xl mx-4">
          <div className="container mx-auto px-4">
            <div className="flex flex-col md:flex-row items-center gap-12">
              {/* Left Side - Text Content */}
              <div className="w-full md:w-1/2">
                <div className="relative inline-block mb-4">
                  <h2 className="text-3xl md:text-4xl font-bold text-[#111827] relative z-10">
                    Master Cybersecurity, <br />
                    <span className="text-[#4F46E5]">One Quiz At A Time!</span>
                  </h2>
                  <div className="absolute -bottom-2 left-0 h-3 w-40 bg-[#FB923C]/30 rounded-full -z-0"></div>
                </div>

                <p className="text-gray-600 text-lg mb-8 max-w-lg">
                  Explore topics, test your skills, and become a cybersecurity pro through fun and interactive quizzes.
                </p>

                <Link to="/quizzes" className="inline-flex px-8 py-4 bg-[#111827] text-white font-medium rounded-full hover:bg-[#111827]/90 transition-all duration-300 shadow-lg hover:shadow-xl hover:-translate-y-1 items-center gap-2">
                  Start Learning Now
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </Link>

                <div className="flex items-center gap-4 mt-12">
                  <img src="/secquiz-logo.svg" alt="SecQuiz Logo" className="h-8 w-8" />
                  <div>
                    <p className="text-[#111827] font-bold">SecQuiz</p>
                    <p className="text-gray-500 text-sm">© {new Date().getFullYear()} All rights reserved</p>
                  </div>
                </div>
              </div>

              {/* Right Side - Phone Mockup */}
              <div className="w-full md:w-1/2 flex justify-center">
                <div className="relative">
                  {/* Phone frame */}
                  <div className="relative z-10 bg-[#111827] rounded-[32px] p-3 shadow-2xl rotate-3 transform hover:rotate-0 transition-all duration-500">
                    <div className="bg-white rounded-[24px] overflow-hidden">
                      <div className="h-8 w-full bg-[#111827] flex justify-center items-center">
                        <div className="h-2 w-24 bg-gray-600 rounded-full"></div>
                      </div>

                      <div className="p-4">
                        <img
                          src="/phone-screen-with-abstract-marble-aesthetic.jpg"
                          alt="SecQuiz Mobile App"
                          className="rounded-lg h-[400px] w-auto object-cover"
                        />

                        {/* Quiz Categories */}
                        <div className="mt-4 grid grid-cols-2 gap-3">
                          <div className="bg-[#4F46E5]/10 p-3 rounded-xl">
                            <div className="bg-[#4F46E5] h-8 w-8 rounded-full flex items-center justify-center mb-2">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                              </svg>
                            </div>
                            <p className="text-xs font-medium">Network Security</p>
                          </div>

                          <div className="bg-[#FB923C]/10 p-3 rounded-xl">
                            <div className="bg-[#FB923C] h-8 w-8 rounded-full flex items-center justify-center mb-2">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                                <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                              </svg>
                            </div>
                            <p className="text-xs font-medium">Threat Intelligence</p>
                          </div>

                          <div className="bg-green-100 p-3 rounded-xl">
                            <div className="bg-green-500 h-8 w-8 rounded-full flex items-center justify-center mb-2">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                              </svg>
                            </div>
                            <p className="text-xs font-medium">Ethical Hacking</p>
                          </div>

                          <div className="bg-purple-100 p-3 rounded-xl">
                            <div className="bg-purple-500 h-8 w-8 rounded-full flex items-center justify-center mb-2">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M5.5 16a3.5 3.5 0 01-.369-6.98 4 4 0 117.753-1.977A4.5 4.5 0 1113.5 16h-8z" />
                              </svg>
                            </div>
                            <p className="text-xs font-medium">Cloud Security</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Decorative elements */}
                  <div className="absolute -bottom-6 -left-6 h-24 w-24 bg-[#4F46E5]/10 rounded-full -z-10"></div>
                  <div className="absolute -top-4 -right-4 h-16 w-16 bg-[#FB923C]/20 rounded-full -z-10"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      <BottomNavigation />
    </div>
  );
};

export default Index;
