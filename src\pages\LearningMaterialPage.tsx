import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate, <PERSON> } from "react-router-dom";
import { Loader2, <PERSON>Left, BookOpen, Lock, AlertCircle } from "lucide-react";
import Navbar from "@/components/Navbar";
import BottomNavigation from "@/components/BottomNavigation";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { fetchLearningMaterialById, LearningMaterial } from "@/utils/fetch-learning-materials";
import { canAccessTopicSync, canAccessTopic, PUBLIC_TOPICS } from "@/utils/topic-access";
import { motion } from "framer-motion";

// Helper function to extract body content from full HTML documents
const extractBodyContent = (htmlContent: string): string => {
  try {
    if (!htmlContent) return '';

    // If it's a full HTML document, extract just the body content
    if (htmlContent.includes('<!DOCTYPE html>') ||
        htmlContent.includes('<html') ||
        htmlContent.includes('<head>')) {

      // Try to extract content between body tags first
      const bodyMatch = htmlContent.match(/<body[^>]*>([\s\S]*)<\/body>/i);
      if (bodyMatch && bodyMatch[1]) {
        return bodyMatch[1].trim();
      }

      // If no body tags found, try to remove DOCTYPE, html, head tags
      let cleanedContent = htmlContent;

      // Remove DOCTYPE declaration
      cleanedContent = cleanedContent.replace(/<!DOCTYPE[^>]*>/i, '');

      // Remove html open and close tags
      cleanedContent = cleanedContent.replace(/<html[^>]*>/i, '').replace(/<\/html>/i, '');

      // Remove head section completely
      cleanedContent = cleanedContent.replace(/<head>[\s\S]*?<\/head>/i, '');

      // Remove any remaining body tags
      cleanedContent = cleanedContent.replace(/<body[^>]*>/i, '').replace(/<\/body>/i, '');

      return cleanedContent.trim();
    }

    return htmlContent;
  } catch (e) {
    console.error("Error extracting body content:", e);
    return htmlContent || '';
  }
};

const LearningMaterialPage = () => {
  const { materialId } = useParams<{ materialId: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user } = useAuth();
  const [material, setMaterial] = useState<LearningMaterial | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [canAccess, setCanAccess] = useState(false);

  useEffect(() => {
    const loadMaterial = async () => {
      try {
        setIsLoading(true);

        if (!materialId) {
          console.error("No material ID provided");
          navigate("/learn");
          return;
        }

        const materialData = await fetchLearningMaterialById(materialId);

        if (!materialData) {
          toast({
            title: "Material Not Found",
            description: "The requested learning material could not be found.",
            variant: "destructive",
          });
          navigate("/learn");
          return;
        }

        setMaterial(materialData);

        // Always allow access if is_premium is false
        if (!materialData.is_premium) {
          setCanAccess(true);
          return;
        }
        // If the material is public (accessible to everyone), allow access
        if (materialData.topic_title && PUBLIC_TOPICS.includes(materialData.topic_title)) {
          setCanAccess(true);
          return;
        }

        // For premium content, do a quick synchronous check for immediate UI feedback
        let userCanAccess = materialData.topic_title
          ? canAccessTopicSync(materialData.topic_title, materialData.topic_id, user)
          : !materialData.is_premium; // Fallback if no topic title

        setCanAccess(userCanAccess);

        // Then do a more thorough async check that will update the UI if needed
        if (!userCanAccess && materialData.topic_title) {
          // Only do the async check if the sync check failed and we have a topic title
          try {
            const asyncCanAccess = await canAccessTopic(materialData.topic_title, materialData.topic_id, user);

            if (asyncCanAccess) {
              // Update the UI if the async check says the user can access
              setCanAccess(true);
              userCanAccess = true;
            }
          } catch (error) {
            console.error("Error checking async access:", error);
            // Keep the result from the sync check
          }
        }

        // If user cannot access this material, show a toast but don't redirect
        if (!userCanAccess) {
          toast({
            title: "Premium Content",
            description: "This learning material requires a premium subscription",
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error("Error loading learning material:", error);
        toast({
          title: "Error",
          description: "Failed to load learning material",
          variant: "destructive",
        });
        navigate("/learn");
      } finally {
        setIsLoading(false);
      }
    };

    loadMaterial();
  }, [materialId, navigate, toast, user]);

  if (isLoading) {
    return (
      <div className="flex flex-col min-h-screen">
        <Navbar />
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p>Loading learning material...</p>
          </div>
        </div>
        <BottomNavigation />
      </div>
    );
  }

  if (!material) {
    return (
      <div className="flex flex-col min-h-screen">
        <Navbar />
        <div className="flex-1 container py-8 px-4 flex flex-col items-center justify-center">
          <div className="w-20 h-20 rounded-full bg-cyber-warning/20 flex items-center justify-center mb-6">
            <AlertCircle className="w-10 h-10 text-cyber-warning" />
          </div>

          <h1 className="text-2xl font-bold mb-2">Material Not Found</h1>
          <p className="text-center text-muted-foreground mb-8 max-w-md">
            Sorry, we couldn't find the learning material you're looking for.
          </p>

          <Button asChild className="bg-cyber-primary hover:bg-cyber-primary/90">
            <Link to="/learn">Browse Learning Materials</Link>
          </Button>
        </div>
        <BottomNavigation />
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-indigo-900">
      {/* Consistent dark navbar styling */}
      <header className="w-full bg-indigo-900 text-white shadow-sm sticky top-0 z-20">
        <Navbar />
      </header>
      <main className="flex-1 container px-4 py-6 mx-auto">
        <div className="mb-4">
          <Button
            variant="ghost"
            size="sm"
            className="text-white font-medium hover:text-white hover:bg-indigo-800"
            onClick={() => navigate("/learn")}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Learning Materials
          </Button>
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Card className="p-6 mb-6">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h1 className="text-2xl font-bold mb-2">{material.title}</h1>
                {material.topic_title && (
                  <div className="text-sm text-muted-foreground mb-2">
                    Topic: {material.topic_title}
                  </div>
                )}
                <div className={`inline-block px-2 py-1 rounded text-xs font-medium ${
                  material.is_premium
                    ? 'bg-purple-100 text-purple-800'
                    : 'bg-blue-100 text-blue-800'
                }`}>
                  {material.is_premium ? "Premium" : "Free"}
                </div>
              </div>
            </div>

            {canAccess ? (
              <div className="prose prose-indigo max-w-none learning-material-content">
                {/* Render content properly regardless of HTML format */}
                <div
                  dangerouslySetInnerHTML={{
                    __html: material.content.includes('<!DOCTYPE html>') ||
                           material.content.includes('<html') ||
                           material.content.includes('<head>')
                      ? extractBodyContent(material.content)
                      : material.content
                  }}
                  className="font-sans"
                />
              </div>
            ) : (
              <div className="bg-indigo-50 border border-indigo-100 rounded-lg p-6 text-center">
                <Lock className="h-12 w-12 text-indigo-400 mx-auto mb-4" />
                <h3 className="text-xl font-medium text-indigo-900 mb-2">Premium Content</h3>
                <p className="text-indigo-700 mb-6">
                  This learning material is only available to premium subscribers.
                </p>
                <Button asChild className="bg-cyber-primary hover:bg-cyber-primary/90">
                  <Link to="/#pricing">View Pricing Plans</Link>
                </Button>
              </div>
            )}
          </Card>

          {/* Related quiz button */}
          <div className="flex justify-center mb-12">
            <Button asChild className="bg-cyber-primary hover:bg-cyber-primary/90">
              <Link to={`/quizzes`} state={{ fromLearningMaterial: true, topicTitle: material.topic_title }}>
                <BookOpen className="mr-2 h-4 w-4" />
                Take Quiz on This Topic
              </Link>
            </Button>
          </div>
        </motion.div>
      </main>
      <BottomNavigation />
    </div>
  );
};

export default LearningMaterialPage;
