/* Learning Content Styles - Colorful elements for better user experience */

/* Colorful callout boxes */
.info-box {
  background-color: #EEF2FF;
  border-left: 4px solid #6366F1;
  padding: 1rem;
  margin: 1rem 0;
  border-radius: 0.375rem;
}

.warning-box {
  background-color: #FEF3C7;
  border-left: 4px solid #F59E0B;
  padding: 1rem;
  margin: 1rem 0;
  border-radius: 0.375rem;
}

.success-box {
  background-color: #ECFDF5;
  border-left: 4px solid #10B981;
  padding: 1rem;
  margin: 1rem 0;
  border-radius: 0.375rem;
}

.tip-box {
  background-color: #F5F3FF;
  border-left: 4px solid #8B5CF6;
  padding: 1rem;
  margin: 1rem 0;
  border-radius: 0.375rem;
}

.example-box {
  background-color: #F3F4F6;
  border-left: 4px solid #9CA3AF;
  padding: 1rem;
  margin: 1rem 0;
  border-radius: 0.375rem;
}

/* Box titles */
.box-title {
  font-weight: 600;
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.info-box .box-title {
  color: #4F46E5;
}

.warning-box .box-title {
  color: #D97706;
}

.success-box .box-title {
  color: #059669;
}

.tip-box .box-title {
  color: #7C3AED;
}

.example-box .box-title {
  color: #4B5563;
}

/* Highlighted terms */
.key-concept {
  background-color: #DBEAFE;
  font-weight: 600;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
}

.important-term {
  background-color: #FEF3C7;
  font-weight: 600;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
}

.definition {
  background-color: #F3F4F6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-style: italic;
}

/* Section headers with color accents */
.section-header {
  border-bottom: 2px solid #3B82F6;
  padding-bottom: 0.5rem;
  margin-bottom: 1rem;
}

/* Colored lists */
.checklist {
  list-style: none;
  padding-left: 1.5rem;
}

.checklist li {
  position: relative;
  margin-bottom: 0.5rem;
}

.checklist li::before {
  content: "✓";
  color: #10B981;
  font-weight: bold;
  position: absolute;
  left: -1.5rem;
}

.steps-list {
  list-style: none;
  counter-reset: step-counter;
  padding-left: 2rem;
}

.steps-list li {
  position: relative;
  margin-bottom: 0.75rem;
  counter-increment: step-counter;
}

.steps-list li::before {
  content: counter(step-counter);
  background-color: #3B82F6;
  color: white;
  font-weight: bold;
  font-size: 0.8rem;
  border-radius: 50%;
  width: 1.5rem;
  height: 1.5rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: -2rem;
}

/* Code blocks */
.code-block {
  background-color: #1E293B;
  color: #E2E8F0;
  padding: 1rem;
  border-radius: 0.375rem;
  font-family: monospace;
  overflow-x: auto;
  margin: 1rem 0;
}

/* Tables */
.data-table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
}

.data-table th {
  background-color: #F3F4F6;
  padding: 0.75rem;
  text-align: left;
  font-weight: 600;
  border-bottom: 2px solid #E5E7EB;
}

.data-table td {
  padding: 0.75rem;
  border-bottom: 1px solid #E5E7EB;
}

.data-table tr:nth-child(even) {
  background-color: #F9FAFB;
}

/* Mobile responsiveness */
@media (max-width: 640px) {
  .info-box, .warning-box, .success-box, .tip-box, .example-box {
    padding: 0.75rem;
    margin: 0.75rem 0;
  }
  
  .steps-list {
    padding-left: 1.75rem;
  }
  
  .steps-list li::before {
    width: 1.25rem;
    height: 1.25rem;
    font-size: 0.7rem;
    left: -1.75rem;
  }
  
  .checklist {
    padding-left: 1.25rem;
  }
  
  .checklist li::before {
    left: -1.25rem;
  }
  
  .data-table th, .data-table td {
    padding: 0.5rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .info-box {
    background-color: rgba(99, 102, 241, 0.1);
  }
  
  .warning-box {
    background-color: rgba(245, 158, 11, 0.1);
  }
  
  .success-box {
    background-color: rgba(16, 185, 129, 0.1);
  }
  
  .tip-box {
    background-color: rgba(139, 92, 246, 0.1);
  }
  
  .example-box {
    background-color: rgba(156, 163, 175, 0.1);
  }
  
  .key-concept {
    background-color: rgba(59, 130, 246, 0.2);
  }
  
  .important-term {
    background-color: rgba(245, 158, 11, 0.2);
  }
  
  .definition {
    background-color: rgba(156, 163, 175, 0.2);
  }
  
  .data-table th {
    background-color: #374151;
    border-bottom-color: #4B5563;
  }
  
  .data-table td {
    border-bottom-color: #4B5563;
  }
  
  .data-table tr:nth-child(even) {
    background-color: #1F2937;
  }
}
