import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';

export function useCustomAuth() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Sign up a user with email verification via Supabase
   */
  const signUp = async (email: string, password: string, metadata?: any) => {
    setLoading(true);
    setError(null);

    try {
      // Sign up the user with Supabase (which handles email verification)
      const { data, error: signUpError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: metadata,
          emailRedirectTo: `${window.location.origin}/auth/callback`,
        },
      });

      if (signUpError) throw signUpError;

      return {
        success: true,
        message: 'Account created! Please check your email to verify your account.',
        data,
      };
    } catch (err: any) {
      setError(err.message || 'An error occurred during sign up');
      return {
        success: false,
        error: err.message || 'An error occurred during sign up',
      };
    } finally {
      setLoading(false);
    }
  };

  /**
   * Send a password reset email via Supabase
   */
  const resetPassword = async (email: string) => {
    setLoading(true);
    setError(null);

    try {
      // Request password reset from Supabase
      const { data, error: resetError } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`,
      });

      if (resetError) throw resetError;

      return {
        success: true,
        message: 'Password reset instructions sent to your email.',
        data,
      };
    } catch (err: any) {
      setError(err.message || 'An error occurred during password reset');
      return {
        success: false,
        error: err.message || 'An error occurred during password reset',
      };
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    error,
    signUp,
    resetPassword,
  };
}
