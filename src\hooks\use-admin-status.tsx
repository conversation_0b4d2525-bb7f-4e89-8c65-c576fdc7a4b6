import { useState, useEffect } from "react";
import { User } from "@supabase/supabase-js";
import { isUserAdmin } from "@/utils/auth-helpers";

/**
 * Hook to check if current user has admin privileges
 * Properly handles the async nature of the admin check
 * Returns isAdmin status and loading state
 */
export function useAdminStatus(user: User | null): { isAdmin: boolean, isLoading: boolean } {
  const [isAdmin, setIsAdmin] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    async function checkAdminStatus() {
      if (!user) {
        setIsAdmin(false);
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        const adminStatus = await isUserAdmin(user);
        setIsAdmin(adminStatus);
      } catch (error) {
        console.error("Error checking admin status:", error);
        setIsAdmin(false);
      } finally {
        setIsLoading(false);
      }
    }

    checkAdminStatus();
  }, [user]);

  return { isAdmin, isLoading };
}
