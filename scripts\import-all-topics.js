// Script to import all topics and questions from the database
// This script ensures all topics and questions are properly loaded

require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Please check your .env file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Main function to run the import
async function main() {
  try {
    console.log('Starting import of all topics and questions...');
    
    // 1. Get all topics from the database
    const { data: topics, error: topicsError } = await supabase
      .from('topics')
      .select('*');
    
    if (topicsError) {
      throw new Error(`Error fetching topics: ${topicsError.message}`);
    }
    
    console.log(`Found ${topics.length} topics in the database.`);
    
    // 2. Get all questions from the database
    const { data: questions, error: questionsError } = await supabase
      .from('questions')
      .select('*');
    
    if (questionsError) {
      throw new Error(`Error fetching questions: ${questionsError.message}`);
    }
    
    console.log(`Found ${questions.length} questions in the database.`);
    
    // 3. Group questions by topic
    const questionsByTopic = {};
    questions.forEach(question => {
      if (!question.topic_id) return;
      
      if (!questionsByTopic[question.topic_id]) {
        questionsByTopic[question.topic_id] = [];
      }
      
      questionsByTopic[question.topic_id].push(question);
    });
    
    // 4. Print summary of topics and questions
    console.log('\nSummary of topics and questions:');
    console.log('--------------------------------');
    
    topics.forEach(topic => {
      const topicQuestions = questionsByTopic[topic.id] || [];
      console.log(`Topic: ${topic.title}`);
      console.log(`  ID: ${topic.id}`);
      console.log(`  Description: ${topic.description || 'N/A'}`);
      console.log(`  Difficulty: ${topic.difficulty || 'N/A'}`);
      console.log(`  Questions: ${topicQuestions.length}`);
      console.log('');
    });
    
    // 5. Check for topics with no questions
    const topicsWithNoQuestions = topics.filter(topic => 
      !questionsByTopic[topic.id] || questionsByTopic[topic.id].length === 0
    );
    
    if (topicsWithNoQuestions.length > 0) {
      console.log('\nTopics with no questions:');
      console.log('------------------------');
      topicsWithNoQuestions.forEach(topic => {
        console.log(`- ${topic.title} (ID: ${topic.id})`);
      });
    }
    
    console.log('\nImport completed successfully!');
    
  } catch (error) {
    console.error('Error during import:', error);
    process.exit(1);
  }
}

// Run the main function
main();
