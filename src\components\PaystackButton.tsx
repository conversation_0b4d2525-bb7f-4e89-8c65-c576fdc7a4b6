import { useState } from 'react';
import { usePaystackPayment } from 'react-paystack';
import { motion } from 'framer-motion';
import { useAuth } from '@/hooks/use-auth';
import { 
  PAYSTACK_PUBLIC_KEY, 
  generateReference, 
  toKobo, 
  handlePaymentSuccess, 
  handlePaymentClose,
  SubscriptionPlan
} from '@/utils/paystack';
import { toast } from 'sonner';

interface PaystackButtonProps {
  plan: SubscriptionPlan;
  className?: string;
  buttonText?: string;
}

const PaystackButton = ({ plan, className = '', buttonText = 'Select' }: PaystackButtonProps) => {
  const { user } = useAuth();
  const [isProcessing, setIsProcessing] = useState(false);
  
  // Initialize payment configuration
  const initializePayment = usePaystackPayment({
    reference: generateReference(),
    email: user?.email || '',
    amount: toKobo(plan.amount),
    publicKey: PAYSTACK_PUBLIC_KEY,
    currency: 'NGN',
    metadata: {
      custom_fields: [
        {
          display_name: 'Plan',
          variable_name: 'plan',
          value: plan.id
        }
      ]
    }
  });

  const handlePayment = () => {
    if (!user) {
      // Redirect to login if user is not authenticated
      toast.error('Please log in to subscribe to a plan');
      return;
    }

    setIsProcessing(true);
    
    // @ts-ignore - The type definitions for react-paystack are not complete
    initializePayment({
      onSuccess: (reference: any) => {
        handlePaymentSuccess(reference, plan.id, user.email)
          .then((result) => {
            if (result.success) {
              toast.success(result.message);
            }
          })
          .finally(() => {
            setIsProcessing(false);
          });
      },
      onClose: () => {
        handlePaymentClose();
        setIsProcessing(false);
      }
    });
  };

  return (
    <motion.button
      className={`w-full py-3 px-6 rounded-full font-medium transition-colors duration-200 uppercase text-sm tracking-wider ${className}`}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      onClick={handlePayment}
      disabled={isProcessing || !user}
    >
      {isProcessing ? 'Processing...' : buttonText}
    </motion.button>
  );
};

export default PaystackButton;
