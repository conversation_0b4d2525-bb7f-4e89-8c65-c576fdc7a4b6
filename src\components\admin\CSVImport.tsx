import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, CheckCircle2, Download, Upload } from "lucide-react";
import { parseQuestionCSV, generateCSVTemplate, ImportResult } from "@/utils/csv-import";
import { supabase } from "@/integrations/supabase/client";
import { Progress } from "@/components/ui/progress";
import { Spinner } from "@/components/ui/spinner";
import { useToast } from "@/hooks/use-toast";

interface Topic {
  id: string;
  title: string;
}

interface CSVImportProps {
  topics: { id: string; title: string }[];
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function CSVImport({ topics, onSuccess, onCancel }: CSVImportProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [selectedTopic, setSelectedTopic] = useState<string>("");
  const [importing, setImporting] = useState(false);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);
  const [progress, setProgress] = useState(0);
  const { toast } = useToast();

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setSelectedFile(e.target.files[0]);
      setImportResult(null);
    }
  };

  const handleTopicChange = (value: string) => {
    setSelectedTopic(value);
    setImportResult(null);
  };

  const handleDownloadTemplate = () => {
    const csvContent = generateCSVTemplate();
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", "quiz_questions_template.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleImport = async () => {
    if (!selectedFile || !selectedTopic) {
      toast({
        title: "Missing information",
        description: "Please select a file and topic before importing.",
        variant: "destructive",
      });
      return;
    }

    setImporting(true);
    setProgress(10);
    setImportResult(null);

    try {
      // Parse and validate the CSV file
      const result = await parseQuestionCSV(selectedFile, selectedTopic);
      setProgress(40);

      if (result.errors.length > 0) {
        setImportResult(result);
        setImporting(false);
        setProgress(100);
        return;
      }

      // Insert the validated questions into the database
      const { validQuestions } = result;
      let successCount = 0;

      // Insert questions in batches to show progress
      const batchSize = 5;
      const batches = Math.ceil(validQuestions.length / batchSize);

      for (let i = 0; i < batches; i++) {
        const start = i * batchSize;
        const end = Math.min(start + batchSize, validQuestions.length);
        const batch = validQuestions.slice(start, end);

        const { error } = await supabase.from("questions").insert(batch);

        if (error) {
          console.error("Error inserting questions:", error);
          toast({
            title: "Import error",
            description: `Error inserting questions: ${error.message}`,
            variant: "destructive",
          });
        } else {
          successCount += batch.length;
        }

        // Update progress
        setProgress(40 + Math.floor((i + 1) / batches * 60));
      }

      // Update the result with the actual number of imported questions
      result.validQuestions = result.validQuestions.slice(0, successCount);
      setImportResult(result);

      toast({
        title: "Import successful",
        description: `Successfully imported ${successCount} questions.`,
        variant: "default",
      });

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error("Import error:", error);
      toast({
        title: "Import error",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setImporting(false);
      setProgress(100);
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Import Questions from CSV</CardTitle>
        <CardDescription>
          Upload a CSV file with quiz questions to import them into the selected topic.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="topic">Select Topic</Label>
          <Select value={selectedTopic} onValueChange={handleTopicChange}>
            <SelectTrigger id="topic">
              <SelectValue placeholder="Select a topic" />
            </SelectTrigger>
            <SelectContent>
              {topics.map((topic) => (
                <SelectItem key={topic.id} value={topic.id}>
                  {topic.title}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="csv-file">Upload CSV File</Label>
          <div className="flex items-center gap-2">
            <Input
              id="csv-file"
              type="file"
              accept=".csv"
              onChange={handleFileChange}
              disabled={importing}
            />
            <Button
              variant="outline"
              size="icon"
              onClick={handleDownloadTemplate}
              title="Download CSV Template"
            >
              <Download className="h-4 w-4" />
            </Button>
          </div>
          <p className="text-sm text-muted-foreground">
            CSV must include: question_text, option_a, option_b, option_c, option_d, correct_answer, explanation, and optional difficulty.
          </p>
        </div>

        {importing && (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Spinner size="sm" />
              <span>Importing questions...</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>
        )}

        {importResult && (
          <div className="space-y-4">
            <Alert variant={importResult.errors.length > 0 ? "destructive" : "default"}>
              <div className="flex items-center gap-2">
                {importResult.errors.length > 0 ? (
                  <AlertCircle className="h-4 w-4" />
                ) : (
                  <CheckCircle2 className="h-4 w-4" />
                )}
                <AlertTitle>
                  {importResult.errors.length > 0
                    ? "Import completed with errors"
                    : "Import successful"}
                </AlertTitle>
              </div>
              <AlertDescription>
                {importResult.errors.length > 0 ? (
                  <div>
                    <p>
                      Imported {importResult.validQuestions.length} of {importResult.totalRows} questions.
                    </p>
                    <div className="mt-2 max-h-40 overflow-y-auto">
                      <p className="font-semibold">Errors:</p>
                      <ul className="list-disc pl-5 text-sm">
                        {importResult.errors.map((error, index) => (
                          <li key={index}>
                            Row {error.row}: {error.message}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                ) : (
                  <p>
                    Successfully imported {importResult.validQuestions.length} questions.
                  </p>
                )}
              </AlertDescription>
            </Alert>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-end gap-2">
        {onCancel && (
          <Button
            variant="outline"
            onClick={onCancel}
            disabled={importing}
          >
            Cancel
          </Button>
        )}
        <Button
          onClick={handleImport}
          disabled={!selectedFile || !selectedTopic || importing}
          className={onCancel ? "" : "w-full"}
        >
          {importing ? (
            <div className="flex items-center gap-2">
              <Spinner size="sm" />
              <span>Importing...</span>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <Upload className="h-4 w-4" />
              <span>Import Questions</span>
            </div>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
