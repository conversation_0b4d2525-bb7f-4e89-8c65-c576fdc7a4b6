
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 220 33% 98%;
    --foreground: 222 47% 11%;

    --card: 0 0% 100%;
    --card-foreground: 222 47% 11%;

    --popover: 0 0% 100%;
    --popover-foreground: 222 47% 11%;

    --primary: 201 71% 56%;
    --primary-foreground: 210 40% 98%;

    --secondary: 199 69% 84%;
    --secondary-foreground: 222 47% 11%;

    --muted: 210 40% 96%;
    --muted-foreground: 215 16% 47%;

    --accent: 271 91% 65%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 222 84% 48%;

    --radius: 0.75rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5% 26%;
    --sidebar-primary: 240 6% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 5% 96%;
    --sidebar-accent-foreground: 240 6% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217 91% 60%;
  }

  .dark {
    --background: 222 47% 11%;
    --foreground: 210 40% 98%;

    --card: 222 47% 11%;
    --card-foreground: 210 40% 98%;

    --popover: 222 47% 11%;
    --popover-foreground: 210 40% 98%;

    --primary: 201 71% 56%;
    --primary-foreground: 222 47% 11%;

    --secondary: 217 33% 18%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217 33% 18%;
    --muted-foreground: 215 20% 65%;

    --accent: 271 91% 65%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;

    --border: 217 33% 18%;
    --input: 217 33% 18%;
    --ring: 213 27% 84%;

    --sidebar-background: 240 6% 10%;
    --sidebar-foreground: 240 5% 96%;
    --sidebar-primary: 224 76% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 4% 16%;
    --sidebar-accent-foreground: 240 5% 96%;
    --sidebar-border: 240 4% 16%;
    --sidebar-ring: 217 91% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    font-family: 'Poppins', sans-serif;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Poppins', sans-serif;
    font-feature-settings: "ss01", "ss02", "cv01", "cv02", "cv03";
  }

  /* Cybersecurity-themed custom classes */
  .cyber-card {
    @apply bg-white/90 backdrop-blur-sm border border-cyber-light/30 shadow-md rounded-xl overflow-hidden;
  }

  .cyber-glow {
    @apply relative;
  }

  .cyber-glow::after {
    content: '';
    @apply absolute -inset-0.5 bg-gradient-to-r from-cyber-primary to-cyber-accent rounded-xl blur opacity-30 -z-10 transition-all duration-300;
  }

  .cyber-glow:hover::after {
    @apply opacity-60;
  }

  /* Premium button hover effect */
  .premium-btn:hover {
    @apply text-red-600 border-red-500;
  }

  /* Admin button hover effect */
  .admin-btn:hover {
    @apply text-red-600;
  }

  .cyber-grid-bg {
    background-image:
      linear-gradient(to right, rgba(14, 165, 233, 0.05) 1px, transparent 1px),
      linear-gradient(to bottom, rgba(14, 165, 233, 0.05) 1px, transparent 1px);
    background-size: 20px 20px;
  }

  .bottom-nav-item {
    @apply flex flex-col items-center justify-center text-xs font-medium text-muted-foreground;
  }

  .bottom-nav-item.active {
    @apply text-cyber-primary;
  }

  .quiz-option {
    @apply relative p-4 rounded-lg border bg-card w-full text-left transition-all duration-300
           hover:bg-cyber-light hover:border-cyber-primary/50 hover:shadow-md;
  }

  .quiz-option.selected {
    @apply border-cyber-primary bg-cyber-light/50;
  }

  .quiz-option.correct {
    @apply border-cyber-success bg-cyber-success/10;
  }

  .quiz-option.incorrect {
    @apply border-cyber-error bg-cyber-error/10;
  }

  .progress-bar {
    @apply h-2 rounded-full bg-cyber-primary/20 overflow-hidden;
  }

  .progress-bar-fill {
    @apply h-full bg-cyber-primary transition-all duration-300;
  }

  .explanation-box {
    @apply mt-6 p-4 bg-cyber-primary/5 border border-cyber-primary/20 rounded-md animate-in fade-in duration-300;
  }

  .explanation-icon {
    @apply bg-cyber-primary/10 p-1 rounded-full flex-shrink-0;
  }

  .explanation-title {
    @apply text-sm font-medium mb-1 text-cyber-primary;
  }

  .explanation-text {
    @apply text-sm text-muted-foreground leading-relaxed;
  }

  /* Custom styles for learning material content */
  .prose {
    font-family: 'Poppins', sans-serif !important;
  }

  .prose p, .prose li, .prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6,
  .prose ul, .prose ol, .prose blockquote, .prose pre, .prose code, .prose a, .prose strong, .prose em {
    font-family: 'Poppins', sans-serif !important;
  }

  /* Ensure all content in learning material pages uses Poppins */
  .learning-material-content * {
    font-family: 'Poppins', sans-serif !important;
  }
}
