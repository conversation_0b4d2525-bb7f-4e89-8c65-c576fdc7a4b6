import { cn } from "@/lib/utils";
import { UserCircle } from "lucide-react";
import { Link, useLocation } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/hooks/use-auth";
import { useIsMobile } from "@/hooks/use-mobile";
import { useState } from "react";
import { useAdminStatus } from "@/hooks/use-admin-status";

const Navbar = () => {
  const location = useLocation();
  const path = location.pathname;
  const { user } = useAuth();
  const isMobile = useIsMobile();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const { isAdmin } = useAdminStatus(user);

  const navItems = [
    {
      name: "Home",
      path: "/",
      active: path === "/",
      alwaysShow: true
    },
    {
      name: "Learn",
      path: "/learn",
      active: path === "/learn" || path.startsWith("/learn/"),
      alwaysShow: true
    },
    {
      name: "Quizzes",
      path: "/quizzes",
      active: path === "/quizzes",
      alwaysShow: true
    },
    {
      name: "About",
      path: "/about",
      active: path === "/about",
      alwaysShow: true
    },
    {
      name: "Contact",
      path: "/contact",
      active: path === "/contact",
      alwaysShow: true
    },
  ];

  // If mobile view, render a simplified navbar with About and Contact links
  if (isMobile) {
    return (
      <div className="sticky top-0 z-30 w-full border-b border-indigo-800 bg-indigo-900 backdrop-blur-sm">
        <div className="container flex h-16 items-center justify-between py-4">
          {/* Logo */}
          <Link
            to="/"
            className="flex items-center gap-2 transition-transform hover:scale-105"
          >
            <img src="/secquiz-logo.svg" alt="SecQuiz Logo" className="h-8 w-8" />
            <span className="font-bold text-xl text-white">
              SecQuiz
            </span>
          </Link>

          {/* Navigation Links */}
          <div className="flex items-center gap-3">
            <Link
              to="/about"
              className={cn(
                "text-sm font-medium transition-colors px-2 py-1",
                path === "/about"
                  ? "text-cyan-400"
                  : "text-white hover:text-cyan-400"
              )}
            >
              About
            </Link>
            <Link
              to="/contact"
              className={cn(
                "text-sm font-medium transition-colors px-2 py-1",
                path === "/contact"
                  ? "text-cyan-400"
                  : "text-white hover:text-cyan-400"
              )}
            >
              Contact
            </Link>
            {user && (
              <Link
                to="/profile"
                className="ml-1 p-1.5 rounded-full bg-indigo-800/50 text-white hover:bg-indigo-800 transition-colors"
              >
                <UserCircle className="h-5 w-5" />
              </Link>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Desktop navbar remains the same but updated to only show admin for admins
  return (
    <div className="sticky top-0 z-30 w-full border-b border-indigo-800 bg-indigo-900 backdrop-blur-sm">
      <div className="container flex h-16 items-center justify-between py-4">
        {/* Logo */}
        <Link
          to="/"
          className="flex items-center gap-2 transition-transform hover:scale-105"
        >
          <img src="/secquiz-logo.svg" alt="SecQuiz Logo" className="h-8 w-8" />
          <span className="font-bold text-xl text-white">
            SecQuiz
          </span>
        </Link>

        {/* Navigation Links */}
        <nav className="mx-6 flex items-center space-x-4 lg:space-x-6">
          {navItems.map((item) => (
            <Link
              key={item.name}
              to={item.path}
              className={cn(
                "text-sm font-medium transition-colors relative py-2",
                item.active
                  ? "text-white after:absolute after:bottom-0 after:left-0 after:right-0 after:h-0.5 after:bg-cyan-400"
                  : "text-white hover:text-cyan-400 hover:after:absolute hover:after:bottom-0 hover:after:left-0 hover:after:right-0 hover:after:h-0.5 hover:after:bg-cyan-400/40"
              )}
            >
              {item.name}
            </Link>
          ))}
          {isAdmin && (
            <Link
              to="/admin"
              className={cn(
                "text-sm font-medium transition-colors relative py-2",
                path === "/admin"
                  ? "text-white after:absolute after:bottom-0 after:left-0 after:right-0 after:h-0.5 after:bg-cyan-400"
                  : "text-white hover:text-cyan-400 hover:after:absolute hover:after:bottom-0 hover:after:left-0 hover:after:right-0 hover:after:h-0.5 hover:after:bg-cyan-400/40"
              )}
            >
              Admin
            </Link>
          )}
        </nav>

        {/* CTA */}
        <div className="flex items-center gap-4">
          {user ? (
            <Button asChild className="bg-cyan-500 hover:bg-cyan-600 animate-in slide-in-from-right-5 duration-300 text-white">
              <Link to="/profile">My Profile</Link>
            </Button>
          ) : (
            <Button asChild className="bg-green-500 hover:bg-green-600 animate-in slide-in-from-right-5 duration-300 text-white">
              <Link to="/auth">Sign In</Link>
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default Navbar;
