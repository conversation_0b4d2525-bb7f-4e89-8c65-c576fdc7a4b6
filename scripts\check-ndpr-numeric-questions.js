// <PERSON>ript to check NDPR questions with numeric indices
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkNumericQuestions() {
  try {
    // Get the NDPR topic ID
    const { data: topic, error: topicError } = await supabase
      .from('topics')
      .select('id')
      .eq('title', 'NDPR')
      .single();
    
    if (topicError) {
      console.error('Error fetching NDPR topic:', topicError);
      return;
    }
    
    console.log(`Found NDPR topic with ID: ${topic.id}`);
    
    // Get all questions for this topic
    const { data: questions, error: questionsError } = await supabase
      .from('questions')
      .select('id, question_text, options, correct_answer')
      .eq('topic_id', topic.id);
    
    if (questionsError) {
      console.error('Error fetching questions:', questionsError);
      return;
    }
    
    // Find questions with numeric option keys or numeric correct answers
    const numericQuestions = questions.filter(q => {
      const optionKeys = Object.keys(q.options);
      return optionKeys.some(k => ['0', '1', '2', '3'].includes(k)) || 
             ['0', '1', '2', '3'].includes(q.correct_answer);
    });
    
    console.log(`Found ${numericQuestions.length} questions with numeric indices out of ${questions.length} total questions`);
    
    // Display detailed information about each numeric question
    for (const question of numericQuestions) {
      console.log('\n' + '-'.repeat(80));
      console.log(`Question ID: ${question.id}`);
      console.log(`Question Text: ${question.question_text.substring(0, 100)}...`);
      console.log(`Correct Answer: ${question.correct_answer} (${typeof question.correct_answer})`);
      
      console.log('\nOptions:');
      const optionKeys = Object.keys(question.options);
      console.log(`Option Keys: ${optionKeys.join(', ')}`);
      
      for (const key of optionKeys) {
        console.log(`  ${key} (${typeof key}): ${question.options[key].substring(0, 50)}...`);
      }
      
      // Check if there's a mismatch between option keys and correct answer
      if (!optionKeys.includes(question.correct_answer)) {
        console.log(`\nWARNING: Correct answer "${question.correct_answer}" does not match any option key!`);
        
        // If correct_answer is numeric but options use letters
        if (['0', '1', '2', '3'].includes(question.correct_answer) && 
            optionKeys.some(k => ['A', 'B', 'C', 'D'].includes(k))) {
          const numberToLetter = { '0': 'A', '1': 'B', '2': 'C', '3': 'D' };
          console.log(`Suggestion: Change correct_answer to "${numberToLetter[question.correct_answer]}"`);
        }
        
        // If correct_answer is a letter but options use numbers
        if (['A', 'B', 'C', 'D'].includes(question.correct_answer) && 
            optionKeys.some(k => ['0', '1', '2', '3'].includes(k))) {
          const letterToNumber = { 'A': '0', 'B': '1', 'C': '2', 'D': '3' };
          console.log(`Suggestion: Change correct_answer to "${letterToNumber[question.correct_answer]}"`);
        }
      }
    }
    
  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the function
checkNumericQuestions();
