import { User } from "@supabase/supabase-js";
import { supabase } from "@/integrations/supabase/client";

// Array of admin user IDs - in a real app this would come from the database
// For demo purposes, you would add your test user ID here
const ADMIN_USER_IDS = [
  // Add test admin user IDs here
  // e.g. "d4c2b0a8-62e0-41e5-9b2d-8e1c9e3e4f5a"
];

// Array of admin emails for easier testing
const ADMIN_EMAILS = [
  "<EMAIL>"
];

// Array of subscribed (paid) user emails
const SUBSCRIBED_EMAILS = [
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>"
];

/**
 * Check if a user has admin privileges
 * This checks both hardcoded admin emails and the database
 */
export async function isUserAdmin(user: User | null): Promise<boolean> {
  if (!user) return false;

  // First check hardcoded lists for development/demo purposes
  // Check if the user's ID is in the admin list or email is in admin emails (case-insensitive)
  if (ADMIN_USER_IDS.includes(user.id)) return true;

  if (user.email) {
    const userEmail = user.email.toLowerCase();
    if (ADMIN_EMAILS.some(email => email.toLowerCase() === userEmail)) {
      return true;
    }
  }

  try {
    // Check admin_users table
    const { data: adminUser, error: adminError } = await supabase
      .from('admin_users')
      .select('*')
      .eq('user_id', user.id)
      .maybeSingle();

    if (adminUser) {
      return true;
    }

    // Also check profiles table
    const { data: userProfile, error: profileError } = await supabase
      .from('profiles')
      .select('is_admin')
      .eq('id', user.id)
      .maybeSingle();

    if (userProfile && userProfile.is_admin) {
      return true;
    }
  } catch (error) {
    console.error('Error checking admin status:', error);
  }

  return false;
}

/**
 * Check if a user has a paid subscription
 * This checks the subscriptions table in the database
 * and verifies that the subscription has not expired
 */
export async function isUserSubscribed(user: User | null): Promise<boolean> {
  if (!user) return false;

  try {
    // First check the hardcoded list for development/demo purposes
    if (user.email) {
      const userEmail = user.email.toLowerCase();
      if (SUBSCRIBED_EMAILS.some(email => email.toLowerCase() === userEmail)) {
        return true;
      }
    }

    // Check the user_profiles table first
    try {
      const { data: profile, error: profileError } = await supabase
        .from('user_profiles')
        .select('is_subscribed, subscription_expires_at')
        .eq('user_id', user.id)
        .maybeSingle();

      if (!profileError && profile && profile.is_subscribed) {
        // If there's an expiration date, check if it's still valid
        if (profile.subscription_expires_at) {
          const expirationDate = new Date(profile.subscription_expires_at);
          const now = new Date();

          // If subscription has expired, update the status in the database
          if (expirationDate <= now) {
            try {
              // Update the profile to mark subscription as expired
              await supabase
                .from('user_profiles')
                .update({
                  is_subscribed: false,
                  updated_at: new Date().toISOString()
                })
                .eq('user_id', user.id);
            } catch (updateError) {
              console.warn('Error updating user_profiles:', updateError);
              // Continue anyway
            }

            return false; // Subscription has expired
          }
        }

        return true; // Valid subscription
      }
    } catch (profileError) {
      console.warn('Error checking user_profiles table:', profileError);
      // Continue to next check
    }

    // Check the profiles table as a fallback
    const { data: legacyProfile, error: legacyProfileError } = await supabase
      .from('profiles')
      .select('subscription_status, subscription_ends_at')
      .eq('id', user.id)
      .maybeSingle();

    if (legacyProfile && legacyProfile.subscription_status === 'premium') {
      // If there's an expiration date, check if it's still valid
      if (!legacyProfile.subscription_ends_at || new Date(legacyProfile.subscription_ends_at) > new Date()) {
        return true;
      }
    }

    // Check the subscriptions table
    try {
      const { data: subscription, error: subscriptionError } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('user_id', user.id)
        .eq('is_active', true)
        .order('end_date', { ascending: false })
        .limit(1);

      if (!subscriptionError && subscription && subscription.length > 0) {
        // Check if the subscription has expired
        const endDate = new Date(subscription[0].end_date);
        const now = new Date();

        if (endDate > now) {
          return true; // Active subscription
        } else {
          try {
            // Update the subscription to mark it as inactive
            await supabase
              .from('subscriptions')
              .update({
                is_active: false,
                updated_at: new Date().toISOString()
              })
              .eq('id', subscription[0].id);
          } catch (updateError) {
            console.warn('Error updating subscription:', updateError);
            // Continue anyway
          }
        }
      }
    } catch (subscriptionError) {
      console.warn('Error checking subscriptions table:', subscriptionError);
      // Continue to next check
    }

    // Check the payments table as a last fallback
    try {
      const { data, error } = await supabase
        .from('payments')
        .select('*')
        .eq('user_id', user.id)
        .eq('status', 'completed')
        .order('created_at', { ascending: false })
        .limit(1);

      if (!error && data && data.length > 0) {
        // For payments, we need to check if there's an expiration date
        if (data[0].expires_at) {
          const expirationDate = new Date(data[0].expires_at);
          const now = new Date();

          if (expirationDate > now) {
            return true; // Payment is still valid
          }
        } else {
          // If there's no expiration date, consider it valid for backward compatibility
          return true;
        }
      }
    } catch (subError) {
      console.error('Error checking payments table:', subError);
      // Continue anyway, we've already checked other tables
    }

    return false;
  } catch (error) {
    console.error('Error checking subscription:', error);
    return false;
  }
}

/**
 * Get the subscription status of a user
 */
export async function getUserSubscriptionStatus(user: User | null): Promise<"Free" | "Premium" | "Expired"> {
  if (!user) return "Free";

  // Check if user is subscribed (active subscription)
  const subscribed = await isUserSubscribed(user);
  if (subscribed) return "Premium";

  // If not subscribed, check if they had a subscription that expired
  try {
    // Check user_profiles table
    try {
      const { data: profile, error: profileError } = await supabase
        .from('user_profiles')
        .select('is_subscribed, subscription_expires_at')
        .eq('user_id', user.id)
        .maybeSingle();

      if (!profileError && profile && profile.subscription_expires_at) {
        const expirationDate = new Date(profile.subscription_expires_at);
        const now = new Date();

        if (expirationDate <= now) {
          return "Expired";
        }
      }
    } catch (profileError) {
      console.warn('Error checking user_profiles for expiration:', profileError);
      // Continue to next check
    }

    // Check subscriptions table
    try {
      const { data: subscription, error: subscriptionError } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('user_id', user.id)
        .eq('is_active', false) // Look for inactive subscriptions
        .order('end_date', { ascending: false })
        .limit(1);

      if (!subscriptionError && subscription && subscription.length > 0) {
        return "Expired";
      }
    } catch (subscriptionError) {
      console.warn('Error checking subscriptions for expiration:', subscriptionError);
      // Continue anyway
    }
  } catch (error) {
    console.error('Error checking expired status:', error);
  }

  return "Free";
}

/**
 * Get the subscription details of a user
 */
export async function getUserSubscriptionDetails(user: User | null) {
  if (!user) return null;

  try {
    // First check if user_profiles table exists
    try {
      // First check user_profiles table
      const { data: profile, error: profileError } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('user_id', user.id)
        .maybeSingle();

      if (!profileError && profile) {
        const now = new Date();
        const expirationDate = profile.subscription_expires_at ? new Date(profile.subscription_expires_at) : null;
        const isExpired = expirationDate && expirationDate <= now;

        return {
          status: profile.is_subscribed ? 'premium' : (isExpired ? 'expired' : 'free'),
          expiresAt: profile.subscription_expires_at,
          isActive: profile.is_subscribed && (!expirationDate || expirationDate > now),
          isExpired: isExpired
        };
      }
    } catch (profileTableError) {
      console.warn('Error accessing user_profiles table:', profileTableError);
      // Continue to next check
    }

    // Check if subscriptions table exists
    try {
      // Check subscriptions table
      const { data: subscription, error: subscriptionError } = await supabase
        .from('subscriptions')
        .select('*')
        .eq('user_id', user.id)
        .order('end_date', { ascending: false })
        .limit(1);

      if (!subscriptionError && subscription && subscription.length > 0) {
        const now = new Date();
        const endDate = new Date(subscription[0].end_date);
        const isExpired = endDate <= now;

        return {
          status: isExpired ? 'expired' : 'premium',
          planId: subscription[0].plan_id,
          expiresAt: subscription[0].end_date,
          startDate: subscription[0].start_date,
          isActive: subscription[0].is_active && endDate > now,
          isExpired: isExpired,
          lastPayment: subscription[0].last_payment_reference
        };
      }
    } catch (subscriptionTableError) {
      console.warn('Error accessing subscriptions table:', subscriptionTableError);
      // Continue to next check
    }

    // Fallback to legacy profile
    const { data: legacyProfile, error: legacyProfileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .maybeSingle();

    if (!legacyProfileError && legacyProfile) {
      const now = new Date();
      const expirationDate = legacyProfile.subscription_ends_at ? new Date(legacyProfile.subscription_ends_at) : null;
      const isExpired = expirationDate && expirationDate <= now;

      return {
        status: legacyProfile.subscription_status === 'premium' ?
               (isExpired ? 'expired' : 'premium') : 'free',
        expiresAt: legacyProfile.subscription_ends_at,
        isActive: legacyProfile.subscription_status === 'premium' &&
                 (!expirationDate || expirationDate > now),
        isExpired: isExpired
      };
    }

    // Fallback to payments
    const { data: payment, error: paymentError } = await supabase
      .from('payments')
      .select('*')
      .eq('user_id', user.id)
      .eq('status', 'completed')
      .order('created_at', { ascending: false })
      .limit(1);

    if (!paymentError && payment && payment.length > 0) {
      const now = new Date();
      const expirationDate = payment[0].expires_at ? new Date(payment[0].expires_at) : null;
      const isExpired = expirationDate && expirationDate <= now;

      return {
        status: isExpired ? 'expired' : 'premium',
        paymentId: payment[0].id,
        amount: payment[0].amount,
        currency: payment[0].currency,
        createdAt: payment[0].created_at,
        expiresAt: payment[0].expires_at,
        isActive: !isExpired,
        isExpired: isExpired
      };
    }

    return {
      status: 'free',
      isActive: false,
      isExpired: false
    };
  } catch (error) {
    console.error('Error getting subscription details:', error);
    return null;
  }
}

/**
 * Helper for handling the free questions quota for premium content
 * Note: This limit only applies to premium content.
 * Registered users have unlimited access to free courses.
 */
export const FREE_QUESTIONS_LIMIT = 10;

export function getRemainingFreeQuestions(): number {
  const used = getUsedFreeQuestions();
  return Math.max(0, FREE_QUESTIONS_LIMIT - used);
}

export function getUsedFreeQuestions(): number {
  try {
    const used = parseInt(localStorage.getItem('free_questions_used') || '0', 10);
    return isNaN(used) ? 0 : used;
  } catch (e) {
    return 0;
  }
}

export function incrementFreeQuestionsUsed(): void {
  try {
    const current = getUsedFreeQuestions();
    localStorage.setItem('free_questions_used', (current + 1).toString());
  } catch (e) {
    console.error("Could not update local storage", e);
  }
}

export function resetFreeQuestionsUsed(): void {
  try {
    localStorage.setItem('free_questions_used', '0');
  } catch (e) {
    console.error("Could not reset local storage", e);
  }
}
