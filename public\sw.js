// Service Worker for SecQuiz PWA

const CACHE_VERSION = 1;
const CACHE_NAME = `secquiz-cache-v${CACHE_VERSION}`;

// Assets to cache immediately on install
const PRECACHE_ASSETS = [
  '/',
  '/index.html',
  '/manifest.json',
  '/secquiz-logo.svg',
  '/icons/icon-192x192.png',
  '/icons/icon-512x512.png',
  '/icons/icon-72x72.png',
  '/icons/icon-96x96.png',
  '/icons/icon-128x128.png',
  '/icons/icon-144x144.png',
  '/icons/icon-152x152.png',
  '/icons/icon-384x384.png',
  '/screenshots/home.png',
  '/screenshots/quiz.png'
];

// Cache for dynamic content
const DYNAMIC_CACHE = `secquiz-dynamic-v${CACHE_VERSION}`;

// API endpoints that should never be cached
const API_ENDPOINTS = [
  '/api/',
  'supabase.co',
  'api.paystack.co'
];

// Offline fallback page
const OFFLINE_PAGE = '/offline.html';

// Install event - cache assets and skip waiting to activate immediately
self.addEventListener('install', (event) => {
  event.waitUntil(
    (async () => {
      try {
        const cache = await caches.open(CACHE_NAME);
        // Cache all precache assets
        await cache.addAll(PRECACHE_ASSETS);

        // Create offline page if it doesn't exist
        const offlineResponse = await fetch(OFFLINE_PAGE).catch(() => null);
        if (!offlineResponse) {
          // Create a simple offline page if it doesn't exist
          const offlineContent = `
            <!DOCTYPE html>
            <html lang="en">
            <head>
              <meta charset="UTF-8">
              <meta name="viewport" content="width=device-width, initial-scale=1.0">
              <title>SecQuiz - Offline</title>
              <style>
                body { font-family: 'Poppins', sans-serif; margin: 0; padding: 20px; text-align: center; }
                .container { max-width: 500px; margin: 50px auto; }
                h1 { color: #4f46e5; }
                p { color: #4b5563; }
                .icon { font-size: 64px; margin-bottom: 20px; }
              </style>
            </head>
            <body>
              <div class="container">
                <div class="icon">📡</div>
                <h1>You're offline</h1>
                <p>Please check your internet connection and try again.</p>
              </div>
            </body>
            </html>
          `;
          const offlineBlob = new Blob([offlineContent], { type: 'text/html' });
          const offlineResponse = new Response(offlineBlob, {
            status: 200,
            headers: { 'Content-Type': 'text/html' }
          });
          await cache.put(OFFLINE_PAGE, offlineResponse);
        }

        // Skip waiting to activate immediately
        await self.skipWaiting();
      } catch (error) {
        console.error('Service worker installation failed:', error);
      }
    })()
  );
});

// Activate event - clean up old caches and claim clients
self.addEventListener('activate', (event) => {
  event.waitUntil(
    (async () => {
      try {
        // Clean up old caches
        const cacheWhitelist = [CACHE_NAME, DYNAMIC_CACHE];
        const cacheNames = await caches.keys();

        await Promise.all(
          cacheNames.map(cacheName => {
            if (!cacheWhitelist.includes(cacheName)) {
              return caches.delete(cacheName);
            }
          })
        );

        // Claim any clients immediately
        await self.clients.claim();

        // Notify clients about the update
        const clients = await self.clients.matchAll({ type: 'window' });
        clients.forEach(client => {
          client.postMessage({
            type: 'CACHE_UPDATED',
            version: CACHE_VERSION
          });
        });
      } catch (error) {
        console.error('Service worker activation failed:', error);
      }
    })()
  );
});

// Fetch event - implement stale-while-revalidate strategy
self.addEventListener('fetch', (event) => {
  // Skip cross-origin requests and API endpoints
  const shouldHandleFetch = event.request.url.startsWith(self.location.origin) &&
    !API_ENDPOINTS.some(endpoint => event.request.url.includes(endpoint));

  if (!shouldHandleFetch) {
    // For API requests, just use network
    return;
  }

  // For HTML navigation requests - use network-first strategy
  if (event.request.mode === 'navigate') {
    event.respondWith(
      (async () => {
        try {
          // Try network first for fresh content
          const networkResponse = await fetch(event.request);

          // Cache the response for future use
          const cache = await caches.open(DYNAMIC_CACHE);
          cache.put(event.request, networkResponse.clone());

          return networkResponse;
        } catch (error) {
          // Network failed, try cache
          const cachedResponse = await caches.match(event.request);

          if (cachedResponse) {
            return cachedResponse;
          }

          // If no cached response, return offline page
          return caches.match(OFFLINE_PAGE);
        }
      })()
    );
    return;
  }

  // For other requests (assets, etc.) - use stale-while-revalidate
  event.respondWith(
    (async () => {
      try {
        // Try cache first
        const cachedResponse = await caches.match(event.request);

        // Start network fetch in the background
        const fetchPromise = fetch(event.request).then(async (networkResponse) => {
          if (networkResponse && networkResponse.ok) {
            // Update cache with fresh response
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(event.request, networkResponse.clone());
          }
          return networkResponse;
        }).catch(() => {
          // Network failed completely
          return null;
        });

        // Return cached response immediately if available
        if (cachedResponse) {
          return cachedResponse;
        }

        // Otherwise wait for network response
        const networkResponse = await fetchPromise;
        if (networkResponse) {
          return networkResponse;
        }

        // If both cache and network fail, return offline page for HTML
        if (event.request.headers.get('accept').includes('text/html')) {
          return caches.match(OFFLINE_PAGE);
        }

        // For other resources, just fail
        throw new Error('Resource not in cache and network unavailable');
      } catch (error) {
        console.error('Fetch handler failed:', error);

        // Last resort - return offline page for HTML requests
        if (event.request.headers.get('accept').includes('text/html')) {
          return caches.match(OFFLINE_PAGE);
        }

        // Otherwise just fail
        throw error;
      }
    })()
  );
});

// Handle messages from clients
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});
