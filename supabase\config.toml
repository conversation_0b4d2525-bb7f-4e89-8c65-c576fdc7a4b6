project_id = "agdyycknlxojiwhlqicq"

[functions.create-payment]
verify_jwt = true

# Email configuration for Supabase Auth
[auth]
enable_signup = true
site_url = "https://secquiz.app"

# Email configuration
[auth.email]
enable_signup = true
double_confirm_changes = true
enable_confirmations = true
sender_name = "Secquiz"
sender_email = "<EMAIL>"

# SMTP configuration for Resend
[auth.smtp]
host = "smtp.resend.com"
port = 465
user = "resend"
pass = "re_REPLACE_WITH_YOUR_RESEND_API_KEY" # Replace with your actual Resend API key
admin_email = "<EMAIL>"
max_frequency = 1
