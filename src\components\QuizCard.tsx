import { motion } from "framer-motion";
import { ArrowRight, Star, Shield, Network, Database, Key, Cloud, FileText, ShieldCheck, Lock } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Link } from "react-router-dom";

interface QuizCardProps {
  id: string;
  title: string;
  description: string;
  questionCount: number;
  points?: number;
  difficulty: "easy" | "medium" | "hard";
  isPremium?: boolean;
  icon?: React.ReactNode;
  bgColor?: string;
}

// Function to get icon based on quiz title
const getIconByTitle = (title: string) => {
  const titleLower = title.toLowerCase();
  if (titleLower.includes('network')) return <Network className="w-10 h-10" />;
  if (titleLower.includes('web')) return <Database className="w-10 h-10" />;
  if (titleLower.includes('crypto')) return <Key className="w-10 h-10" />;
  if (titleLower.includes('cissp')) return <ShieldCheck className="w-10 h-10" />;
  if (titleLower.includes('ethical')) return <Lock className="w-10 h-10" />;
  if (titleLower.includes('gdpr')) return <FileText className="w-10 h-10" />;
  if (titleLower.includes('cloud')) return <Cloud className="w-10 h-10" />;
  return <Shield className="w-10 h-10" />;
};

// Function to get background color based on quiz title
const getBgColorByTitle = (title: string) => {
  const titleLower = title.toLowerCase();
  if (titleLower.includes('network')) return 'bg-blue-600';
  if (titleLower.includes('web')) return 'bg-green-600';
  if (titleLower.includes('crypto')) return 'bg-purple-600';
  if (titleLower.includes('cissp')) return 'bg-orange-600';
  if (titleLower.includes('ethical')) return 'bg-red-600';
  if (titleLower.includes('gdpr')) return 'bg-teal-600';
  if (titleLower.includes('cloud')) return 'bg-cyan-600';
  return 'bg-indigo-600';
};

const QuizCard = ({
  id,
  title,
  description,
  questionCount,
  points = 100,
  difficulty,
  isPremium = false,
  icon,
  bgColor
}: QuizCardProps) => {
  // Get difficulty color
  const getDifficultyColor = () => {
    switch (difficulty) {
      case "easy":
        return "bg-green-100 text-green-700";
      case "medium":
        return "bg-blue-100 text-blue-700";
      case "hard":
        return "bg-purple-100 text-purple-700";
      default:
        return "bg-gray-100 text-gray-700";
    }
  };

  return (
    <motion.div
      className="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300"
      whileHover={{ y: -5, transition: { duration: 0.2 } }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className={`${bgColor || getBgColorByTitle(title)} p-6 relative`}>
        {isPremium && (
          <div className="absolute top-3 right-3 bg-gradient-to-r from-cyber-primary to-cyber-accent text-white text-xs font-bold px-2 py-1 rounded-full">
            PRO
          </div>
        )}
        <div className="flex justify-center items-center h-24">
          {icon || (
            <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white">
              {getIconByTitle(title)}
            </div>
          )}
        </div>
      </div>

      <div className="p-6">
        <h3 className="text-xl font-bold mb-2">{title}</h3>
        <p className="text-gray-600 text-sm mb-4 line-clamp-2">{description}</p>

        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center gap-1">
            <span className="text-xs text-gray-500">{questionCount} questions</span>
          </div>
          <div className="flex items-center gap-1">
            <Star className="w-3 h-3 text-yellow-500" />
            <span className="text-xs text-gray-500">+{points} points</span>
          </div>
          <div className={`text-xs px-2 py-1 rounded-full ${getDifficultyColor()}`}>
            {difficulty}
          </div>
        </div>

        <Button asChild className={`w-full ${bgColor || getBgColorByTitle(title)} hover:brightness-90 transition-all`}>
          <Link to={`/quiz/${id}`}>
            Start Quiz <ArrowRight className="ml-2 h-4 w-4" />
          </Link>
        </Button>
      </div>
    </motion.div>
  );
};

export default QuizCard;
