// This file is kept as a placeholder for future custom email functionality
// Currently, we're using Supabase's built-in email system

/**
 * Send a signup confirmation email
 *
 * Note: Currently using Supabase's built-in email system
 */
export function sendSignupEmail(to: string, name: string, confirmationLink: string) {
  console.log('Using Supabase built-in email system for signup');
  return { success: true };
}

/**
 * Send a password reset email
 *
 * Note: Currently using Supabase's built-in email system
 */
export function sendPasswordResetEmail(to: string, name: string, resetLink: string) {
  console.log('Using Supabase built-in email system for password reset');
  return { success: true };
}

/**
 * Send a magic link email
 *
 * Note: Currently using Supabase's built-in email system
 */
export function sendMagicLinkEmail(to: string, name: string, magicLink: string) {
  console.log('Using Supabase built-in email system for magic link');
  return { success: true };
}
