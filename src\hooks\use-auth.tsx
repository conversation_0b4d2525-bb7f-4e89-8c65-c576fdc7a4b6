
import { createContext, useContext, useEffect, useState } from "react";
import { Session, User, AuthError } from "@supabase/supabase-js";
import { supabase } from "@/integrations/supabase/client";
import { authConfig, featureFlags } from "@/config";
import * as authService from "@/services/auth-service";

interface AuthContextType {
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  signIn: (email: string, password: string) => Promise<{
    error: AuthError | null;
    data: { session: Session | null };
  }>;
  signUp: (email: string, password: string, fullName: string) => Promise<{
    error: AuthError | null;
    data: { session: Session | null };
  }>;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Set up auth state listener FIRST
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        // Log auth events in debug mode
        if (featureFlags.enableDebugMode) {
          console.log(`Auth event: ${event}`, session);
        }

        // Handle token refresh errors
        if (event === 'TOKEN_REFRESHED') {
          console.log('Token refreshed successfully');
        }

        // Update state based on session
        setSession(session);
        setUser(session?.user ?? null);
      }
    );

    // THEN check for existing session
    const checkSession = async () => {
      try {
        const { data, error } = await supabase.auth.getSession();

        if (error) {
          console.error('Error getting session:', error);
          // Clear session state on error
          setSession(null);
          setUser(null);
        } else {
          setSession(data.session);
          setUser(data.session?.user ?? null);
        }
      } catch (err) {
        console.error('Unexpected error getting session:', err);
        // Clear session state on error
        setSession(null);
        setUser(null);
      } finally {
        setIsLoading(false);
      }
    };

    checkSession();

    return () => subscription.unsubscribe();
  }, []);

  async function signIn(email: string, password: string) {
    return await authService.signIn(email, password);
  }

  async function signUp(email: string, password: string, fullName: string) {
    try {
      // Log debug information if debug mode is enabled
      if (featureFlags.enableDebugMode) {
        console.log("Signup with custom email service");
      }

      // Sign up with custom email service
      const result = await authService.signUp(email, password, {
        full_name: fullName
      });

      // Log debug information if debug mode is enabled
      if (featureFlags.enableDebugMode) {
        console.log("Signup result:", result);
      }

      // If there's an error about email confirmation, handle it gracefully
      if (result.error) {
        if (result.error.message && result.error.message.includes("confirmation email")) {
          if (featureFlags.enableDebugMode) {
            console.log("Email confirmation error handled gracefully");
          }
          // Return a successful signup without the error
          return {
            data: result.data,
            error: null
          };
        }
        console.error("Signup error:", result.error);
      } else {
        // Check if the user was created but needs email confirmation
        if (featureFlags.enableDebugMode) {
          if (result.data?.user && !result.data?.session) {
            console.log("User created, awaiting email confirmation");
          } else if (result.data?.user && result.data?.session) {
            console.log("User created and automatically signed in");
          }
        }
      }

      return result;
    } catch (error) {
      console.error("Signup error:", error);
      return {
        data: { session: null },
        error: { message: "An unexpected error occurred during signup." } as AuthError
      };
    }
  }

  async function signOut() {
    try {
      // Log debug information if debug mode is enabled
      if (featureFlags.enableDebugMode) {
        console.log("Signing out user:", user?.email);
      }

      // Clear local storage manually first to ensure tokens are removed
      // This helps prevent the "Invalid Refresh Token" error
      try {
        localStorage.removeItem('secquiz-auth');
        localStorage.removeItem('supabase.auth.token');
      } catch (storageError) {
        console.warn("Error clearing local storage:", storageError);
        // Continue with signout process anyway
      }

      // Sign out using the auth service
      const { error } = await authService.signOut();

      if (error) {
        console.error("Error signing out:", error);
        // Don't throw the error, just log it and continue
      }

      // Manually clear user and session state
      setUser(null);
      setSession(null);

      // Log debug information if debug mode is enabled
      if (featureFlags.enableDebugMode) {
        console.log("User signed out successfully");
      }

      // Force a page reload to clear any cached state
      window.location.href = "/";
    } catch (error) {
      console.error("Unexpected error during sign out:", error);
      // Still try to clear the state even if there was an error
      setUser(null);
      setSession(null);

      // Force a page reload even on error
      window.location.href = "/";
    }
  }

  const value = {
    user,
    session,
    isLoading,
    signIn,
    signUp,
    signOut,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
