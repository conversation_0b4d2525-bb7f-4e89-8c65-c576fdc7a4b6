import { supabase } from '@/integrations/supabase/client';
import { checkFeedbackTable, createFeedbackTable } from './create-feedback-table';

/**
 * Migrate feedback from local storage to the database
 * @returns Object with success status and counts
 */
export async function migrateLocalFeedbackToDatabase() {
  try {
    // First check if the feedback table exists
    let tableExists = await checkFeedbackTable();
    
    // If not, try to create it
    if (!tableExists) {
      const created = await createFeedbackTable();
      if (!created) {
        return { 
          success: false, 
          error: 'Failed to create feedback table',
          migratedCount: 0,
          failedCount: 0
        };
      }
      tableExists = true;
    }
    
    // Get feedback from local storage
    const localFeedback = getLocalFeedback();
    
    if (localFeedback.length === 0) {
      return { 
        success: true, 
        message: 'No local feedback to migrate',
        migratedCount: 0,
        failedCount: 0
      };
    }
    
    // Migrate each feedback item
    let migratedCount = 0;
    let failedCount = 0;
    
    for (const feedback of localFeedback) {
      try {
        const { error } = await supabase
          .from('feedback')
          .insert({
            id: feedback.id, // Use the same ID to avoid duplicates
            name: feedback.name,
            email: feedback.email,
            subject: feedback.subject,
            message: feedback.message,
            user_id: feedback.user_id,
            status: feedback.status,
            created_at: feedback.created_at,
            updated_at: feedback.updated_at
          });
        
        if (!error) {
          migratedCount++;
        } else {
          console.error('Error migrating feedback item:', error);
          failedCount++;
        }
      } catch (error) {
        console.error('Exception migrating feedback item:', error);
        failedCount++;
      }
    }
    
    // If we successfully migrated any items, update local storage
    if (migratedCount > 0) {
      try {
        // Get the IDs of successfully migrated items
        const migratedIds = localFeedback
          .slice(0, migratedCount)
          .map(item => item.id);
        
        // Remove migrated items from local storage
        const remainingFeedback = localFeedback.filter(
          item => !migratedIds.includes(item.id)
        );
        
        localStorage.setItem('pendingFeedback', JSON.stringify(remainingFeedback));
      } catch (error) {
        console.error('Error updating local storage after migration:', error);
      }
    }
    
    return {
      success: true,
      migratedCount,
      failedCount,
      message: `Migrated ${migratedCount} items, failed to migrate ${failedCount} items`
    };
  } catch (error: any) {
    console.error('Error migrating local feedback:', error);
    return {
      success: false,
      error: error.message || 'Unknown error migrating feedback',
      migratedCount: 0,
      failedCount: 0
    };
  }
}

/**
 * Get feedback from local storage
 */
function getLocalFeedback() {
  try {
    const localFeedback = localStorage.getItem('pendingFeedback');
    return localFeedback ? JSON.parse(localFeedback) : [];
  } catch (error) {
    console.error('Error reading from local storage:', error);
    return [];
  }
}
