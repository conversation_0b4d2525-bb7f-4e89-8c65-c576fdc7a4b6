// Script to apply SMTP configuration to Supabase
import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import readline from 'readline';

// Set up __dirname equivalent for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Function to prompt for Resend API key
function promptForResendApiKey() {
  return new Promise((resolve) => {
    rl.question('Enter your Resend API key (starts with re_): ', (apiKey) => {
      resolve(apiKey);
    });
  });
}

// Main function
async function applySmtpConfig() {
  console.log('=== Applying SMTP Configuration to Supabase ===');
  
  try {
    // Get Resend API key from user
    const resendApiKey = await promptForResendApiKey();
    
    if (!resendApiKey.startsWith('re_')) {
      console.error('Error: Invalid Resend API key format. It should start with "re_"');
      rl.close();
      return;
    }
    
    // Path to config.toml
    const configPath = path.join(__dirname, '../supabase/config.toml');
    
    // Read the current config
    let configContent = fs.readFileSync(configPath, 'utf8');
    
    // Replace the placeholder with the actual API key
    configContent = configContent.replace('re_REPLACE_WITH_YOUR_RESEND_API_KEY', resendApiKey);
    
    // Write the updated config back to the file
    fs.writeFileSync(configPath, configContent);
    
    console.log('SMTP configuration updated with your Resend API key.');
    
    // Check if Supabase CLI is installed
    try {
      execSync('supabase --version', { stdio: 'ignore' });
      
      // Ask if user wants to restart Supabase
      rl.question('Do you want to restart Supabase to apply the changes? (y/n): ', (answer) => {
        if (answer.toLowerCase() === 'y') {
          console.log('\nRestarting Supabase...');
          try {
            execSync('supabase stop && supabase start', { stdio: 'inherit' });
            console.log('Supabase restarted successfully!');
          } catch (error) {
            console.error('Error restarting Supabase:', error.message);
            console.log('\nYou may need to manually restart Supabase or apply the changes through the Supabase dashboard.');
          }
        } else {
          console.log('\nPlease manually restart Supabase or apply the changes through the Supabase dashboard.');
        }
        rl.close();
      });
    } catch (error) {
      console.log('\nSupabase CLI not found. Please apply the changes through the Supabase dashboard:');
      console.log('1. Go to your Supabase project dashboard');
      console.log('2. Navigate to Authentication > Email Templates');
      console.log('3. Configure the SMTP settings with your Resend API key');
      rl.close();
    }
  } catch (err) {
    console.error('Unexpected error:', err);
    rl.close();
  }
}

// Run the script
applySmtpConfig();
