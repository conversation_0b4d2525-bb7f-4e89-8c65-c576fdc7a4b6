-- Create a function to verify a user's email
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION verify_user_email(user_email TEXT)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_id UUID;
BEGIN
  -- Get the user ID from the email
  SELECT id INTO user_id FROM auth.users WHERE email = user_email;
  
  -- Update the user's email_confirmed status
  UPDATE auth.users
  SET email_confirmed_at = now(),
      updated_at = now()
  WHERE id = user_id;
END;
$$;
