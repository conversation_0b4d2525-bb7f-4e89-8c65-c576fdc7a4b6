// Script to fix specific NDPR questions with numeric indices
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// The specific question IDs to fix
const questionIds = [
  'b818fc87-79df-47a7-9b6e-8472dbeedaec', // DPIA question
  'a15ded3b-0edb-4f8d-9dda-d44687f36119'  // DPO question
];

async function fixSpecificQuestions() {
  try {
    console.log('Starting to fix specific NDPR questions with numeric indices...');

    for (const questionId of questionIds) {
      // Get the question
      const { data: question, error: questionError } = await supabase
        .from('questions')
        .select('id, question_text, options, correct_answer')
        .eq('id', questionId)
        .single();

      if (questionError) {
        console.error(`Error fetching question ${questionId}:`, questionError);
        continue;
      }

      console.log(`\nProcessing question: ${question.question_text.substring(0, 100)}...`);
      console.log(`Current correct answer: ${question.correct_answer}`);
      console.log(`Current options: ${JSON.stringify(question.options)}`);

      // Convert numeric options to letter options
      const newOptions = {};
      const numberToLetter = { '0': 'A', '1': 'B', '2': 'C', '3': 'D' };

      for (const [key, value] of Object.entries(question.options)) {
        if (['0', '1', '2', '3'].includes(key)) {
          newOptions[numberToLetter[key]] = value;
        } else {
          newOptions[key] = value;
        }
      }

      // Convert numeric correct answer to letter
      let newCorrectAnswer = question.correct_answer;
      if (['0', '1', '2', '3'].includes(question.correct_answer)) {
        newCorrectAnswer = numberToLetter[question.correct_answer];
      }

      console.log(`New correct answer: ${newCorrectAnswer}`);
      console.log(`New options: ${JSON.stringify(newOptions)}`);

      // Update the question
      const { data, error: updateError } = await supabase
        .from('questions')
        .update({
          options: newOptions,
          correct_answer: newCorrectAnswer
        })
        .eq('id', questionId)
        .select();

      console.log(`Update response:`, data);

      if (updateError) {
        console.error(`Error updating question ${questionId}:`, updateError);
      } else {
        console.log(`Successfully updated question ${questionId}`);
      }
    }

    console.log('\nFinished fixing specific NDPR questions');

  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the function
fixSpecificQuestions();
