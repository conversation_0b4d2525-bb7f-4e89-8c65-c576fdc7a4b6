import { describe, it, expect, vi } from 'vitest';
import { renderHook } from '@testing-library/react';
import { useIsAdminOrProfilePage } from '@/hooks/use-is-admin-or-profile-page';
import { MemoryRouter } from 'react-router-dom';

// We'll use MemoryRouter instead of mocking useLocation

describe('useIsAdminOrProfilePage hook', () => {
  it('returns true when on admin page', () => {
    // Setup the test with MemoryRouter at /admin path
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <MemoryRouter initialEntries={['/admin']}>
        {children}
      </MemoryRouter>
    );

    const { result } = renderHook(() => useIsAdminOrProfilePage(), { wrapper });

    expect(result.current).toBe(true);
  });

  it('returns true when on profile page', () => {
    // Setup the test with MemoryRouter at /profile path
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <MemoryRouter initialEntries={['/profile']}>
        {children}
      </MemoryRouter>
    );

    const { result } = renderHook(() => useIsAdminOrProfilePage(), { wrapper });

    expect(result.current).toBe(true);
  });

  it('returns false when on other pages', () => {
    // Setup the test with MemoryRouter at a different path
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <MemoryRouter initialEntries={['/quizzes']}>
        {children}
      </MemoryRouter>
    );

    const { result } = renderHook(() => useIsAdminOrProfilePage(), { wrapper });

    expect(result.current).toBe(false);
  });
});
