# CSV Import Guide for SecQuiz

This guide explains how to use the CSV import feature to add questions to your SecQuiz application.

## CSV Format

The CSV file should have the following columns:

| Column | Description | Required |
|--------|-------------|----------|
| question_text | The text of the question | Yes |
| option_a | The text for option A | Yes |
| option_b | The text for option B | Yes |
| option_c | The text for option C | Yes |
| option_d | The text for option D | Yes |
| correct_answer | The correct answer (A, B, C, or D) | Yes |
| explanation | Explanation of the correct answer | Yes |
| difficulty | Difficulty level (easy, medium, hard) | No (defaults to medium) |

## Example CSV

```csv
question_text,option_a,option_b,option_c,option_d,correct_answer,explanation,difficulty
What is the primary purpose of a firewall?,To prevent physical access to a network,To control network traffic based on security rules,To encrypt data during transmission,To detect malware on endpoints,B,A firewall controls network traffic flow based on predetermined security rules allowing or blocking traffic based on these rules.,medium
What is a vulnerability assessment?,A process to identify security vulnerabilities in systems or applications,An evaluation of employee performance,A check for physical security weaknesses,A financial audit,A,A vulnerability assessment is a systematic process of identifying classifying and prioritizing security vulnerabilities in systems networks or applications.,easy
What is a data breach?,A hardware failure,An unauthorized access and extraction of sensitive data,A routine backup process,A software update,B,A data breach is a security incident where sensitive protected or confidential data is accessed viewed stolen or used by an unauthorized party.,hard
```

## How to Import Questions

1. **Prepare your CSV file**:
   - Create a CSV file with the columns listed above
   - Make sure each question has all required fields
   - Save the file with a `.csv` extension

2. **Import the questions**:
   - Log in to your SecQuiz admin dashboard
   - Go to the "Questions" tab
   - Click the "Import" button and select "CSV Format"
   - Select the topic you want to add the questions to
   - Upload your CSV file
   - Click "Import Questions"

3. **Review the results**:
   - The system will validate your CSV file
   - If there are any errors, they will be displayed
   - Successfully imported questions will be added to the selected topic

## Tips for Successful Imports

- Make sure your CSV file uses commas (,) as separators
- If your text contains commas, enclose the text in double quotes (")
- Keep question texts concise and clear
- Provide detailed explanations for correct answers
- Use consistent formatting for options
- Verify the correct answer is one of the options (A, B, C, or D)
- For large imports, consider splitting into multiple smaller files

## Troubleshooting

If you encounter issues during import:

- Check that your CSV file is properly formatted
- Ensure all required fields are present
- Verify that the correct answer is valid (A, B, C, or D)
- Check for special characters that might cause parsing issues
- Try importing a smaller batch to identify problematic questions

For any further assistance, please contact the system administrator.
