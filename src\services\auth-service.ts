import { supabase } from '@/integrations/supabase/client';

/**
 * Sign up a new user
 */
export async function signUp(email: string, password: string, metadata = {}) {
  try {
    // Always use Supabase's built-in email verification
    // but customize the redirect URL
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: `${window.location.origin}/auth/callback`,
        data: metadata
      }
    });

    if (error) throw error;

    // If signup was successful, we can show a custom message
    // but we'll use Supabase's built-in email system
    return { data, error: null };
  } catch (error) {
    console.error('Error signing up:', error);
    return { data: null, error };
  }
}

/**
 * Request password reset
 */
export async function resetPassword(email: string) {
  try {
    // Use Supabase's built-in reset password
    const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/auth/reset-password`,
    });

    if (error) throw error;

    return { data, error: null };
  } catch (error) {
    console.error('Error resetting password:', error);
    return { data: null, error };
  }
}

/**
 * Sign in with magic link
 */
export async function signInWithMagicLink(email: string) {
  try {
    // Use Supabase's built-in magic link
    const { data, error } = await supabase.auth.signInWithOtp({
      email,
      options: {
        emailRedirectTo: `${window.location.origin}/auth/callback`,
      },
    });

    if (error) throw error;

    return { data, error: null };
  } catch (error) {
    console.error('Error sending magic link:', error);
    return { data: null, error };
  }
}

/**
 * Sign in with email and password
 */
export async function signIn(email: string, password: string) {
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) throw error;

    return { data, error: null };
  } catch (error) {
    console.error('Error signing in:', error);
    return { data: null, error };
  }
}

/**
 * Sign out
 */
export async function signOut() {
  try {
    // Use the scope parameter to clear all storage
    const { error } = await supabase.auth.signOut({
      scope: 'global' // This will clear all storage including on other tabs
    });

    if (error) {
      console.error('Error signing out:', error);

      // If there's an error with the token, try to clear storage manually
      if (error.message.includes('Invalid Refresh Token') ||
          error.message.includes('JWT expired') ||
          error.message.includes('token')) {
        try {
          // Clear all Supabase-related items from localStorage
          Object.keys(localStorage).forEach(key => {
            if (key.includes('supabase') || key.includes('secquiz-auth')) {
              localStorage.removeItem(key);
            }
          });

          console.log('Manually cleared auth storage after token error');
          return { error: null }; // Return success after manual cleanup
        } catch (storageError) {
          console.warn('Error clearing localStorage:', storageError);
        }
      }

      return { error };
    }

    return { error: null };
  } catch (error) {
    console.error('Error signing out:', error);
    return { error };
  }
}
