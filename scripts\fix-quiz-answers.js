import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { createInterface } from 'readline';

dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Topic ID for CISSP Fundamentals
const CISSP_TOPIC_ID = '1de31e9d-97e5-4ee7-9e89-968615011645';

async function getQuestionsForTopic(topicId) {
  const { data, error } = await supabase
    .from('questions')
    .select('*')
    .eq('topic_id', topicId);

  if (error) {
    console.error('Error fetching questions:', error);
    return [];
  }

  return data;
}

async function updateQuestionCorrectAnswer(questionId, correctAnswer) {
  const { data, error } = await supabase
    .from('questions')
    .update({ correct_answer: correctAnswer })
    .eq('id', questionId)
    .select();

  if (error) {
    console.error(`Error updating question ${questionId}:`, error);
    return false;
  }

  return true;
}

function analyzeQuestion(question) {
  const { id, question_text, options, correct_answer, explanation } = question;

  // Convert options to array for easier analysis
  let optionsArray = [];
  let optionKeys = [];

  if (typeof options === 'object') {
    // Handle both numeric and letter keys
    if ('A' in options || 'B' in options) {
      // Letter keys (A, B, C, D)
      optionKeys = Object.keys(options).filter(key => ['A', 'B', 'C', 'D'].includes(key));
    } else {
      // Numeric keys (0, 1, 2, 3)
      optionKeys = Object.keys(options).filter(key => ['0', '1', '2', '3'].includes(key));
    }

    optionsArray = optionKeys.map(key => options[key]);
  }

  // Check if the current correct answer matches the explanation
  const currentOptionText = options[correct_answer];

  // Simple analysis based on explanation text
  const result = {
    id,
    question_text,
    current_answer: correct_answer,
    current_answer_text: currentOptionText,
    explanation,
    needs_review: false,
    suggested_answer: null,
    suggested_answer_text: null,
    confidence: 'high',
    reasoning: ''
  };

  // Special case handling for "NOT" questions
  if (question_text.toUpperCase().includes('NOT') || question_text.toUpperCase().includes('EXCEPT')) {
    // For "NOT" questions, we need to find which option is NOT mentioned in the explanation
    const explanationLower = explanation.toLowerCase();

    // Find options that ARE mentioned in the explanation
    const mentionedOptions = new Set();

    for (const key of optionKeys) {
      const optionText = options[key].toLowerCase();
      // Check if this option or key terms from it are mentioned in the explanation
      const optionWords = optionText.split(/\s+/).filter(word => word.length > 3);

      let isOptionMentioned = false;

      // Check if the option text is directly mentioned
      if (explanationLower.includes(optionText)) {
        isOptionMentioned = true;
      } else {
        // Check if key words from the option are mentioned
        for (const word of optionWords) {
          if (explanationLower.includes(word)) {
            isOptionMentioned = true;
            break;
          }
        }
      }

      if (isOptionMentioned) {
        mentionedOptions.add(key);
      }
    }

    // The correct answer should be the option that is NOT mentioned
    const notMentionedOptions = optionKeys.filter(key => !mentionedOptions.has(key));

    if (notMentionedOptions.length === 1 && notMentionedOptions[0] !== correct_answer) {
      const suggestedKey = notMentionedOptions[0];
      result.needs_review = true;
      result.suggested_answer = suggestedKey;
      result.suggested_answer_text = options[suggestedKey];
      result.confidence = 'high';
      result.reasoning = 'This is a NOT/EXCEPT question. The correct answer should be the option NOT mentioned in the explanation.';
      return result;
    }

    // If we couldn't find a clear NOT option, fall back to regular analysis
  }

  // Regular analysis for non-NOT questions
  // Look for keywords in explanation that match option text
  let bestMatchKey = null;
  let bestMatchScore = 0;
  let bestMatchReasoning = '';

  for (const key of optionKeys) {
    const optionText = options[key];
    if (!optionText) continue;

    // Calculate a simple match score based on word overlap
    const optionWords = optionText.toLowerCase().split(/\s+/);
    const explanationLower = explanation.toLowerCase();

    let matchScore = 0;
    let matchReasons = [];

    // Check for exact phrases
    if (explanationLower.includes(optionText.toLowerCase())) {
      matchScore += 3;
      matchReasons.push(`Option text "${optionText}" appears directly in the explanation`);
    }

    // Check for key words
    optionWords.forEach(word => {
      if (word.length > 3 && explanationLower.includes(word.toLowerCase())) {
        matchScore += 0.5;
        matchReasons.push(`Key word "${word}" from option appears in explanation`);
      }
    });

    // Special case: If explanation starts by repeating the option text
    if (explanationLower.startsWith(optionText.toLowerCase()) ||
        explanationLower.startsWith(optionText.toLowerCase().replace(/^(a|an|the) /, ''))) {
      matchScore += 2;
      matchReasons.push('Explanation begins with the option text');
    }

    if (matchScore > bestMatchScore) {
      bestMatchScore = matchScore;
      bestMatchKey = key;
      bestMatchReasoning = matchReasons.join('. ');
    }
  }

  // If we found a better match and it's different from current answer
  if (bestMatchKey && bestMatchKey !== correct_answer && bestMatchScore > 0) {
    result.needs_review = true;
    result.suggested_answer = bestMatchKey;
    result.suggested_answer_text = options[bestMatchKey];
    result.confidence = bestMatchScore > 2 ? 'high' : 'medium';
    result.reasoning = bestMatchReasoning;
  }

  return result;
}

async function main() {
  try {
    console.log('Fetching CISSP Fundamentals questions...');
    const questions = await getQuestionsForTopic(CISSP_TOPIC_ID);
    console.log(`Found ${questions.length} questions.`);

    const analysisResults = questions.map(analyzeQuestion);
    const questionsNeedingReview = analysisResults.filter(result => result.needs_review);

    console.log(`\n${questionsNeedingReview.length} questions need review.`);

    for (const result of questionsNeedingReview) {
      console.log('\n' + '-'.repeat(80));
      console.log(`QUESTION: ${result.question_text}`);

      // Display all options
      console.log('\nOPTIONS:');
      const options = result.options;
      let optionKeys = [];

      if (typeof options === 'object') {
        // Handle both numeric and letter keys
        if ('A' in options || 'B' in options) {
          // Letter keys (A, B, C, D)
          optionKeys = Object.keys(options).filter(key => ['A', 'B', 'C', 'D'].includes(key));
        } else {
          // Numeric keys (0, 1, 2, 3)
          optionKeys = Object.keys(options).filter(key => ['0', '1', '2', '3'].includes(key));
        }
      }

      for (const key of optionKeys) {
        const isCurrentAnswer = key === result.current_answer;
        const isSuggestedAnswer = key === result.suggested_answer;
        const marker = isCurrentAnswer ? '(Current)' : isSuggestedAnswer ? '(Suggested)' : '';
        console.log(`  ${key}: ${options[key]} ${marker}`);
      }

      console.log(`\nCURRENT ANSWER: ${result.current_answer} - "${result.current_answer_text}"`);
      console.log(`SUGGESTED ANSWER: ${result.suggested_answer} - "${result.suggested_answer_text}"`);
      console.log(`EXPLANATION: ${result.explanation}`);
      console.log(`CONFIDENCE: ${result.confidence}`);
      console.log(`REASONING: ${result.reasoning}`);

      // Ask for confirmation before updating
      const readline = createInterface({
        input: process.stdin,
        output: process.stdout
      });

      const answer = await new Promise(resolve => {
        readline.question('Update this question? (y/n/skip): ', resolve);
      });

      if (answer.toLowerCase() === 'y') {
        const success = await updateQuestionCorrectAnswer(result.id, result.suggested_answer);
        console.log(success ? 'Updated successfully!' : 'Update failed.');
      } else if (answer.toLowerCase() === 'skip') {
        console.log('Skipping remaining questions.');
        readline.close();
        break;
      } else {
        console.log('Skipped.');
      }

      readline.close();
    }

    console.log('\nAnalysis complete!');

  } catch (error) {
    console.error('Error:', error);
  }
}

main();
