import { useState, useEffect, useRef } from "react";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import BottomNavigation from "@/components/BottomNavigation";
import DesktopSideNav from "@/components/DesktopSideNav";
import {
  FileText, Search, FilterX, Plus, Edit,
  Trash2, DownloadCloud, RefreshCw,
  BookOpen, LogOut, ChevronLeft, ChevronRight, Loader2,
  Crown, Shield, ChevronDown
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/hooks/use-auth";
import { supabase } from "@/integrations/supabase/client";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";
import { subscriptionPlans } from "@/utils/paystack";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { useAdminTopics, useAdminQuestions, Topic, Question } from "@/hooks/use-admin";
import { TopicQuestions } from "@/components/admin/TopicQuestions";
import { useAdminUsers } from "@/hooks/use-admin-users";
import AdminTopicForm from "@/components/AdminTopicForm";
import AdminQuestionImport from "@/components/AdminQuestionImport";
import { CSVImport } from "@/components/admin/CSVImport";
import AdminQuestionForm from "@/components/AdminQuestionForm";
import { FeedbackManagement } from "@/components/admin/FeedbackManagement";
import { LearningMaterialsManagement } from "@/components/admin/LearningMaterialsManagement";
import { checkAdminUsersTable, ensureUserProfilesTable } from "@/utils/use-existing-tables";
import { fixGetAllUsersSimple, fixDeleteUserFunction } from "@/utils/execute-sql";
import { fixIsAdminFunction } from "@/utils/fix-is-admin-function";

const AdminDashboard = () => {
  const { toast } = useToast();
  const { signOut } = useAuth();
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState("topics");
  const [selectedTopic, setSelectedTopic] = useState<Topic | null>(null);
  const [showTopicForm, setShowTopicForm] = useState(false);
  const [showQuestionImport, setShowQuestionImport] = useState(false);
  const [showCSVImport, setShowCSVImport] = useState(false);
  const [showQuestionForm, setShowQuestionForm] = useState(false);
  // const [showUserForm, setShowUserForm] = useState(false); // Unused - keeping for future reference
  const [editingTopic, setEditingTopic] = useState<any>(null);
  const [editingQuestion, setEditingQuestion] = useState<any>(null);
  // const [editingUser, setEditingUser] = useState(null); // Unused - keeping for future reference

  // Pagination state
  const [topicsPage, setTopicsPage] = useState(1);
  const [questionsPage, setQuestionsPage] = useState(1);
  const [usersPage, setUsersPage] = useState(1);
  const itemsPerPage = 10;

  const {
    topics,
    loading: topicsLoading,
    error: topicsError,
    refreshTopics,
    deleteTopic
  } = useAdminTopics();

  const {
    questions,
    loading: questionsLoading,
    error: questionsError,
    refreshQuestions,
    deleteQuestion
  } = useAdminQuestions();

  // Get the admin users hook
  const {
    users: initialUsers,
    loading: usersLoading,
    error: usersError,
    fetchUsers: refreshUsersFromDB,
    updateUserSubscription,
    updateUserAdminStatus,
    deleteUser
  } = useAdminUsers();

  // Create a local state for users to manage updates more efficiently
  const [users, setUsers] = useState<any[]>([]);

  // Create state for premium, expired, and admin user counts
  const [premiumUsersCount, setPremiumUsersCount] = useState<number>(0);
  const [expiredUsersCount, setExpiredUsersCount] = useState<number>(0);
  const [adminUsersCount, setAdminUsersCount] = useState<number>(0);

  // Create refs for the user counts
  const premiumUsersCountRef = useRef<HTMLParagraphElement>(null);
  const expiredUsersCountRef = useRef<HTMLParagraphElement>(null);
  const adminUsersCountRef = useRef<HTMLParagraphElement>(null);

  // Filter state for users tab
  const [userFilter, setUserFilter] = useState<'all' | 'premium' | 'expired' | 'free'>('all');

  // Sync users state with the data from the hook
  useEffect(function syncUsersWithInitialData() {
    // Only update if we have data and it's different from current state
    if (initialUsers && initialUsers.length > 0) {
      console.log('Syncing users with initialUsers:', initialUsers);
      console.log('Premium users in initialUsers:', initialUsers.filter(u => u.is_subscribed).length);
      setUsers(initialUsers);
    }

  }, [initialUsers]);

  // Update the user counts whenever users state changes
  useEffect(function updateUserCounters() {
    if (users.length === 0) return; // Skip if no users loaded yet

    // Calculate the counts
    const premiumCount = users.filter(u => u.is_subscribed).length;
    const expiredCount = users.filter(u => u.subscription_status === 'expired').length;
    const adminCount = users.filter(u => u.is_admin).length;

    // Log user counts for debugging
    console.log('Premium users count:', premiumCount);
    console.log('Expired users count:', expiredCount);
    console.log('Premium users:', users.filter(u => u.is_subscribed).map(u => u.email));
    console.log('Expired users:', users.filter(u => u.subscription_status === 'expired').map(u => u.email));

    // Update the state variables
    setPremiumUsersCount(premiumCount);
    setExpiredUsersCount(expiredCount);
    setAdminUsersCount(adminCount);

    // Force update the DOM elements after state update
    setTimeout(() => {
      // Update using refs (preferred method)
      if (premiumUsersCountRef.current) {
        premiumUsersCountRef.current.textContent = premiumCount.toString();
      }

      if (expiredUsersCountRef.current) {
        expiredUsersCountRef.current.textContent = expiredCount.toString();
      }

      if (adminUsersCountRef.current) {
        adminUsersCountRef.current.textContent = adminCount.toString();
      }

      // Also update using getElementById as a fallback
      const premiumCountElement = document.getElementById('premium-users-count');
      if (premiumCountElement) {
        premiumCountElement.textContent = premiumCount.toString();
      }

      const expiredCountElement = document.getElementById('expired-users-count');
      if (expiredCountElement) {
        expiredCountElement.textContent = expiredCount.toString();
      }

      const adminCountElement = document.getElementById('admin-users-count');
      if (adminCountElement) {
        adminCountElement.textContent = adminCount.toString();
      }

      console.log('User counts updated in DOM');
    }, 0);
  }, [users]);

  // Create a refreshUsers function that updates both the DB and local state
  const refreshUsers = async () => {
    try {
      await refreshUsersFromDB();
      // Note: We don't need to manually update setUsers here because the useEffect will handle it
      // when initialUsers changes after refreshUsersFromDB() completes
    } catch (error) {
      console.error('Error refreshing users:', error);
    }
  };

  // Function to make a specific user premium
  const makeSpecificUserPremium = async (email: string, planId: string = 'pro') => {
    try {
      // Find the user by email
      const userToUpdate = users.find(user => user.email === email);

      if (!userToUpdate) {
        toast({
          title: "User not found",
          description: `No user found with email ${email}`,
          variant: "destructive",
        });
        return;
      }

      // If user is already premium, show a message
      if (userToUpdate.is_subscribed) {
        toast({
          title: "User already premium",
          description: `User ${email} is already a premium user`,
        });
        return;
      }

      // Make the user premium
      await handleToggleSubscription(userToUpdate, planId);

      toast({
        title: "User made premium",
        description: `User ${email} has been successfully made a premium user with the ${subscriptionPlans[planId].name} plan`,
      });
    } catch (error) {
      console.error('Error making user premium:', error);
      toast({
        title: "Error making user premium",
        description: error.message || "An unknown error occurred",
        variant: "destructive",
      });
    }
  };

  const filteredTopics = topics.filter(topic =>
    topic.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (topic.description && topic.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const filteredQuestions = questions.filter(question =>
    question.question_text.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (question.explanation && question.explanation.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const filteredUsers = users.filter(user => {
    // First apply search filter
    const matchesSearch = user.email.toLowerCase().includes(searchTerm.toLowerCase());
    if (!matchesSearch) return false;

    // Then apply status filter
    if (userFilter === 'all') return true;
    if (userFilter === 'premium') return user.is_subscribed;
    if (userFilter === 'expired') return user.subscription_status === 'expired';
    if (userFilter === 'free') return !user.is_subscribed && user.subscription_status !== 'expired';

    return true;
  });

  // Paginated data
  const paginatedTopics = filteredTopics.slice(
    (topicsPage - 1) * itemsPerPage,
    topicsPage * itemsPerPage
  );

  const paginatedQuestions = filteredQuestions.slice(
    (questionsPage - 1) * itemsPerPage,
    questionsPage * itemsPerPage
  );

  const paginatedUsers = filteredUsers.slice(
    (usersPage - 1) * itemsPerPage,
    usersPage * itemsPerPage
  );

  const topicsPageCount = Math.ceil(filteredTopics.length / itemsPerPage);
  const questionsPageCount = Math.ceil(filteredQuestions.length / itemsPerPage);
  const usersPageCount = Math.ceil(filteredUsers.length / itemsPerPage);

  const handleDeleteTopic = async (id: string) => {
    const result = await deleteTopic(id);
    if (result.success) {
      toast({
        title: "Topic deleted",
        description: "The topic has been successfully deleted",
      });
    } else {
      toast({
        title: "Error deleting topic",
        description: result.error,
        variant: "destructive",
      });
    }
  };

  const handleDeleteQuestion = async (id: string) => {
    const result = await deleteQuestion(id);
    if (result.success) {
      toast({
        title: "Question deleted",
        description: "The question has been successfully deleted",
      });
    } else {
      toast({
        title: "Error deleting question",
        description: result.error,
        variant: "destructive",
      });
    }
  };

  const handleEditTopic = (topic: any) => {
    setEditingTopic(topic);
    setShowTopicForm(true);
  };

  const handleEditQuestion = (question: any) => {
    setEditingQuestion(question);
    setShowQuestionForm(true);
  };

  const handleQuestionFormSuccess = () => {
    setShowQuestionForm(false);
    setEditingQuestion(null);
    refreshQuestions();
  };

  // User form functions removed - not needed for current implementation

  const handleDeleteUser = async (id: string) => {
    try {
      // Show a confirmation dialog before deleting
      if (!confirm("Are you sure you want to delete this user? This action cannot be undone.")) {
        return;
      }

      // Show loading toast
      toast({
        title: "Deleting user",
        description: "Please wait while we delete the user...",
      });

      // Try to fix the is_admin function first to ensure admin check works
      await fixIsAdminFunction();

      // Then fix the delete_user function to ensure it's working
      await fixDeleteUserFunction();

      // Now attempt to delete the user
      const result = await deleteUser(id);

      if (result.success) {
        // Update the local state by removing the deleted user
        const updatedUsers = users.filter(user => user.id !== id);

        // Log the updated users for debugging
        console.log('Updated users after deletion:', updatedUsers);
        console.log('Premium users after deletion:', updatedUsers.filter(u => u.is_subscribed).map(u => u.email));

        // Force update the users state
        setUsers(updatedUsers);

        // Update the premium and admin count states
        const newPremiumCount = updatedUsers.filter(u => u.is_subscribed).length;
        const newAdminCount = updatedUsers.filter(u => u.is_admin).length;
        setPremiumUsersCount(newPremiumCount);
        setAdminUsersCount(newAdminCount);
        console.log('New premium count after deletion:', newPremiumCount);

        // Update the premium and admin counts in the UI
        // Use both ref and getElementById for maximum compatibility
        if (premiumUsersCountRef.current) {
          premiumUsersCountRef.current.textContent = newPremiumCount.toString();
        }

        if (adminUsersCountRef.current) {
          adminUsersCountRef.current.textContent = newAdminCount.toString();
        }

        const premiumCountElement = document.getElementById('premium-users-count');
        const adminCountElement = document.getElementById('admin-users-count');

        if (premiumCountElement) {
          premiumCountElement.textContent = newPremiumCount.toString();
        }

        if (adminCountElement) {
          adminCountElement.textContent = newAdminCount.toString();
        }

        toast({
          title: "User deleted",
          description: "The user has been successfully deleted",
        });

        // Force a refresh of the users list to ensure everything is in sync
        setTimeout(() => {
          refreshUsers();
        }, 1000);
      } else {
        // If there was an error, show a more detailed error message
        console.error("Error deleting user:", result.error);

        toast({
          title: "Error deleting user",
          description: result.error || "An unknown error occurred while deleting the user",
          variant: "destructive",
        });

        // Suggest fixing the delete user function
        toast({
          title: "Try fixing the delete user function",
          description: "Click the 'Fix Delete User Function' button in the Special Actions section and try again.",
          duration: 10000,
        });
      }
    } catch (error) {
      console.error("Unexpected error in handleDeleteUser:", error);

      toast({
        title: "Error deleting user",
        description: error.message || "An unexpected error occurred",
        variant: "destructive",
      });
    }
  };

  const handleRunMigrations = async () => {
    try {
      toast({
        title: "Fixing database functions and tables",
        description: "Please wait while we update the database...",
      });

      // Fix the get_all_users_simple function
      const fixResult = await fixGetAllUsersSimple();

      if (!fixResult.success) {
        toast({
          title: "Function update failed",
          description: fixResult.error || "Could not update the get_all_users_simple function",
          variant: "destructive",
        });
        return;
      }

      // Fix the delete_user function
      const fixDeleteResult = await fixDeleteUserFunction();

      if (!fixDeleteResult.success) {
        toast({
          title: "Delete user function update failed",
          description: fixDeleteResult.error || "Could not update the delete_user function",
          variant: "destructive",
        });
        return;
      }

      // Fix the is_admin function
      const fixIsAdminResult = await fixIsAdminFunction();

      if (!fixIsAdminResult.success) {
        toast({
          title: "Admin check function update failed",
          description: fixIsAdminResult.error || "Could not update the is_admin function",
          variant: "destructive",
        });
        return;
      }

      // Ensure the user_profiles table exists
      const profilesResult = await ensureUserProfilesTable();

      if (!profilesResult.success) {
        console.warn("Warning: Could not ensure user_profiles table exists:", profilesResult.error);
        // Continue anyway, as this is not critical
      }

      // Check the admin_users table structure
      const result = await checkAdminUsersTable();

      if (result.success) {
        toast({
          title: "Database update completed",
          description: `Updated database functions and tables. Found admin_users table with columns: ${result.columns.join(', ')}. Refreshing data...`,
        });

        // Refresh all data
        refreshTopics();
        refreshQuestions();
        refreshUsers();
      } else {
        toast({
          title: "Database check failed",
          description: result.error || "Could not check admin_users table structure",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error updating database:", error);
      toast({
        title: "Database update failed",
        description: "An unexpected error occurred. Please check the console for details.",
        variant: "destructive",
      });
    }
  };

  const handleToggleSubscription = async (user: any, planId = 'pro') => {
    const newStatus = !user.is_subscribed;

    // Set expiration date based on plan type
    let expiresAt = null;
    if (newStatus) {
      const daysToAdd = planId === 'elite' ? 365 : 7; // Elite: 1 year, Basic/Pro: 1 week
      expiresAt = new Date(Date.now() + daysToAdd * 24 * 60 * 60 * 1000).toISOString();
    }

    const result = await updateUserSubscription(user.id, newStatus, expiresAt, planId);
    if (result.success) {
      // Update the user in the local state
      const updatedUsers = users.map(u => {
        if (u.id === user.id) {
          return { ...u, is_subscribed: newStatus, subscription_expires_at: expiresAt };
        }
        return u;
      });

      // Log the updated users for debugging
      console.log('Updated users after subscription change:', updatedUsers);
      console.log('Premium users after update:', updatedUsers.filter(u => u.is_subscribed).map(u => u.email));

      // Force update the users state
      setUsers(updatedUsers);

      // Update the premium users count state
      const newPremiumCount = updatedUsers.filter(u => u.is_subscribed).length;
      setPremiumUsersCount(newPremiumCount);
      console.log('New premium count:', newPremiumCount);

      // Update the premium users count in the UI for immediate feedback
      // Use both ref and getElementById for maximum compatibility
      if (premiumUsersCountRef.current) {
        premiumUsersCountRef.current.textContent = newPremiumCount.toString();
      }

      const premiumCountElement = document.getElementById('premium-users-count');
      if (premiumCountElement) {
        premiumCountElement.textContent = newPremiumCount.toString();
      }

      const planName = newStatus ? subscriptionPlans[planId].name : '';

      toast({
        title: newStatus ? "Successfully subscribed" : "Successfully unsubscribed",
        description: newStatus
          ? `The user has been successfully subscribed to the ${planName} plan`
          : "The user has been successfully unsubscribed from premium",
      });

      // Force a refresh of the users list to ensure everything is in sync
      setTimeout(() => {
        refreshUsers();
      }, 1000);
    } else {
      toast({
        title: "Error updating subscription",
        description: result.error,
        variant: "destructive",
      });
    }
  };

  const handleToggleAdminStatus = async (user: any) => {
    const newStatus = !user.is_admin;
    const result = await updateUserAdminStatus(user.id, newStatus);
    if (result.success) {
      // Update the user in the local state
      const updatedUsers = users.map(u => {
        if (u.id === user.id) {
          return { ...u, is_admin: newStatus };
        }
        return u;
      });

      // Log the updated users for debugging
      console.log('Updated users after admin status change:', updatedUsers);
      console.log('Admin users after update:', updatedUsers.filter(u => u.is_admin).map(u => u.email));

      // Force update the users state
      setUsers(updatedUsers);

      // Update the admin users count state
      const newAdminCount = updatedUsers.filter(u => u.is_admin).length;
      setAdminUsersCount(newAdminCount);
      console.log('New admin count:', newAdminCount);

      // Update the admin users count in the UI for immediate feedback
      // Use both ref and getElementById for maximum compatibility
      if (adminUsersCountRef.current) {
        adminUsersCountRef.current.textContent = newAdminCount.toString();
      }

      const adminCountElement = document.getElementById('admin-users-count');
      if (adminCountElement) {
        adminCountElement.textContent = newAdminCount.toString();
      }

      toast({
        title: newStatus ? "Successfully subscribed" : "Successfully unsubscribed",
        description: newStatus
          ? "The user has been successfully subscribed as admin"
          : "The user has been successfully unsubscribed from admin",
      });

      // Force a refresh of the users list to ensure everything is in sync
      setTimeout(() => {
        refreshUsers();
      }, 1000);
    } else {
      toast({
        title: "Error updating admin status",
        description: result.error,
        variant: "destructive",
      });
    }
  };

  const handleTopicFormSuccess = () => {
    setShowTopicForm(false);
    setEditingTopic(null);
    refreshTopics();
  };

  const handleQuestionImportSuccess = () => {
    setShowQuestionImport(false);
    refreshQuestions();
  };

  const handleCSVImportSuccess = () => {
    setShowCSVImport(false);
    refreshQuestions();
  };

  // Function to handle data export
  const handleExportData = () => {
    try {
      // Determine which data to export based on active tab
      const dataToExport = activeTab === "topics" ? filteredTopics : filteredQuestions;

      // Convert data to JSON string
      const jsonData = JSON.stringify(dataToExport, null, 2);

      // Create a blob with the data
      const blob = new Blob([jsonData], { type: 'application/json' });

      // Create a URL for the blob
      const url = URL.createObjectURL(blob);

      // Create a temporary anchor element
      const a = document.createElement('a');
      a.href = url;
      a.download = activeTab === "topics" ? 'secquiz-topics.json' : 'secquiz-questions.json';

      // Trigger the download
      document.body.appendChild(a);
      a.click();

      // Clean up
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast({
        title: "Export successful",
        description: `${activeTab === "topics" ? "Topics" : "Questions"} exported successfully.`,
      });
    } catch (error) {
      console.error("Error exporting data:", error);
      toast({
        title: "Export failed",
        description: `Failed to export ${activeTab === "topics" ? "topics" : "questions"}: ${error.message}`,
        variant: "destructive",
      });
    }
  };

  const handleLogout = async () => {
    try {
      await signOut();
      toast({
        title: "Logged out",
        description: "You have been successfully logged out.",
      });
      // Navigate to homepage after logout
      navigate("/");
    } catch (error) {
      toast({
        title: "Logout failed",
        description: "An error occurred while logging out.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="flex min-h-screen cyber-grid-bg">
      <DesktopSideNav />

      <div className="flex flex-col flex-1 pb-16 w-full md:max-w-[calc(100%-16rem)]">
        <header className="p-4 border-b bg-background/95 backdrop-blur-sm sticky top-0 z-10">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <img src="/secquiz-logo.svg" alt="SecQuiz Logo" className="h-6 w-6" />
              <h1 className="text-lg font-medium">Admin Dashboard</h1>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleLogout}
              className="text-muted-foreground"
            >
              <LogOut className="h-4 w-4 mr-1" />
              Logout
            </Button>
          </div>
        </header>

        <div className="p-4">
          {/* Special Actions */}
          <div className="mb-4">
            <Card className="cyber-card p-3">
              <div className="flex flex-col space-y-2">
                <h3 className="text-sm font-medium">Special Actions</h3>
                <div className="flex flex-wrap gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-green-500 text-green-600 hover:bg-green-50 premium-btn"
                    onClick={() => makeSpecificUserPremium('<EMAIL>', 'pro')}
                  >
                    <Crown className="h-3 w-3 mr-1" />
                    Make <EMAIL> Premium
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-red-500 text-red-600 hover:bg-red-50 font-medium"
                    onClick={async () => {
                      try {
                        toast({
                          title: "Fixing delete user function",
                          description: "Please wait while we fix the delete user function...",
                        });

                        const result = await fixDeleteUserFunction();

                        if (result.success) {
                          toast({
                            title: "Delete user function fixed",
                            description: "The delete user function has been fixed successfully. You should now be able to delete users.",
                            duration: 5000,
                          });
                        } else {
                          toast({
                            title: "Error fixing delete user function",
                            description: result.error || "An unknown error occurred",
                            variant: "destructive",
                          });
                        }
                      } catch (error) {
                        console.error("Error fixing delete user function:", error);
                        toast({
                          title: "Error fixing delete user function",
                          description: error.message || "An unknown error occurred",
                          variant: "destructive",
                        });
                      }
                    }}
                  >
                    <FileText className="h-3 w-3 mr-1" />
                    Fix Delete User Function
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-blue-500 text-blue-600 hover:bg-blue-50 font-medium"
                    onClick={async () => {
                      try {
                        toast({
                          title: "Fixing admin check function",
                          description: "Please wait while we fix the is_admin function...",
                        });

                        const result = await fixIsAdminFunction();

                        if (result.success) {
                          toast({
                            title: "Admin check function fixed",
                            description: "The is_admin function has been fixed successfully. You should now be able to delete users and perform other admin actions.",
                            duration: 5000,
                          });
                        } else {
                          toast({
                            title: "Error fixing admin check function",
                            description: result.error || "An unknown error occurred",
                            variant: "destructive",
                          });
                        }
                      } catch (error) {
                        console.error("Error fixing admin check function:", error);
                        toast({
                          title: "Error fixing admin check function",
                          description: error.message || "An unknown error occurred",
                          variant: "destructive",
                        });
                      }
                    }}
                  >
                    <Shield className="h-3 w-3 mr-1" />
                    Fix Admin Check Function
                  </Button>
                </div>
              </div>
            </Card>
          </div>

          {/* Stats Overview */}
          <div className="grid grid-cols-5 gap-3 mb-4">
            <Card className="cyber-card p-3">
              <div className="flex items-center">
                <div className="h-10 w-10 rounded-full bg-cyber-primary/10 flex items-center justify-center mr-3">
                  <BookOpen className="h-5 w-5 text-cyber-primary" />
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">Total Topics</p>
                  <p className="text-lg font-bold">{topics.length}</p>
                </div>
              </div>
            </Card>
            <Card className="cyber-card p-3">
              <div className="flex items-center">
                <div className="h-10 w-10 rounded-full bg-cyber-accent/10 flex items-center justify-center mr-3">
                  <FileText className="h-5 w-5 text-cyber-accent" />
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">Total Questions</p>
                  <p className="text-lg font-bold">{questions.length}</p>
                </div>
              </div>
            </Card>
            <Card className="cyber-card p-3">
              <div className="flex items-center">
                <div className="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center mr-3">
                  <Crown className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">Premium Users</p>
                  <p className="text-lg font-bold" id="premium-users-count" ref={premiumUsersCountRef}>{premiumUsersCount}</p>
                </div>
              </div>
            </Card>
            <Card className="cyber-card p-3">
              <div className="flex items-center">
                <div className="h-10 w-10 rounded-full bg-amber-100 flex items-center justify-center mr-3">
                  <Crown className="h-5 w-5 text-amber-600" />
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">Expired Subs</p>
                  <p className="text-lg font-bold" id="expired-users-count" ref={expiredUsersCountRef}>{expiredUsersCount}</p>
                </div>
              </div>
            </Card>
            <Card className="cyber-card p-3">
              <div className="flex items-center">
                <div className="h-10 w-10 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                  <Shield className="h-5 w-5 text-purple-600" />
                </div>
                <div>
                  <p className="text-xs text-muted-foreground">Admin Users</p>
                  <p className="text-lg font-bold" id="admin-users-count" ref={adminUsersCountRef}>{adminUsersCount}</p>
                </div>
              </div>
            </Card>
          </div>

          {/* Tabs and Content */}
          <Tabs
            defaultValue="topics"
            className="mb-6"
            onValueChange={(value) => {
              setActiveTab(value);
              // Reset pagination when tab changes
              if (value === "topics") {
                setTopicsPage(1);
              } else {
                setQuestionsPage(1);
              }
            }}
          >
            <TabsList className="grid grid-cols-5 mb-4">
              <TabsTrigger value="topics">Topics</TabsTrigger>
              <TabsTrigger value="questions">Questions</TabsTrigger>
              <TabsTrigger value="learning">Learning</TabsTrigger>
              <TabsTrigger value="users">Users</TabsTrigger>
              <TabsTrigger value="feedback">Feedback</TabsTrigger>
            </TabsList>

            <div className="flex items-center gap-2 mb-4">
              <div className="relative flex-1">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder={`Search ${activeTab}...`}
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => {
                    setSearchTerm(e.target.value);
                    // Reset pagination when search changes
                    if (activeTab === "topics") {
                      setTopicsPage(1);
                    } else {
                      setQuestionsPage(1);
                    }
                  }}
                />
              </div>
              <Button
                variant="outline"
                size="icon"
                onClick={() => {
                  setSearchTerm("");
                  // Reset pagination when clearing search
                  if (activeTab === "topics") {
                    setTopicsPage(1);
                  } else {
                    setQuestionsPage(1);
                  }
                }}
              >
                <FilterX className="h-4 w-4" />
              </Button>
              {activeTab === "topics" ? (
                <Button
                  size="icon"
                  className="bg-cyber-primary hover:bg-cyber-primary/90"
                  onClick={() => {
                    setEditingTopic(null);
                    setShowTopicForm(true);
                  }}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              ) : activeTab === "questions" ? (
                <div className="flex gap-2">
                  <Button
                    className="bg-cyber-primary hover:bg-cyber-primary/90"
                    onClick={() => {
                      setEditingQuestion(null);
                      setShowQuestionForm(true);
                    }}
                  >
                    <Plus className="h-4 w-4 mr-1" /> New
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline">
                        <Plus className="h-4 w-4 mr-1" /> Import
                        <ChevronDown className="h-3 w-3 ml-1" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => setShowQuestionImport(true)}>
                        Text Format
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => setShowCSVImport(true)}>
                        CSV Format
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              ) : (
                <Button
                  className="bg-cyber-primary hover:bg-cyber-primary/90"
                  onClick={() => refreshUsers()}
                >
                  <RefreshCw className="h-4 w-4 mr-1" /> Refresh
                </Button>
              )}
            </div>

            <TabsContent value="topics" className="mt-0">
              {selectedTopic ? (
                <TopicQuestions
                  topic={selectedTopic}
                  onBack={() => setSelectedTopic(null)}
                  onEditQuestion={handleEditQuestion}
                />
              ) : (
                <Card className="cyber-card overflow-hidden">
                {topicsLoading ? (
                  <div className="p-4 text-center">
                    <Loader2 className="h-8 w-8 text-cyber-primary animate-spin mx-auto mb-2" />
                    <p>Loading topics...</p>
                  </div>
                ) : topicsError ? (
                  <div className="p-4 text-center">
                    <div className="bg-red-50 border border-red-200 rounded-md p-4 max-w-lg mx-auto">
                      <h3 className="text-red-800 font-medium mb-2">Error Loading Topics</h3>
                      <p className="text-red-700 text-sm mb-3">{topicsError}</p>
                      <Button
                        variant="outline"
                        className="mt-3"
                        onClick={refreshTopics}
                      >
                        <RefreshCw className="h-4 w-4 mr-2" /> Try Again
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b bg-muted/50">
                          <th className="text-left font-medium p-3">Title</th>
                          <th className="text-left font-medium p-3">Status</th>
                          <th className="text-left font-medium p-3">Actions</th>
                        </tr>
                      </thead>
                      <tbody className="divide-y">
                        {paginatedTopics.length > 0 ? (
                          paginatedTopics.map((topic) => (
                            <tr key={topic.id} className="hover:bg-muted/20">
                              <td className="p-3">
                                <div>
                                  <div className="flex items-center">
                                    <div className="h-8 w-8 rounded-full bg-muted flex items-center justify-center mr-2">
                                      <img src="/secquiz-logo.svg" alt="SecQuiz Logo" className="h-5 w-5" />
                                    </div>
                                    <p className="font-medium">{topic.title}</p>
                                  </div>
                                  <p className="text-xs text-muted-foreground mt-1">
                                    {topic.description || "No description"}
                                  </p>
                                </div>
                              </td>
                              <td className="p-3">
                                <div className={`px-2 py-0.5 rounded-full text-xs font-medium inline-block
                                              ${topic.is_active ? "bg-green-100 text-green-700" : "bg-orange-100 text-orange-700"}`}>
                                  {topic.is_active ? "Active" : "Inactive"}
                                </div>
                                <div className={`px-2 py-0.5 rounded-full text-xs font-medium inline-block ml-1
                                              ${topic.difficulty === "easy" ? "bg-blue-100 text-blue-700" :
                                                topic.difficulty === "medium" ? "bg-purple-100 text-purple-700" :
                                                "bg-red-100 text-red-700"}`}>
                                  {topic.difficulty}
                                </div>
                              </td>
                              <td className="p-3">
                                <div className="flex gap-1">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    className="h-8 px-2 text-xs"
                                    onClick={() => setSelectedTopic(topic)}
                                  >
                                    View Questions
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => handleEditTopic(topic)}
                                  >
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => handleDeleteTopic(topic.id)}
                                  >
                                    <Trash2 className="h-4 w-4 text-red-500" />
                                  </Button>
                                </div>
                              </td>
                            </tr>
                          ))
                        ) : (
                          <tr>
                            <td colSpan={3} className="p-4 text-center text-muted-foreground">
                              {searchTerm
                                ? "No topics found matching your search."
                                : "No topics found. Create your first topic."}
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                    {topicsPageCount > 1 && (
                      <div className="flex items-center justify-between px-4 py-3 border-t">
                        <div className="text-sm text-muted-foreground">
                          Showing {((topicsPage - 1) * itemsPerPage) + 1} to {Math.min(topicsPage * itemsPerPage, filteredTopics.length)} of {filteredTopics.length} topics
                        </div>
                        <div className="flex gap-1">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setTopicsPage(p => Math.max(1, p - 1))}
                            disabled={topicsPage === 1}
                          >
                            <ChevronLeft className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setTopicsPage(p => Math.min(topicsPageCount, p + 1))}
                            disabled={topicsPage === topicsPageCount}
                          >
                            <ChevronRight className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </Card>
              )}
            </TabsContent>

            <TabsContent value="questions" className="mt-0">
              <Card className="cyber-card overflow-hidden">
                {questionsLoading ? (
                  <div className="p-4 text-center">
                    <Loader2 className="h-8 w-8 text-cyber-primary animate-spin mx-auto mb-2" />
                    <p>Loading questions...</p>
                  </div>
                ) : questionsError ? (
                  <div className="p-4 text-center">
                    <div className="bg-red-50 border border-red-200 rounded-md p-4 max-w-lg mx-auto">
                      <h3 className="text-red-800 font-medium mb-2">Error Loading Questions</h3>
                      <p className="text-red-700 text-sm mb-3">{questionsError}</p>
                      <Button
                        variant="outline"
                        className="mt-3"
                        onClick={refreshQuestions}
                      >
                        <RefreshCw className="h-4 w-4 mr-2" /> Try Again
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b bg-muted/50">
                          <th className="text-left font-medium p-3">Question</th>
                          <th className="text-left font-medium p-3">Details</th>
                          <th className="text-left font-medium p-3">Actions</th>
                        </tr>
                      </thead>
                      <tbody className="divide-y">
                        {paginatedQuestions.length > 0 ? (
                          paginatedQuestions.map((question) => (
                            <tr key={question.id} className="hover:bg-muted/20">
                              <td className="p-3">
                                <p className="text-sm line-clamp-2">{question.question_text}</p>
                              </td>
                              <td className="p-3">
                                <div className="flex items-center gap-2">
                                  <div className={`px-2 py-0.5 rounded-full text-xs font-medium
                                                ${question.difficulty === "easy" ? "bg-green-100 text-green-700" :
                                                  question.difficulty === "medium" ? "bg-yellow-100 text-yellow-700" :
                                                  "bg-red-100 text-red-700"}`}>
                                    {question.difficulty}
                                  </div>
                                  <div className="text-xs text-muted-foreground">
                                    {Object.keys(question.options).length} options
                                  </div>
                                </div>
                              </td>
                              <td className="p-3">
                                <div className="flex gap-1">
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => handleEditQuestion(question)}
                                  >
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => handleDeleteQuestion(question.id)}
                                  >
                                    <Trash2 className="h-4 w-4 text-red-500" />
                                  </Button>
                                </div>
                              </td>
                            </tr>
                          ))
                        ) : (
                          <tr>
                            <td colSpan={3} className="p-4 text-center text-muted-foreground">
                              {searchTerm
                                ? "No questions found matching your search."
                                : "No questions found. Import some questions."}
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                    {questionsPageCount > 1 && (
                      <div className="flex items-center justify-between px-4 py-3 border-t">
                        <div className="text-sm text-muted-foreground">
                          Showing {((questionsPage - 1) * itemsPerPage) + 1} to {Math.min(questionsPage * itemsPerPage, filteredQuestions.length)} of {filteredQuestions.length} questions
                        </div>
                        <div className="flex gap-1">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setQuestionsPage(p => Math.max(1, p - 1))}
                            disabled={questionsPage === 1}
                          >
                            <ChevronLeft className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setQuestionsPage(p => Math.min(questionsPageCount, p + 1))}
                            disabled={questionsPage === questionsPageCount}
                          >
                            <ChevronRight className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </Card>
            </TabsContent>

            <TabsContent value="users" className="mt-0">
              <Card className="cyber-card overflow-hidden">
                {usersLoading ? (
                  <div className="p-4 text-center">
                    <Loader2 className="h-8 w-8 text-cyber-primary animate-spin mx-auto mb-2" />
                    <p>Loading users...</p>
                  </div>
                ) : usersError ? (
                  <div className="p-4 text-center">
                    <div className="bg-red-50 border border-red-200 rounded-md p-4 max-w-lg mx-auto">
                      <h3 className="text-red-800 font-medium mb-2">Error Loading Users</h3>
                      <p className="text-red-700 text-sm mb-3">{usersError}</p>
                      <p className="text-sm text-gray-600">This might be because the user_profiles table doesn't exist yet. Try running the database migrations or refreshing the page.</p>
                      <div className="flex gap-2 justify-center mt-3">
                        <Button
                          variant="outline"
                          onClick={refreshUsers}
                        >
                          <RefreshCw className="h-4 w-4 mr-2" /> Try Again
                        </Button>
                        <div className="flex gap-2">
                          <Button
                            className="bg-cyber-primary hover:bg-cyber-primary/90"
                            onClick={handleRunMigrations}
                          >
                            <FileText className="h-4 w-4 mr-2" /> Fix Database
                          </Button>
                          <Button
                            variant="outline"
                            onClick={() => {
                              // Run the fix_user_profiles_policy.sql script
                              toast({
                                title: "Fixing user profiles policies",
                                description: "Please wait while we fix the user profiles policies...",
                              });

                              // Execute the SQL from fix_user_profiles_policy.sql
                              fetch('/fix_user_profiles_policy.sql')
                                .then(response => response.text())
                                .then(sql => {
                                  return supabase.rpc('execute_sql', { query: sql });
                                })
                                .then(result => {
                                  if (result.error) {
                                    toast({
                                      title: "Error fixing policies",
                                      description: result.error.message,
                                      variant: "destructive",
                                    });
                                  } else {
                                    toast({
                                      title: "Policies fixed",
                                      description: "User profiles policies have been fixed. Refreshing data...",
                                    });
                                    refreshUsers();
                                  }
                                })
                                .catch(error => {
                                  console.error("Error fixing policies:", error);
                                  toast({
                                    title: "Error fixing policies",
                                    description: error.message,
                                    variant: "destructive",
                                  });
                                });
                            }}
                          >
                            <Shield className="h-4 w-4 mr-2" /> Fix Policies
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <div className="p-3 border-b flex justify-between items-center">
                      <div className="flex gap-2">
                        <Button
                          variant={userFilter === 'all' ? "default" : "outline"}
                          size="sm"
                          onClick={() => setUserFilter('all')}
                          className="h-8"
                        >
                          All Users
                        </Button>
                        <Button
                          variant={userFilter === 'premium' ? "default" : "outline"}
                          size="sm"
                          onClick={() => setUserFilter('premium')}
                          className="h-8"
                        >
                          <Crown className="h-3 w-3 mr-1" /> Premium
                        </Button>
                        <Button
                          variant={userFilter === 'expired' ? "default" : "outline"}
                          size="sm"
                          onClick={() => setUserFilter('expired')}
                          className="h-8"
                        >
                          <Crown className="h-3 w-3 mr-1" /> Expired
                        </Button>
                        <Button
                          variant={userFilter === 'free' ? "default" : "outline"}
                          size="sm"
                          onClick={() => setUserFilter('free')}
                          className="h-8"
                        >
                          Free
                        </Button>
                      </div>
                      <div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={async () => {
                            toast({
                              title: "Checking for expired subscriptions",
                              description: "Please wait while we check for expired subscriptions..."
                            });

                            try {
                              // Call the server function to check for expired subscriptions
                              const response = await fetch('/api/check-expired-subscriptions', {
                                method: 'POST'
                              });

                              if (response.ok) {
                                const result = await response.json();
                                toast({
                                  title: "Subscription check complete",
                                  description: `Found ${result.updated} expired subscriptions. Refreshing data...`
                                });
                                refreshUsers();
                              } else {
                                toast({
                                  title: "Error checking subscriptions",
                                  description: "Failed to check for expired subscriptions",
                                  variant: "destructive"
                                });
                              }
                            } catch (error) {
                              console.error("Error checking expired subscriptions:", error);
                              toast({
                                title: "Error checking subscriptions",
                                description: error.message || "An unknown error occurred",
                                variant: "destructive"
                              });
                            }
                          }}
                        >
                          Check Expired Subscriptions
                        </Button>
                      </div>
                    </div>
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b bg-muted/50">
                          <th className="text-left font-medium p-3">Email</th>
                          <th className="text-left font-medium p-3">Status</th>
                          <th className="text-left font-medium p-3">Created</th>
                          <th className="text-left font-medium p-3">Last Login</th>
                          <th className="text-left font-medium p-3">Actions</th>
                        </tr>
                      </thead>
                      <tbody className="divide-y">
                        {paginatedUsers.length > 0 ? (
                          paginatedUsers.map((user) => (
                            <tr key={user.id} className="hover:bg-muted/20">
                              <td className="p-3 font-medium">
                                {user.email}
                              </td>
                              <td className="p-3">
                                <div className="flex flex-col gap-1">
                                  {user.subscription_status === 'expired' ? (
                                    <div className="flex flex-col gap-1">
                                      <div className="px-2 py-0.5 rounded text-xs font-medium inline-flex items-center w-fit bg-amber-100 text-amber-700 border border-amber-300">
                                        <Crown className="h-3 w-3 mr-1" /> Expired
                                      </div>
                                      {user.subscription_expires_at && (
                                        <div className="text-xs text-muted-foreground">
                                          Expired: {new Date(user.subscription_expires_at).toLocaleDateString()}
                                        </div>
                                      )}
                                    </div>
                                  ) : user.is_subscribed ? (
                                    <div className="flex flex-col gap-1">
                                      <div className="px-2 py-0.5 rounded text-xs font-medium inline-flex items-center w-fit bg-green-100 text-green-700 border border-green-300">
                                        <Crown className="h-3 w-3 mr-1" /> Premium
                                      </div>
                                      {user.subscription_expires_at && (
                                        <div className="text-xs text-muted-foreground">
                                          Expires: {new Date(user.subscription_expires_at).toLocaleDateString()}
                                        </div>
                                      )}
                                    </div>
                                  ) : (
                                    <div className="px-2 py-0.5 rounded text-xs font-medium inline-flex items-center w-fit bg-gray-100 text-gray-700">
                                      Free
                                    </div>
                                  )}
                                  {user.is_admin && (
                                    <div className="px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-700 inline-flex items-center w-fit mt-1">
                                      <Shield className="h-3 w-3 mr-1" /> Admin
                                    </div>
                                  )}
                                </div>
                              </td>
                              <td className="p-3">
                                {new Date(user.created_at).toLocaleDateString()}
                              </td>
                              <td className="p-3">
                                {user.last_sign_in_at
                                  ? new Date(user.last_sign_in_at).toLocaleDateString()
                                  : 'Never'}
                              </td>
                              <td className="p-3">
                                <div className="flex gap-1">
                                  {user.is_subscribed ? (
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => handleToggleSubscription(user)}
                                      className="h-8 px-2 text-xs"
                                    >
                                      <span className="mr-1">Remove Premium</span>
                                    </Button>
                                  ) : user.subscription_status === 'expired' ? (
                                    <DropdownMenu>
                                      <DropdownMenuTrigger asChild>
                                        <Button
                                          variant="outline"
                                          size="sm"
                                          className="h-8 px-2 text-xs border-amber-500 text-amber-600 hover:bg-amber-50 hover:text-red-600 premium-btn"
                                        >
                                          <Crown className="h-3 w-3 mr-1" />
                                          <span>Renew Subscription</span>
                                          <ChevronDown className="h-3 w-3 ml-1" />
                                        </Button>
                                      </DropdownMenuTrigger>
                                      <DropdownMenuContent align="end">
                                        <DropdownMenuItem onClick={() => handleToggleSubscription(user, 'basic')}>
                                          <div className="flex items-center">
                                            <span className="font-medium">Basic Plan</span>
                                            <span className="ml-2 text-xs text-muted-foreground">₦998/week</span>
                                          </div>
                                        </DropdownMenuItem>
                                        <DropdownMenuItem onClick={() => handleToggleSubscription(user, 'pro')}>
                                          <div className="flex items-center">
                                            <span className="font-medium">Pro Plan</span>
                                            <span className="ml-2 text-xs text-muted-foreground">₦1,979/week</span>
                                          </div>
                                        </DropdownMenuItem>
                                        <DropdownMenuItem onClick={() => handleToggleSubscription(user, 'elite')}>
                                          <div className="flex items-center">
                                            <span className="font-medium">Elite Plan</span>
                                            <span className="ml-2 text-xs text-muted-foreground">₦5,000 one-time</span>
                                          </div>
                                        </DropdownMenuItem>
                                      </DropdownMenuContent>
                                    </DropdownMenu>
                                  ) : (
                                    <DropdownMenu>
                                      <DropdownMenuTrigger asChild>
                                        <Button
                                          variant="outline"
                                          size="sm"
                                          className="h-8 px-2 text-xs border-green-500 text-green-600 hover:bg-green-50 hover:text-red-600 premium-btn"
                                        >
                                          <Crown className="h-3 w-3 mr-1" />
                                          <span>Make Premium</span>
                                          <ChevronDown className="h-3 w-3 ml-1" />
                                        </Button>
                                      </DropdownMenuTrigger>
                                      <DropdownMenuContent align="end">
                                        <DropdownMenuItem onClick={() => handleToggleSubscription(user, 'basic')}>
                                          <div className="flex items-center">
                                            <span className="font-medium">Basic Plan</span>
                                            <span className="ml-2 text-xs text-muted-foreground">₦998/week</span>
                                          </div>
                                        </DropdownMenuItem>
                                        <DropdownMenuItem onClick={() => handleToggleSubscription(user, 'pro')}>
                                          <div className="flex items-center">
                                            <span className="font-medium">Pro Plan</span>
                                            <span className="ml-2 text-xs text-muted-foreground">₦1,979/week</span>
                                          </div>
                                        </DropdownMenuItem>
                                        <DropdownMenuItem onClick={() => handleToggleSubscription(user, 'elite')}>
                                          <div className="flex items-center">
                                            <span className="font-medium">Elite Plan</span>
                                            <span className="ml-2 text-xs text-muted-foreground">₦5,000 one-time</span>
                                          </div>
                                        </DropdownMenuItem>
                                      </DropdownMenuContent>
                                    </DropdownMenu>
                                  )}
                                  <Button
                                    variant={user.is_admin ? "ghost" : "outline"}
                                    size="sm"
                                    onClick={() => handleToggleAdminStatus(user)}
                                    className={`h-8 px-2 text-xs ${!user.is_admin ? 'border-purple-500 text-purple-600 hover:bg-purple-50 hover:text-red-600 admin-btn' : ''}`}
                                    title={user.is_admin ? 'Remove Admin' : 'Make Admin'}
                                  >
                                    {user.is_admin ? (
                                      <>
                                        <span className="mr-1">Remove Admin</span>
                                      </>
                                    ) : (
                                      <>
                                        <Shield className="h-3 w-3 mr-1" />
                                        <span>Make Admin</span>
                                      </>
                                    )}
                                  </Button>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleDeleteUser(user.id)}
                                    className="h-8 px-2 text-xs border-red-500 text-red-600 hover:bg-red-50"
                                    title="Delete User"
                                  >
                                    <Trash2 className="h-3 w-3 mr-1" />
                                    <span>Delete</span>
                                  </Button>
                                </div>
                              </td>
                            </tr>
                          ))
                        ) : (
                          <tr>
                            <td colSpan={5} className="p-4 text-center text-muted-foreground">
                              {searchTerm
                                ? "No users found matching your search."
                                : "No users found."}
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                    {usersPageCount > 1 && (
                      <div className="flex items-center justify-between px-4 py-3 border-t">
                        <div className="text-sm text-muted-foreground">
                          Showing {((usersPage - 1) * itemsPerPage) + 1} to {Math.min(usersPage * itemsPerPage, filteredUsers.length)} of {filteredUsers.length} users
                        </div>
                        <div className="flex gap-1">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setUsersPage(p => Math.max(1, p - 1))}
                            disabled={usersPage === 1}
                          >
                            <ChevronLeft className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setUsersPage(p => Math.min(usersPageCount, p + 1))}
                            disabled={usersPage === usersPageCount}
                          >
                            <ChevronRight className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </Card>
            </TabsContent>

            <TabsContent value="learning" className="mt-0">
              <LearningMaterialsManagement />
            </TabsContent>

            <TabsContent value="feedback" className="mt-0">
              <FeedbackManagement />
            </TabsContent>
          </Tabs>

          <div className="flex gap-2">
            <Button
              variant="outline"
              className="flex-1"
              onClick={handleExportData}
            >
              <DownloadCloud className="h-4 w-4 mr-2" /> Export Data
            </Button>
            <Button
              className="flex-1 bg-cyber-primary hover:bg-cyber-primary/90"
              onClick={() => activeTab === "topics" ? refreshTopics() : refreshQuestions()}
            >
              <RefreshCw className="h-4 w-4 mr-2" /> Refresh
            </Button>
          </div>
        </div>

        {/* Topic Form Dialog */}
        <Dialog open={showTopicForm} onOpenChange={setShowTopicForm}>
          <DialogContent className="max-h-[90vh] overflow-y-auto">
            <DialogTitle>{editingTopic ? "Edit Topic" : "Create New Topic"}</DialogTitle>
            <DialogDescription className="sr-only">
              {editingTopic ? "Edit an existing topic" : "Create a new topic for quizzes"}
            </DialogDescription>
            <AdminTopicForm
              initialData={editingTopic || undefined}
              onSuccess={handleTopicFormSuccess}
              onCancel={() => setShowTopicForm(false)}
            />
          </DialogContent>
        </Dialog>

        {/* Question Form Dialog */}
        <Dialog open={showQuestionForm} onOpenChange={setShowQuestionForm}>
          <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
            <DialogTitle>{editingQuestion ? "Edit Question" : "Create New Question"}</DialogTitle>
            <DialogDescription className="sr-only">
              {editingQuestion ? "Edit an existing question" : "Create a new question for quizzes"}
            </DialogDescription>
            <AdminQuestionForm
              initialData={editingQuestion || undefined}
              onSuccess={handleQuestionFormSuccess}
              onCancel={() => setShowQuestionForm(false)}
              topics={topics}
            />
          </DialogContent>
        </Dialog>

        {/* Question Import Dialog */}
        <Dialog open={showQuestionImport} onOpenChange={setShowQuestionImport}>
          <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
            <DialogTitle>Import Questions (Text Format)</DialogTitle>
            <DialogDescription className="sr-only">
              Import questions from text format
            </DialogDescription>
            <AdminQuestionImport
              topics={topics}
              onSuccess={handleQuestionImportSuccess}
              onCancel={() => setShowQuestionImport(false)}
            />
          </DialogContent>
        </Dialog>

        {/* CSV Import Dialog */}
        <Dialog open={showCSVImport} onOpenChange={setShowCSVImport}>
          <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
            <DialogTitle>Import Questions (CSV Format)</DialogTitle>
            <DialogDescription className="sr-only">
              Import questions from CSV format
            </DialogDescription>
            <CSVImport
              topics={topics}
              onSuccess={handleCSVImportSuccess}
              onCancel={() => setShowCSVImport(false)}
            />
          </DialogContent>
        </Dialog>

        <BottomNavigation />
      </div>
    </div>
  );
};

export default AdminDashboard;
