// Test script for email delivery through Supabase/Resend
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, resolve } from 'path';

// Set up __dirname equivalent for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables from .env
dotenv.config({ path: resolve(__dirname, '../.env') });

// Supabase configuration
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://agdyycknlxojiwhlqicq.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseKey) {
  console.error('Error: VITE_SUPABASE_ANON_KEY is not set in .env file');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Test email to send to - REPLACE WITH YOUR EMAIL
const testEmail = '<EMAIL>';

async function testEmailDelivery() {
  console.log('Testing email delivery through Supabase/Resend...');
  console.log(`Sending test email to: ${testEmail}`);

  try {
    // Send a password reset email as a test
    const { error } = await supabase.auth.resetPasswordForEmail(testEmail, {
      redirectTo: `${process.env.VITE_APP_URL || 'https://secquiz.app'}/auth/reset-password`,
    });

    if (error) {
      console.error('Error sending test email:', error.message);
    } else {
      console.log('Test email sent successfully!');
      console.log(`Please check ${testEmail} for the password reset email.`);
      console.log('If you don\'t receive it within 2 minutes, check your spam folder.');
      console.log('Also check the Resend dashboard to confirm the email was sent.');
    }
  } catch (err) {
    console.error('Unexpected error:', err);
  }
}

// Run the test
testEmailDelivery();
