/* Quill Editor Styles */

.quill-editor-container {
  margin-bottom: 2rem;
}

.quill-editor-container .ql-container {
  border-bottom-left-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
  background: white;
  font-family: '<PERSON><PERSON><PERSON>', sans-serif;
}

.quill-editor-container .ql-toolbar {
  border-top-left-radius: 0.375rem;
  border-top-right-radius: 0.375rem;
  background: #f9fafb;
  border-color: #e5e7eb;
}

.quill-editor-container .ql-editor {
  min-height: 200px;
  font-size: 0.875rem;
  line-height: 1.5;
}

.quill-editor-container .ql-editor p,
.quill-editor-container .ql-editor h1,
.quill-editor-container .ql-editor h2,
.quill-editor-container .ql-editor h3,
.quill-editor-container .ql-editor h4,
.quill-editor-container .ql-editor h5,
.quill-editor-container .ql-editor h6 {
  margin-bottom: 0.75rem;
}

.quill-editor-container .ql-editor h1 {
  font-size: 1.5rem;
  font-weight: 600;
}

.quill-editor-container .ql-editor h2 {
  font-size: 1.25rem;
  font-weight: 600;
}

.quill-editor-container .ql-editor h3 {
  font-size: 1.125rem;
  font-weight: 600;
}

.quill-editor-container .ql-editor ul,
.quill-editor-container .ql-editor ol {
  padding-left: 1.5rem;
  margin-bottom: 0.75rem;
}

.quill-editor-container .ql-editor li {
  margin-bottom: 0.25rem;
}

.quill-editor-container .ql-editor blockquote {
  border-left: 4px solid #e5e7eb;
  padding-left: 1rem;
  margin-left: 0;
  margin-right: 0;
  font-style: italic;
  color: #6b7280;
}

.quill-editor-container .ql-editor pre {
  background: #f3f4f6;
  border-radius: 0.375rem;
  padding: 0.75rem;
  margin-bottom: 0.75rem;
  font-family: monospace;
}

.quill-editor-container .ql-editor code {
  background: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: monospace;
  font-size: 0.875rem;
}

/* Dark mode support */
.dark .quill-editor-container .ql-container {
  background: #1f2937;
  border-color: #374151;
  color: #e5e7eb;
}

.dark .quill-editor-container .ql-toolbar {
  background: #111827;
  border-color: #374151;
  color: #e5e7eb;
}

.dark .quill-editor-container .ql-toolbar button,
.dark .quill-editor-container .ql-toolbar .ql-picker {
  color: #e5e7eb;
}

.dark .quill-editor-container .ql-editor blockquote {
  border-left-color: #4b5563;
  color: #9ca3af;
}

.dark .quill-editor-container .ql-editor pre,
.dark .quill-editor-container .ql-editor code {
  background: #374151;
  color: #e5e7eb;
}
