// Paystack integration utility

// Get Paystack public key from environment variables
export const PAYSTACK_PUBLIC_KEY = import.meta.env.VITE_PAYSTACK_PUBLIC_KEY || "pk_test_d996328a7eaa7468d3549a67c26dd105ef70ece2";

// Public key is used for client-side initialization
// No need to log it to console

export interface PaymentConfig {
  reference: string;
  email: string;
  amount: number; // in kobo (multiply Naira amount by 100)
  plan?: string;
  currency?: string;
  metadata?: {
    custom_fields: {
      display_name: string;
      variable_name: string;
      value: string;
    }[];
  };
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  amount: number; // in Naira
  interval: 'weekly' | 'monthly' | 'one-time';
  features: string[];
}

// Define subscription plans
export const subscriptionPlans: Record<string, SubscriptionPlan> = {
  basic: {
    id: 'basic',
    name: 'Basic',
    amount: 998, // ₦998
    interval: 'weekly',
    features: [
      'Access to 4 quiz domains',
      '400 questions weekly'
    ]
  },
  pro: {
    id: 'pro',
    name: 'Pro',
    amount: 1979, // ₦1,979
    interval: 'weekly',
    features: [
      'Access to all quiz domains',
      'Unlimited questions',
      'Cancel anytime'
    ]
  },
  elite: {
    id: 'elite',
    name: 'Elite',
    amount: 5000, // ₦5,000
    interval: 'one-time',
    features: [
      'Everything in Pro',
      'Community Access',
      '24/7 Priority Mentorship & Support',
      'CV Design and Job readiness Assist',
      'Daily cybersecurity related jobs',
      'Referrals for job openings'
    ]
  }
};

// Generate a unique reference for each transaction
export const generateReference = (): string => {
  const timestamp = new Date().getTime();
  const randomString = Math.random().toString(36).substring(2, 15);
  return `secquiz-${timestamp}-${randomString}`;
};

// Convert Naira to Kobo (Paystack requires amount in kobo)
export const toKobo = (amount: number): number => {
  return amount * 100;
};

// Handle successful payment
export const handlePaymentSuccess = async (reference: string, planId: string, userEmail: string) => {
  try {
    // Call your server to verify the payment
    const response = await fetch(`${import.meta.env.VITE_API_URL || ''}/api/payments/verify`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ reference }),
    });

    const result = await response.json();

    if (result.success) {
      // Payment verified on the server
      // No need to log sensitive payment information

      // Redirect to success page
      window.location.href = `/payment/success?reference=${reference}&plan=${subscriptionPlans[planId].name}`;

      return {
        success: true,
        message: `Successfully subscribed to ${subscriptionPlans[planId].name} plan`,
        reference,
        plan: subscriptionPlans[planId]
      };
    } else {
      // Payment verification failed
      // Handle error without logging sensitive details

      return {
        success: false,
        message: 'Payment could not be verified',
        error: result.message
      };
    }
  } catch (error) {
    // Handle error without exposing details to console

    // For development/demo purposes, still redirect to success page
    // In production, you would handle this error differently
    if (import.meta.env.DEV) {
      window.location.href = `/payment/success?reference=${reference}&plan=${subscriptionPlans[planId].name}`;
    }

    return {
      success: false,
      message: 'Error processing payment',
      error: error.message
    };
  }
};

// Handle payment closure
export const handlePaymentClose = () => {
  // Payment window was closed by user
  return {
    success: false,
    message: 'Payment was cancelled'
  };
};
