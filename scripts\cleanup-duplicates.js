// This script cleans up duplicate topics and questions in the database
// Run with: node scripts/cleanup-duplicates.js

import { config } from 'dotenv';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;

// Try to use service role key if available, otherwise fall back to anon key
// For security, the service role key should be used only for scripts, not in the browser
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Please check your .env file.');
  console.error('You need to set VITE_SUPABASE_URL and either SUPABASE_SERVICE_ROLE_KEY or VITE_SUPABASE_ANON_KEY');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Main function to clean up duplicates
async function cleanupDuplicates() {
  try {
    console.log('Starting duplicate cleanup...');
    
    // 1. Find duplicate topics (same title)
    console.log('Checking for duplicate topics...');
    const { data: topics, error: topicsError } = await supabase
      .from('topics')
      .select('title, id, created_at')
      .order('created_at', { ascending: true });
    
    if (topicsError) {
      throw new Error(`Error fetching topics: ${topicsError.message}`);
    }
    
    // Group topics by title
    const topicsByTitle = {};
    topics.forEach(topic => {
      if (!topicsByTitle[topic.title]) {
        topicsByTitle[topic.title] = [];
      }
      topicsByTitle[topic.title].push(topic);
    });
    
    // Find duplicates (more than one topic with the same title)
    const duplicateTopics = Object.entries(topicsByTitle)
      .filter(([_, topics]) => topics.length > 1)
      .map(([title, topics]) => ({
        title,
        // Keep the oldest topic (first in the array), delete the rest
        keepId: topics[0].id,
        deleteIds: topics.slice(1).map(t => t.id)
      }));
    
    if (duplicateTopics.length === 0) {
      console.log('No duplicate topics found.');
    } else {
      console.log(`Found ${duplicateTopics.length} duplicate topic titles.`);
      
      // For each duplicate topic, update questions to point to the kept topic
      for (const dupTopic of duplicateTopics) {
        console.log(`Processing duplicate topic: ${dupTopic.title}`);
        
        // Update questions to point to the kept topic
        for (const deleteId of dupTopic.deleteIds) {
          const { data, error: updateError } = await supabase
            .from('questions')
            .update({ topic_id: dupTopic.keepId })
            .eq('topic_id', deleteId);
          
          if (updateError) {
            console.error(`Error updating questions for topic ${deleteId}: ${updateError.message}`);
          } else {
            console.log(`Updated questions from topic ${deleteId} to point to ${dupTopic.keepId}`);
          }
        }
        
        // Delete the duplicate topics
        const { error: deleteError } = await supabase
          .from('topics')
          .delete()
          .in('id', dupTopic.deleteIds);
        
        if (deleteError) {
          console.error(`Error deleting duplicate topics: ${deleteError.message}`);
        } else {
          console.log(`Deleted ${dupTopic.deleteIds.length} duplicate topics for "${dupTopic.title}"`);
        }
      }
    }
    
    // 2. Find duplicate questions (same question_text within the same topic)
    console.log('\nChecking for duplicate questions...');
    const { data: questions, error: questionsError } = await supabase
      .from('questions')
      .select('id, question_text, topic_id, created_at')
      .order('created_at', { ascending: true });
    
    if (questionsError) {
      throw new Error(`Error fetching questions: ${questionsError.message}`);
    }
    
    // Group questions by topic_id and question_text
    const questionsByTopicAndText = {};
    questions.forEach(question => {
      const key = `${question.topic_id}:${question.question_text}`;
      if (!questionsByTopicAndText[key]) {
        questionsByTopicAndText[key] = [];
      }
      questionsByTopicAndText[key].push(question);
    });
    
    // Find duplicates (more than one question with the same text in the same topic)
    const duplicateQuestions = Object.entries(questionsByTopicAndText)
      .filter(([_, questions]) => questions.length > 1)
      .map(([key, questions]) => ({
        key,
        // Keep the oldest question (first in the array), delete the rest
        keepId: questions[0].id,
        deleteIds: questions.slice(1).map(q => q.id)
      }));
    
    if (duplicateQuestions.length === 0) {
      console.log('No duplicate questions found.');
    } else {
      console.log(`Found ${duplicateQuestions.length} duplicate questions.`);
      
      // Delete the duplicate questions
      for (const dupQuestion of duplicateQuestions) {
        const { error: deleteError } = await supabase
          .from('questions')
          .delete()
          .in('id', dupQuestion.deleteIds);
        
        if (deleteError) {
          console.error(`Error deleting duplicate questions: ${deleteError.message}`);
        } else {
          console.log(`Deleted ${dupQuestion.deleteIds.length} duplicate questions for key "${dupQuestion.key}"`);
        }
      }
    }
    
    console.log('\nDuplicate cleanup completed successfully!');
  } catch (error) {
    console.error('Error during cleanup:', error.message);
    process.exit(1);
  }
}

// Run the cleanup
cleanupDuplicates().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
