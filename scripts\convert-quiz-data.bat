@echo off
echo SecQuiz Data Conversion Tool
echo ===========================

if "%1"=="" (
  echo Error: Input file not specified
  echo Usage: convert-quiz-data.bat input.csv [output.csv] [topic_id]
  exit /b 1
)

set INPUT=%1
set OUTPUT=converted_questions.csv
set TOPIC=

if not "%2"=="" set OUTPUT=%2
if not "%3"=="" set TOPIC=--topic %3

echo Input file: %INPUT%
echo Output file: %OUTPUT%
if not "%TOPIC%"=="" echo Topic filter: %3

echo.
echo Converting data...
node scripts/convert-quiz-data.js --input %INPUT% --output %OUTPUT% %TOPIC%

if %ERRORLEVEL% NEQ 0 (
  echo.
  echo Conversion failed. See error message above.
) else (
  echo.
  echo Conversion completed successfully!
  echo Output saved to: %OUTPUT%
)

echo.
pause
