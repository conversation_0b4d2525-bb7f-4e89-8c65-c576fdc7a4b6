// Script to fix NDPR questions directly
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function fixQuestionsDirect() {
  try {
    console.log('Starting to fix NDPR questions directly...');
    
    // Fix the DPIA question
    const dpiaId = 'b818fc87-79df-47a7-9b6e-8472dbeedaec';
    
    // First, get the current question to verify
    const { data: dpiaQuestion, error: dpiaGetError } = await supabase
      .from('questions')
      .select('id, question_text, options, correct_answer')
      .eq('id', dpiaId)
      .single();
    
    if (dpiaGetError) {
      console.error('Error getting DPIA question:', dpiaGetError);
    } else {
      console.log('Current DPIA question:', dpiaQuestion);
      
      // Create new options with letter keys
      const newDpiaOptions = {
        "A": dpiaQuestion.options["0"],
        "B": dpiaQuestion.options["1"],
        "C": dpiaQuestion.options["2"],
        "D": dpiaQuestion.options["3"]
      };
      
      // Convert numeric correct answer to letter
      const numberToLetter = { '0': 'A', '1': 'B', '2': 'C', '3': 'D' };
      const newDpiaCorrectAnswer = numberToLetter[dpiaQuestion.correct_answer];
      
      console.log('New DPIA options:', newDpiaOptions);
      console.log('New DPIA correct answer:', newDpiaCorrectAnswer);
      
      // Update the question
      const { data: dpiaUpdateData, error: dpiaUpdateError } = await supabase
        .from('questions')
        .update({
          options: newDpiaOptions,
          correct_answer: newDpiaCorrectAnswer
        })
        .eq('id', dpiaId);
      
      if (dpiaUpdateError) {
        console.error('Error updating DPIA question:', dpiaUpdateError);
      } else {
        console.log('Successfully updated DPIA question');
      }
    }
    
    // Fix the DPO question
    const dpoId = 'a15ded3b-0edb-4f8d-9dda-d44687f36119';
    
    // First, get the current question to verify
    const { data: dpoQuestion, error: dpoGetError } = await supabase
      .from('questions')
      .select('id, question_text, options, correct_answer')
      .eq('id', dpoId)
      .single();
    
    if (dpoGetError) {
      console.error('Error getting DPO question:', dpoGetError);
    } else {
      console.log('Current DPO question:', dpoQuestion);
      
      // Create new options with letter keys
      const newDpoOptions = {
        "A": dpoQuestion.options["0"],
        "B": dpoQuestion.options["1"],
        "C": dpoQuestion.options["2"],
        "D": dpoQuestion.options["3"]
      };
      
      // Convert numeric correct answer to letter
      const numberToLetter = { '0': 'A', '1': 'B', '2': 'C', '3': 'D' };
      const newDpoCorrectAnswer = numberToLetter[dpoQuestion.correct_answer];
      
      console.log('New DPO options:', newDpoOptions);
      console.log('New DPO correct answer:', newDpoCorrectAnswer);
      
      // Update the question
      const { data: dpoUpdateData, error: dpoUpdateError } = await supabase
        .from('questions')
        .update({
          options: newDpoOptions,
          correct_answer: newDpoCorrectAnswer
        })
        .eq('id', dpoId);
      
      if (dpoUpdateError) {
        console.error('Error updating DPO question:', dpoUpdateError);
      } else {
        console.log('Successfully updated DPO question');
      }
    }
    
    console.log('Finished fixing NDPR questions directly');
    
  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the function
fixQuestionsDirect();
