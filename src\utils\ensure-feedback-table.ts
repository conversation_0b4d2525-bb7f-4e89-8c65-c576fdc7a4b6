import { supabase } from '@/integrations/supabase/client';

/**
 * Ensures that the feedback table exists in the database
 * This is a temporary solution until we have proper migrations
 */
export async function ensureFeedbackTable() {
  try {
    // Always ensure local storage is set up first as a fallback
    try {
      const localFeedback = localStorage.getItem('pendingFeedback');
      if (!localFeedback) {
        localStorage.setItem('pendingFeedback', JSON.stringify([]));
      }
      console.log('Local storage fallback for feedback is ready');
    } catch (localError: any) {
      console.error('Error setting up local storage fallback:', localError);
      // Continue anyway - we'll try the database
    }

    // Check if the feedback table exists
    try {
      const { error: checkError } = await supabase
        .from('feedback')
        .select('id')
        .limit(1);

      // If there's no error, the table exists
      if (!checkError) {
        console.log('Feedback table exists');
        return true;
      }

      // If the error is not a "relation does not exist" error, we'll fall back to local storage
      if (!checkError.message.includes('relation "feedback" does not exist')) {
        console.error('Error checking feedback table:', checkError);
        return true; // Return true anyway since we have local storage fallback
      }
    } catch (checkError: any) {
      console.error('Exception checking feedback table:', checkError);
      return true; // Return true anyway since we have local storage fallback
    }

    // Try to create the feedback table using the RPC function
    try {
      const { error: createError } = await supabase.rpc('create_feedback_table');
      if (!createError) {
        console.log('Feedback table created successfully via RPC');
        return true;
      }
      console.error('Error creating feedback table via RPC:', createError);
    } catch (rpcError: any) {
      console.error('Exception creating feedback table via RPC:', rpcError);
      // Continue to next method
    }

    // If RPC fails, try using a direct SQL query
    try {
      // First try with execute_sql function
      const { error: sqlError } = await supabase.rpc('execute_sql', {
        sql_query: getFeedbackTableSQL()
      });

      if (!sqlError) {
        console.log('Feedback table created successfully via execute_sql');
        return true;
      }
      console.error('Error creating feedback table with execute_sql:', sqlError);
    } catch (sqlError: any) {
      console.error('Exception creating feedback table with execute_sql:', sqlError);
      // Continue to fallback
    }

    // If we got here, we couldn't create the table, but we have local storage
    // so we'll return true anyway
    return true;
  } catch (error: any) {
    console.error('Error ensuring feedback table:', error);
    // Even if everything fails, we'll still try to use local storage
    return true;
  }
}

/**
 * Get the SQL for creating the feedback table
 */
function getFeedbackTableSQL() {
  return `
    CREATE TABLE IF NOT EXISTS public.feedback (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      name TEXT NOT NULL,
      email TEXT NOT NULL,
      subject TEXT NOT NULL,
      message TEXT NOT NULL,
      user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
      status TEXT DEFAULT 'new',
      created_at TIMESTAMPTZ DEFAULT now(),
      updated_at TIMESTAMPTZ DEFAULT now()
    );

    -- Enable Row Level Security
    ALTER TABLE public.feedback ENABLE ROW LEVEL SECURITY;

    -- Create policies for feedback
    DROP POLICY IF EXISTS "Anyone can submit feedback" ON public.feedback;
    CREATE POLICY "Anyone can submit feedback"
      ON public.feedback
      FOR INSERT
      WITH CHECK (true);

    DROP POLICY IF EXISTS "Users can view their own feedback" ON public.feedback;
    CREATE POLICY "Users can view their own feedback"
      ON public.feedback
      FOR SELECT
      USING (auth.uid() = user_id OR user_id IS NULL);
  `;
}
