import { useLocation } from "react-router-dom";
import { useEffect } from "react";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-cyber-primary/5 via-cyber-accent/5 to-purple-300/10">
      <div className="text-center p-8 bg-white/80 backdrop-blur-sm rounded-xl border border-cyber-light/30 shadow-md">
        <div className="flex justify-center mb-6">
          <img src="/secquiz-logo.svg" alt="SecQuiz Logo" className="h-20 w-20" />
        </div>
        <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-cyber-primary to-cyber-accent bg-clip-text text-transparent">404</h1>
        <p className="text-xl text-gray-600 mb-6">Oops! Page not found</p>
        <a href="/" className="px-4 py-2 bg-cyber-primary text-white rounded-md hover:bg-cyber-primary/90 transition-colors">
          Return to Home
        </a>
      </div>
    </div>
  );
};

export default NotFound;
