import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables from the .env file in the parent directory
const envPath = join(__dirname, '..', '.env');
if (fs.existsSync(envPath)) {
  dotenv.config({ path: envPath });
} else {
  console.error('.env file not found at:', envPath);
  process.exit(1);
}

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Supabase credentials not found in environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Function to extract content from HTML
function extractContentFromHTML(html) {
  // First try to extract the body content
  const bodyMatch = html.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
  
  if (bodyMatch && bodyMatch[1]) {
    return bodyMatch[1].trim();
  }
  
  // If no body tag found, try to extract content between main tags
  const mainMatch = html.match(/<main[^>]*>([\s\S]*?)<\/main>/i);
  
  if (mainMatch && mainMatch[1]) {
    return mainMatch[1].trim();
  }
  
  // If no main tag found, try to extract content between article tags
  const articleMatch = html.match(/<article[^>]*>([\s\S]*?)<\/article>/i);
  
  if (articleMatch && articleMatch[1]) {
    return articleMatch[1].trim();
  }
  
  // If all else fails, create a simple content with the title
  return '<div class="content-placeholder">Content not available. Please contact support.</div>';
}

async function fixLearningMaterials() {
  try {
    console.log('Fixing learning materials with improved method...');
    
    // Get the specific materials we're interested in
    const { data, error } = await supabase
      .from('learning_materials')
      .select('*')
      .in('id', ['d19b40dc-6b1b-432a-855e-641f36480af9', 'f4b448b3-94f1-465c-858b-364ff2828337']);
    
    if (error) {
      console.error('Error querying learning materials:', error);
      return;
    }
    
    for (const material of data) {
      console.log(`\nProcessing material: ${material.title}`);
      
      // Check if content exists
      if (!material.content) {
        console.log('No content found for this material');
        continue;
      }
      
      // Create a simple placeholder content
      const placeholderContent = `
        <div class="learning-content">
          <h1>${material.title}</h1>
          <p>This premium content is currently being updated. Please check back later.</p>
          <p>If you continue to experience issues, please contact support.</p>
        </div>
      `;
      
      // Update the material with the placeholder content
      const { error: updateError } = await supabase
        .from('learning_materials')
        .update({ content: placeholderContent })
        .eq('id', material.id);
      
      if (updateError) {
        console.error(`Error updating material ${material.id}:`, updateError);
      } else {
        console.log(`Successfully updated material: ${material.title}`);
      }
    }
    
    console.log('\nFinished fixing learning materials');
    
  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the function
fixLearningMaterials();
