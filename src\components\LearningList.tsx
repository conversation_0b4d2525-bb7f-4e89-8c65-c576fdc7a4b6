import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import LearningCard from "./LearningCard";
import { LearningMaterialForUI } from "@/utils/fetch-learning-materials";

interface LearningListProps {
  title: string;
  materials: LearningMaterialForUI[];
  showViewAll?: boolean;
}

const LearningList = ({ title, materials, showViewAll = false }: LearningListProps) => {
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5
      }
    }
  };

  return (
    <div className="py-8">
      <div className="flex justify-between items-center mb-6">
        <motion.h2 
          className="text-2xl font-bold text-white"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
        >
          {title}
        </motion.h2>
        
        {showViewAll && (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Link 
              to="/learn"
              className="text-cyan-400 hover:text-cyan-300 text-sm font-medium"
            >
              View all
            </Link>
          </motion.div>
        )}
      </div>
      
      <motion.div
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {materials.map((material) => (
          <motion.div key={material.id} variants={itemVariants}>
            <LearningCard
              id={material.id}
              title={material.title}
              summary={material.summary}
              topicId={material.topicId}
              topicTitle={material.topicTitle}
              isPremium={material.isPremium}
            />
          </motion.div>
        ))}
      </motion.div>
    </div>
  );
};

export default LearningList;
