import { supabase } from "@/integrations/supabase/client";

// Function to create the user_profiles table using Supabase's built-in functions
export async function createUserProfilesTable() {
  try {
    console.log("Creating user_profiles table using Supabase's built-in functions...");

    // Check if the table already exists by directly querying it
    const { data, error: checkError } = await supabase
      .from('user_profiles')
      .select('id')
      .limit(1);

    // If there's no error, the table exists
    if (!checkError) {
      console.log("user_profiles table already exists");
      return { success: true };
    }

    // If the error is not a "relation does not exist" error, something else is wrong
    if (!checkError.message.includes('relation "user_profiles" does not exist')) {
      console.error("Error checking if table exists:", checkError);
      return { success: false, error: checkError.message };
    }

    // Create the user_profiles table
    console.log("Creating user_profiles table...");

    // We'll use Supabase's REST API to create the table
    // First, get the current user's JWT token
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      throw new Error("No active session");
    }

    // Create the table
    const { error: createError } = await supabase
      .from('user_profiles')
      .insert([
        {
          user_id: session.user.id,
          is_admin: true,
          is_subscribed: true,
          subscription_expires_at: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString()
        }
      ]);

    if (createError) {
      // If the error is that the table doesn't exist, we need to create it
      if (createError.message.includes('relation "user_profiles" does not exist')) {
        console.log("Table doesn't exist, creating it manually...");

        // We'll use a different approach - create a simple table without constraints
        // This is a fallback method that should work in most cases
        const { data, error } = await supabase.rpc('create_user_profiles_simple');

        if (error) {
          console.error("Error creating table:", error);
          return { success: false, error: error.message };
        }

        // Try inserting again
        const { error: insertError } = await supabase
          .from('user_profiles')
          .insert([
            {
              user_id: session.user.id,
              is_admin: true,
              is_subscribed: true,
              subscription_expires_at: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString()
            }
          ]);

        if (insertError) {
          console.error("Error inserting initial record:", insertError);
          return { success: false, error: insertError.message };
        }
      } else {
        console.error("Error creating table:", createError);
        return { success: false, error: createError.message };
      }
    }

    console.log("user_profiles table created successfully");
    return { success: true };
  } catch (error: any) {
    console.error("Error creating user_profiles table:", error);
    return { success: false, error: error.message };
  }
}
