// Test script for email delivery
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, resolve } from 'path';

// Set up __dirname equivalent for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables from .env.local
dotenv.config({ path: resolve(__dirname, '../.env.local') });

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'http://localhost:54321';
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0';

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Test email to send to
const testEmail = '<EMAIL>'; // Your email address

async function testEmailDelivery() {
  console.log('Testing email delivery with Supabase...');

  try {
    // Send a password reset email as a test
    // This will use Supabase's built-in email service
    const { error } = await supabase.auth.resetPasswordForEmail(testEmail, {
      redirectTo: 'http://localhost:3000/reset-password',
    });

    if (error) {
      console.error('Error sending test email:', error.message);
    } else {
      console.log('Test email sent successfully!');
      console.log(`Please check ${testEmail} for the password reset email.`);
      console.log('If you don\'t receive it within 2 minutes, check your spam folder.');
    }
  } catch (err) {
    console.error('Unexpected error:', err);
  }
}

// Run the test
testEmailDelivery();
