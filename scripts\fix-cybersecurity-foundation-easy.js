import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { createInterface } from 'readline';

dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Topic ID for Cybersecurity Foundation - Easy
const TOPIC_ID = '89397ef4-0b9d-42be-9753-27261559a756';

// Questions to update
const QUESTIONS_TO_UPDATE = [
  // Fix duplicate question about vulnerability remediation
  {
    id: 'df076962-a596-4cb5-b37c-2a40d36eed10',
    updates: {
      question_text: "What is the first step in the vulnerability management lifecycle?",
      options: {
        "A": "Implementing a patch",
        "B": "Identifying vulnerabilities",
        "C": "Developing a mitigation plan",
        "D": "Notifying stakeholders"
      },
      correct_answer: "B",
      explanation: "The vulnerability management lifecycle typically begins with identifying vulnerabilities through scanning, testing, or other discovery methods. This is followed by risk assessment, prioritization, remediation, and verification."
    }
  },
  // Fix duplicate question about security awareness training
  {
    id: '6b160d04-e78f-4fe6-8d9d-8de378d1860e',
    updates: {
      options: {
        "A": "To teach employees programming skills",
        "B": "To educate users about security threats and safe practices",
        "C": "To train new security personnel",
        "D": "To improve system performance"
      },
      correct_answer: "B",
      explanation: "Security awareness training educates users about security threats, best practices, and their role in maintaining organizational security. It helps reduce human-based security incidents by increasing awareness of common threats like phishing, social engineering, and unsafe practices."
    }
  },
  // Fix question with numeric options
  {
    id: '162832ca-76d1-46be-9a61-d38eac49bf2b',
    updates: {
      options: {
        "A": "A scheduled system maintenance",
        "B": "An event that potentially violates security policies",
        "C": "A software update",
        "D": "A change in management structure"
      },
      correct_answer: "B",
      explanation: "A security incident is an event that potentially violates an organization's security policies, threatens the confidentiality, integrity, or availability of information assets. Examples include unauthorized access, data breaches, malware infections, and denial of service attacks."
    }
  },
  // Improve explanation for security incident response plan
  {
    id: '7513d685-968b-4b45-b9f9-8a7d29ea140e',
    updates: {
      explanation: "The purpose of a security incident response plan is to provide a structured approach to responding to security incidents. It outlines the procedures, roles, and responsibilities for effectively detecting, analyzing, containing, eradicating, and recovering from security incidents, as well as documenting lessons learned."
    }
  },
  // Improve explanation for defense-in-depth
  {
    id: '0f3dd519-6d78-4e90-8c47-b8b26d0c8993',
    updates: {
      explanation: "Implementing a defense-in-depth strategy increases security by using multiple layers of security controls throughout the system or network. This approach ensures that if one security control fails, others are in place to prevent or limit the impact of a security breach. It combines people, technology, and operational processes to protect critical assets."
    }
  },
  // Improve explanation for BIA
  {
    id: '5159493f-d6f0-4efb-b63f-b4ea2dc70dd8',
    updates: {
      explanation: "A Business Impact Analysis (BIA) determines the impact of a disaster on business operations by identifying critical business functions, their recovery time objectives, and the resources needed to resume operations. It helps organizations prioritize recovery efforts and allocate resources effectively during a disaster recovery situation."
    }
  },
  // Improve explanation for security awareness training goal
  {
    id: '4aad9193-7d54-41d3-9b63-47eafdcf81ed',
    updates: {
      explanation: "The primary goal of a security awareness training program is to promote a culture of security within the organization. This involves making security a part of everyone's daily responsibilities, encouraging employees to report suspicious activities, and ensuring that security considerations are integrated into business processes and decision-making."
    }
  },
  // Improve explanation for threat intelligence
  {
    id: '29e99e2e-32be-4470-828a-5bbe0f61de85',
    updates: {
      explanation: "A threat intelligence program identifies potential threats by collecting, analyzing, and disseminating information about current and emerging threats targeting the organization. This helps security teams understand the tactics, techniques, and procedures used by threat actors, enabling more effective security controls and incident response."
    }
  },
  // Improve explanation for SOAR
  {
    id: '972b46b7-d0bc-49f4-8f42-f2294f3872cc',
    updates: {
      explanation: "Implementing a Security Orchestration, Automation, and Response (SOAR) solution improves incident response by automating routine security tasks, orchestrating complex workflows across multiple security tools, and providing a centralized platform for managing security incidents. This reduces response times, minimizes human error, and allows security teams to handle more incidents effectively."
    }
  }
];

// Questions to delete (duplicates)
const QUESTIONS_TO_DELETE = [
  '10aa80f9-acb8-4753-995c-164adf5b31de' // Duplicate vulnerability remediation question
];

async function updateQuestion(questionId, updates) {
  try {
    const { data, error } = await supabase
      .from('questions')
      .update(updates)
      .eq('id', questionId)
      .select();
    
    if (error) {
      console.error(`Error updating question ${questionId}:`, error);
      return false;
    }
    
    console.log(`Successfully updated question: ${questionId}`);
    return true;
  } catch (error) {
    console.error(`Error updating question ${questionId}:`, error);
    return false;
  }
}

async function deleteQuestion(questionId) {
  try {
    const { error } = await supabase
      .from('questions')
      .delete()
      .eq('id', questionId);
    
    if (error) {
      console.error(`Error deleting question ${questionId}:`, error);
      return false;
    }
    
    console.log(`Successfully deleted question: ${questionId}`);
    return true;
  } catch (error) {
    console.error(`Error deleting question ${questionId}:`, error);
    return false;
  }
}

async function main() {
  try {
    console.log('Starting to fix Cybersecurity Foundation - Easy quiz...');
    
    // Ask for confirmation before making changes
    const readline = createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    const answer = await new Promise(resolve => {
      readline.question(`This will update ${QUESTIONS_TO_UPDATE.length} questions and delete ${QUESTIONS_TO_DELETE.length} questions. Continue? (y/n): `, resolve);
    });
    
    if (answer.toLowerCase() !== 'y') {
      console.log('Operation cancelled.');
      readline.close();
      return;
    }
    
    // Update questions
    console.log('\nUpdating questions...');
    let updateSuccessCount = 0;
    
    for (const question of QUESTIONS_TO_UPDATE) {
      const success = await updateQuestion(question.id, question.updates);
      if (success) updateSuccessCount++;
    }
    
    // Delete duplicate questions
    console.log('\nDeleting duplicate questions...');
    let deleteSuccessCount = 0;
    
    for (const questionId of QUESTIONS_TO_DELETE) {
      const success = await deleteQuestion(questionId);
      if (success) deleteSuccessCount++;
    }
    
    console.log(`\nSummary: Updated ${updateSuccessCount}/${QUESTIONS_TO_UPDATE.length} questions, Deleted ${deleteSuccessCount}/${QUESTIONS_TO_DELETE.length} questions.`);
    
    readline.close();
  } catch (error) {
    console.error('Error:', error);
  }
}

main();
