-- Create topics table
CREATE TABLE IF NOT EXISTS topics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  description TEXT,
  icon TEXT,
  difficulty TEXT CHECK (difficulty IN ('easy', 'medium', 'hard')),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create questions table
CREATE TABLE IF NOT EXISTS questions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  topic_id UUID REFERENCES topics(id) ON DELETE CASCADE,
  question_text TEXT NOT NULL,
  options JSONB NOT NULL, -- Store options as JSON: {"0": "Option A", "1": "Option B", ...}
  correct_answer TEXT NOT NULL, -- Store the index of the correct answer as a string
  explanation TEXT,
  difficulty TEXT CHECK (difficulty IN ('easy', 'medium', 'hard')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_progress table to track quiz attempts
CREATE TABLE IF NOT EXISTS user_progress (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  topic_id UUID REFERENCES topics(id) ON DELETE CASCADE,
  score INTEGER NOT NULL,
  max_score INTEGER NOT NULL,
  completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create RLS policies for topics
ALTER TABLE topics ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DO $$
BEGIN
  -- Drop existing policies on topics table
  BEGIN
    DROP POLICY IF EXISTS "Topics are viewable by everyone" ON topics;
  EXCEPTION WHEN OTHERS THEN
    -- Policy doesn't exist, ignore
  END;

  BEGIN
    DROP POLICY IF EXISTS "Service role can manage topics" ON topics;
  EXCEPTION WHEN OTHERS THEN
    -- Policy doesn't exist, ignore
  END;

  BEGIN
    DROP POLICY IF EXISTS "Topics are editable by admins" ON topics;
  EXCEPTION WHEN OTHERS THEN
    -- Policy doesn't exist, ignore
  END;
END $$;

-- Everyone can read topics
CREATE POLICY "Topics are viewable by everyone"
  ON topics FOR SELECT
  USING (true);

-- Allow service role to manage topics (for data import)
CREATE POLICY "Service role can manage topics"
  ON topics FOR ALL
  TO service_role
  USING (true);

-- Only admins can insert/update/delete topics (for authenticated users)
CREATE POLICY "Topics are editable by admins"
  ON topics FOR ALL
  USING (auth.uid() IN (
    SELECT user_id FROM admin_users
  ));

-- Create RLS policies for questions
ALTER TABLE questions ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DO $$
BEGIN
  -- Drop existing policies on questions table
  BEGIN
    DROP POLICY IF EXISTS "Questions are viewable by everyone" ON questions;
  EXCEPTION WHEN OTHERS THEN
    -- Policy doesn't exist, ignore
  END;

  BEGIN
    DROP POLICY IF EXISTS "Service role can manage questions" ON questions;
  EXCEPTION WHEN OTHERS THEN
    -- Policy doesn't exist, ignore
  END;

  BEGIN
    DROP POLICY IF EXISTS "Questions are editable by admins" ON questions;
  EXCEPTION WHEN OTHERS THEN
    -- Policy doesn't exist, ignore
  END;
END $$;

-- Everyone can read questions
CREATE POLICY "Questions are viewable by everyone"
  ON questions FOR SELECT
  USING (true);

-- Allow service role to manage questions (for data import)
CREATE POLICY "Service role can manage questions"
  ON questions FOR ALL
  TO service_role
  USING (true);

-- Only admins can insert/update/delete questions (for authenticated users)
CREATE POLICY "Questions are editable by admins"
  ON questions FOR ALL
  USING (auth.uid() IN (
    SELECT user_id FROM admin_users
  ));

-- Create RLS policies for user_progress
ALTER TABLE user_progress ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DO $$
BEGIN
  -- Drop existing policies on user_progress table
  BEGIN
    DROP POLICY IF EXISTS "Users can view their own progress" ON user_progress;
  EXCEPTION WHEN OTHERS THEN
    -- Policy doesn't exist, ignore
  END;

  BEGIN
    DROP POLICY IF EXISTS "Users can insert their own progress" ON user_progress;
  EXCEPTION WHEN OTHERS THEN
    -- Policy doesn't exist, ignore
  END;
END $$;

-- Users can read their own progress
CREATE POLICY "Users can view their own progress"
  ON user_progress FOR SELECT
  USING (auth.uid() = user_id);

-- Users can insert their own progress
CREATE POLICY "Users can insert their own progress"
  ON user_progress FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Create admin_users table to track admin users
CREATE TABLE IF NOT EXISTS admin_users (
  user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create function to check if a user is an admin
CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM admin_users WHERE user_id = auth.uid()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop existing triggers if they exist
DROP TRIGGER IF EXISTS update_topics_timestamp ON topics;
DROP TRIGGER IF EXISTS update_questions_timestamp ON questions;

-- Create triggers
CREATE TRIGGER update_topics_timestamp
BEFORE UPDATE ON topics
FOR EACH ROW
EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_questions_timestamp
BEFORE UPDATE ON questions
FOR EACH ROW
EXECUTE FUNCTION update_timestamp();
