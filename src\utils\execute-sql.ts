import { supabase } from "@/integrations/supabase/client";

// Function to execute SQL directly
export async function executeSql(sql: string) {
  try {
    console.log("Executing SQL:", sql);

    // Get the current user's JWT token
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      throw new Error("No active session");
    }

    // Execute the SQL using the REST API
    const response = await fetch(`${supabase.supabaseUrl}/rest/v1/rpc/run_sql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`,
        'apikey': supabase.supabaseKey,
      },
      body: JSON.stringify({
        sql: sql
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`SQL execution failed: ${errorText}`);
    }

    return { success: true };
  } catch (error: any) {
    console.error("Error executing SQL:", error);
    return { success: false, error: error.message };
  }
}

// Function to fix the get_all_users_simple function
export async function fixGetAllUsersSimple() {
  const sql = `
  -- Drop the existing function if it exists
  DROP FUNCTION IF EXISTS public.get_all_users_simple();

  -- Create a new version of the function with the exact structure needed
  CREATE OR REPLACE FUNCTION public.get_all_users_simple()
  RETURNS TABLE (
    id uuid,
    email text,
    created_at timestamptz,
    last_sign_in_at timestamptz,
    is_subscribed boolean,
    is_admin boolean,
    subscription_expires_at timestamptz
  )
  SECURITY DEFINER
  AS $$
  BEGIN
    RETURN QUERY
    SELECT
      au.id,
      au.email::text,
      au.created_at,
      au.last_sign_in_at,
      COALESCE((SELECT true FROM public.admin_users WHERE user_id = au.id), false) as is_admin,
      false as is_subscribed,
      NULL::timestamptz as subscription_expires_at
    FROM auth.users au
    ORDER BY au.created_at DESC;
  END;
  $$ LANGUAGE plpgsql;

  -- Grant execute permission to authenticated users
  GRANT EXECUTE ON FUNCTION public.get_all_users_simple() TO authenticated;
  `;

  return await executeSql(sql);
}

// Function to check table structures
export async function checkTableStructures() {
  const sql = `
  -- Get column information for admin_users table
  SELECT column_name, data_type
  FROM information_schema.columns
  WHERE table_schema = 'public' AND table_name = 'admin_users';
  `;

  try {
    // Get the current user's JWT token
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
      throw new Error("No active session");
    }

    // Execute the SQL using the REST API
    const response = await fetch(`${supabase.supabaseUrl}/rest/v1/rpc/run_sql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`,
        'apikey': supabase.supabaseKey,
      },
      body: JSON.stringify({
        sql: sql
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`SQL execution failed: ${errorText}`);
    }

    const result = await response.json();
    return { success: true, data: result };
  } catch (error: any) {
    console.error("Error checking table structures:", error);
    return { success: false, error: error.message };
  }
}

// Function to fix the delete_user function
export async function fixDeleteUserFunction() {
  // First, check the table structures to determine the correct column names
  const checkResult = await checkTableStructures();

  if (!checkResult.success) {
    console.error("Failed to check table structures:", checkResult.error);
    // Proceed with a more cautious approach using dynamic SQL
  }

  // Log the table structure information
  console.log("Table structure information:", checkResult.data);

  const sql = `
  -- Drop the existing function if it exists
  DROP FUNCTION IF EXISTS public.delete_user(uuid);

  -- Create a new version of the function with dynamic column checks
  CREATE OR REPLACE FUNCTION public.delete_user(user_id_param uuid)
  RETURNS void
  SECURITY DEFINER
  AS $$
  DECLARE
    column_exists boolean;
    row_count integer;
    user_exists boolean;
  BEGIN
    -- Check if the user exists first
    SELECT EXISTS (
      SELECT 1 FROM auth.users WHERE id = user_id_param
    ) INTO user_exists;

    IF NOT user_exists THEN
      RAISE EXCEPTION 'User with ID % does not exist', user_id_param;
    END IF;

    -- Log the start of the deletion process
    RAISE NOTICE 'Starting deletion process for user ID: %', user_id_param;

    -- Check if profiles table exists (this is often used instead of user_profiles)
    IF EXISTS (
      SELECT FROM pg_tables
      WHERE schemaname = 'public' AND tablename = 'profiles'
    ) THEN
      -- Check if id column exists in profiles
      SELECT EXISTS (
        SELECT FROM information_schema.columns
        WHERE table_schema = 'public' AND table_name = 'profiles' AND column_name = 'id'
      ) INTO column_exists;

      IF column_exists THEN
        DELETE FROM public.profiles WHERE id = user_id_param;
        GET DIAGNOSTICS row_count = ROW_COUNT;
        RAISE NOTICE 'Deleted % rows from profiles', row_count;
      END IF;
    END IF;

    -- Check if admin_users table exists
    IF EXISTS (
      SELECT FROM pg_tables
      WHERE schemaname = 'public' AND tablename = 'admin_users'
    ) THEN
      -- Check if user_id column exists in admin_users
      SELECT EXISTS (
        SELECT FROM information_schema.columns
        WHERE table_schema = 'public' AND table_name = 'admin_users' AND column_name = 'user_id'
      ) INTO column_exists;

      IF column_exists THEN
        DELETE FROM public.admin_users WHERE user_id = user_id_param;
        GET DIAGNOSTICS row_count = ROW_COUNT;
        RAISE NOTICE 'Deleted % rows from admin_users', row_count;
      END IF;
    END IF;

    -- Check if user_profiles table exists
    IF EXISTS (
      SELECT FROM pg_tables
      WHERE schemaname = 'public' AND tablename = 'user_profiles'
    ) THEN
      -- Check if user_id column exists in user_profiles
      SELECT EXISTS (
        SELECT FROM information_schema.columns
        WHERE table_schema = 'public' AND table_name = 'user_profiles' AND column_name = 'user_id'
      ) INTO column_exists;

      IF column_exists THEN
        DELETE FROM public.user_profiles WHERE user_id = user_id_param;
        GET DIAGNOSTICS row_count = ROW_COUNT;
        RAISE NOTICE 'Deleted % rows from user_profiles', row_count;
      END IF;
    END IF;

    -- Check if subscriptions table exists
    IF EXISTS (
      SELECT FROM pg_tables
      WHERE schemaname = 'public' AND tablename = 'subscriptions'
    ) THEN
      -- Check if user_id column exists in subscriptions
      SELECT EXISTS (
        SELECT FROM information_schema.columns
        WHERE table_schema = 'public' AND table_name = 'subscriptions' AND column_name = 'user_id'
      ) INTO column_exists;

      IF column_exists THEN
        DELETE FROM public.subscriptions WHERE user_id = user_id_param;
        GET DIAGNOSTICS row_count = ROW_COUNT;
        RAISE NOTICE 'Deleted % rows from subscriptions', row_count;
      END IF;
    END IF;

    -- Check if payments table exists
    IF EXISTS (
      SELECT FROM pg_tables
      WHERE schemaname = 'public' AND tablename = 'payments'
    ) THEN
      -- Check if user_id column exists in payments
      SELECT EXISTS (
        SELECT FROM information_schema.columns
        WHERE table_schema = 'public' AND table_name = 'payments' AND column_name = 'user_id'
      ) INTO column_exists;

      IF column_exists THEN
        DELETE FROM public.payments WHERE user_id = user_id_param;
        GET DIAGNOSTICS row_count = ROW_COUNT;
        RAISE NOTICE 'Deleted % rows from payments', row_count;
      END IF;
    END IF;

    -- Check if quiz_attempts table exists
    IF EXISTS (
      SELECT FROM pg_tables
      WHERE schemaname = 'public' AND tablename = 'quiz_attempts'
    ) THEN
      -- Check if user_id column exists in quiz_attempts
      SELECT EXISTS (
        SELECT FROM information_schema.columns
        WHERE table_schema = 'public' AND table_name = 'quiz_attempts' AND column_name = 'user_id'
      ) INTO column_exists;

      IF column_exists THEN
        DELETE FROM public.quiz_attempts WHERE user_id = user_id_param;
        GET DIAGNOSTICS row_count = ROW_COUNT;
        RAISE NOTICE 'Deleted % rows from quiz_attempts', row_count;
      END IF;
    END IF;

    -- Check if user_progress table exists
    IF EXISTS (
      SELECT FROM pg_tables
      WHERE schemaname = 'public' AND tablename = 'user_progress'
    ) THEN
      -- Check if user_id column exists in user_progress
      SELECT EXISTS (
        SELECT FROM information_schema.columns
        WHERE table_schema = 'public' AND table_name = 'user_progress' AND column_name = 'user_id'
      ) INTO column_exists;

      IF column_exists THEN
        DELETE FROM public.user_progress WHERE user_id = user_id_param;
        GET DIAGNOSTICS row_count = ROW_COUNT;
        RAISE NOTICE 'Deleted % rows from user_progress', row_count;
      END IF;
    END IF;

    -- Check if user_quiz_results table exists
    IF EXISTS (
      SELECT FROM pg_tables
      WHERE schemaname = 'public' AND tablename = 'user_quiz_results'
    ) THEN
      -- Check if user_id column exists in user_quiz_results
      SELECT EXISTS (
        SELECT FROM information_schema.columns
        WHERE table_schema = 'public' AND table_name = 'user_quiz_results' AND column_name = 'user_id'
      ) INTO column_exists;

      IF column_exists THEN
        DELETE FROM public.user_quiz_results WHERE user_id = user_id_param;
        GET DIAGNOSTICS row_count = ROW_COUNT;
        RAISE NOTICE 'Deleted % rows from user_quiz_results', row_count;
      END IF;
    END IF;

    -- Check if feedback table exists
    IF EXISTS (
      SELECT FROM pg_tables
      WHERE schemaname = 'public' AND tablename = 'feedback'
    ) THEN
      -- Check if user_id column exists in feedback
      SELECT EXISTS (
        SELECT FROM information_schema.columns
        WHERE table_schema = 'public' AND table_name = 'feedback' AND column_name = 'user_id'
      ) INTO column_exists;

      IF column_exists THEN
        DELETE FROM public.feedback WHERE user_id = user_id_param;
        GET DIAGNOSTICS row_count = ROW_COUNT;
        RAISE NOTICE 'Deleted % rows from feedback', row_count;
      END IF;
    END IF;

    -- Check if payment_transactions table exists
    IF EXISTS (
      SELECT FROM pg_tables
      WHERE schemaname = 'public' AND tablename = 'payment_transactions'
    ) THEN
      -- Check if user_id column exists in payment_transactions
      SELECT EXISTS (
        SELECT FROM information_schema.columns
        WHERE table_schema = 'public' AND table_name = 'payment_transactions' AND column_name = 'user_id'
      ) INTO column_exists;

      IF column_exists THEN
        DELETE FROM public.payment_transactions WHERE user_id = user_id_param;
        GET DIAGNOSTICS row_count = ROW_COUNT;
        RAISE NOTICE 'Deleted % rows from payment_transactions', row_count;
      END IF;
    END IF;

    -- Delete user from auth.users (requires admin privileges)
    -- This is the most important part and requires proper permissions
    BEGIN
      DELETE FROM auth.users WHERE id = user_id_param;
      GET DIAGNOSTICS row_count = ROW_COUNT;
      RAISE NOTICE 'Deleted % rows from auth.users', row_count;

      IF row_count = 0 THEN
        RAISE EXCEPTION 'Failed to delete user from auth.users. This function requires admin privileges.';
      END IF;
    EXCEPTION WHEN OTHERS THEN
      RAISE EXCEPTION 'Error deleting from auth.users: %. This function requires admin privileges.', SQLERRM;
    END;

    RAISE NOTICE 'User deletion completed successfully for ID: %', user_id_param;
  END;
  $$ LANGUAGE plpgsql;

  -- Grant execute permission to authenticated users
  GRANT EXECUTE ON FUNCTION public.delete_user(uuid) TO authenticated;

  -- Grant additional permissions if needed
  DO $$
  BEGIN
    -- Try to grant additional permissions to the function
    BEGIN
      GRANT USAGE ON SCHEMA auth TO authenticated;
      GRANT SELECT, DELETE ON auth.users TO authenticated;
    EXCEPTION WHEN OTHERS THEN
      RAISE NOTICE 'Could not grant additional permissions: %', SQLERRM;
    END;
  END $$;
  `;

  return await executeSql(sql);
}
