import { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CheckCircle, XCircle, Loader2 } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import Navbar from "@/components/Navbar";
import BottomNavigation from "@/components/BottomNavigation";
import { motion } from "framer-motion";

const VerifyEmailPage = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [verificationStatus, setVerificationStatus] = useState<"loading" | "success" | "error">("loading");
  const [errorMessage, setErrorMessage] = useState<string>("");

  useEffect(() => {
    const verifyEmail = async () => {
      try {
        // Check if we have a hash in the URL (Supabase sometimes uses this format)
        const hash = window.location.hash;

        // Log all available parameters for debugging
        console.log("URL Parameters:", Object.fromEntries(searchParams.entries()));
        console.log("URL Hash:", hash);

        // Try to get token from different possible sources
        let token = searchParams.get("token") || searchParams.get("token_hash");
        let type = searchParams.get("type");

        // If we have a hash, try to extract parameters from it
        if (hash && (!token || !type)) {
          const hashParams = new URLSearchParams(hash.substring(1));
          token = token || hashParams.get("token") || hashParams.get("token_hash");
          type = type || hashParams.get("type");
        }

        // If we still don't have the parameters, check if we have an access_token
        // which indicates the user is already authenticated
        const access_token = searchParams.get("access_token");

        if (access_token) {
          console.log("User already authenticated with access_token");
          setVerificationStatus("success");
          return;
        }

        if (!token || !type) {
          // Check if the user is already authenticated
          const { data } = await supabase.auth.getSession();
          if (data.session) {
            console.log("User already has a valid session");
            setVerificationStatus("success");
            return;
          }

          setVerificationStatus("error");
          setErrorMessage("Invalid verification link. Missing parameters.");
          return;
        }

        // Verify the email
        const { error } = await supabase.auth.verifyOtp({
          token_hash: token,
          type: type === "signup" ? "signup" : "recovery",
        });

        if (error) {
          console.error("Verification error:", error);

          // Check if the error indicates the user is already verified
          if (error.message.includes("User already confirmed") ||
              error.message.includes("already been verified")) {
            setVerificationStatus("success");
          } else {
            setVerificationStatus("error");
            setErrorMessage(error.message);
          }
        } else {
          setVerificationStatus("success");
        }
      } catch (error) {
        console.error("Verification error:", error);
        setVerificationStatus("error");
        setErrorMessage("An unexpected error occurred during verification.");
      }
    };

    verifyEmail();
  }, [searchParams]);

  return (
    <div className="flex flex-col min-h-screen">
      <Navbar />

      <div className="flex-1 flex flex-col items-center justify-center p-4 cyber-grid-bg">
        <motion.div
          className="w-full max-w-md"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="text-center mb-6">
            <div className="inline-flex mb-4 p-3 rounded-full bg-cyber-primary/10">
              <img src="/secquiz-logo.svg" alt="SecQuiz Logo" className="h-12 w-12" />
            </div>
            <h1 className="text-2xl font-bold bg-gradient-to-r from-cyber-primary to-cyber-accent bg-clip-text text-transparent">
              Email Verification
            </h1>
          </div>

          <Card className="cyber-card p-6 shadow-lg">
            <div className="flex flex-col items-center text-center">
              {verificationStatus === "loading" && (
                <>
                  <Loader2 className="h-12 w-12 text-cyber-primary animate-spin mb-4" />
                  <h2 className="text-xl font-semibold mb-2">Verifying your email</h2>
                  <p className="text-muted-foreground">Please wait while we verify your email address...</p>
                </>
              )}

              {verificationStatus === "success" && (
                <>
                  <CheckCircle className="h-12 w-12 text-green-500 mb-4" />
                  <h2 className="text-xl font-semibold mb-2">Email Verified!</h2>
                  <p className="text-muted-foreground mb-6">
                    Your email has been successfully verified. You can now log in to your account.
                  </p>
                  <div className="flex flex-col gap-3 w-full">
                    <Button
                      className="w-full bg-cyber-primary hover:bg-cyber-primary/90"
                      onClick={() => navigate("/auth")}
                    >
                      Go to Login
                    </Button>
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() => navigate("/")}
                    >
                      Go to Homepage
                    </Button>
                  </div>
                </>
              )}

              {verificationStatus === "error" && (
                <>
                  <XCircle className="h-12 w-12 text-red-500 mb-4" />
                  <h2 className="text-xl font-semibold mb-2">Verification Failed</h2>
                  <p className="text-muted-foreground mb-2">
                    We couldn't verify your email address.
                  </p>
                  <p className="text-sm text-red-500 mb-6">
                    {errorMessage || "The verification link may have expired or is invalid."}
                  </p>
                  <div className="flex flex-col gap-3 w-full">
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() => navigate("/auth")}
                    >
                      Back to Login
                    </Button>
                    <Button
                      className="w-full bg-cyber-primary hover:bg-cyber-primary/90"
                      onClick={() => navigate("/contact")}
                    >
                      Contact Support
                    </Button>
                  </div>
                </>
              )}
            </div>
          </Card>
        </motion.div>
      </div>

      <BottomNavigation />
    </div>
  );
};

export default VerifyEmailPage;
