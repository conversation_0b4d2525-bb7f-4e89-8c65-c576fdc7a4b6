import { supabase } from "@/integrations/supabase/client";
import { User } from "@supabase/supabase-js";

/**
 * Get the total number of quizzes taken by a user
 */
export async function getQuizzesTaken(user: User | null): Promise<number> {
  if (!user) return 0;

  try {
    console.log('Fetching quiz count for user:', user.id);

    // Count the number of quiz attempts for this user
    const { count, error } = await supabase
      .from('quiz_attempts')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id);

    if (error) {
      console.error('Error fetching quiz attempts count:', error);
      return 0;
    }

    console.log('Quiz count result:', count);
    return count || 0;
  } catch (error) {
    console.error('Error in getQuizzesTaken:', error);
    return 0;
  }
}

/**
 * Get the average score percentage for a user
 */
export async function getAverageScore(user: User | null): Promise<number> {
  if (!user) return 0;

  try {
    console.log('Fetching average score for user:', user.id);

    // Get all quiz attempts for this user
    const { data, error } = await supabase
      .from('quiz_attempts')
      .select('score, total_questions')
      .eq('user_id', user.id);

    if (error) {
      console.error('Error fetching quiz attempts:', error);
      return 0;
    }

    if (!data || data.length === 0) {
      console.log('No quiz attempts found for user');
      return 0;
    }

    console.log('Found quiz attempts:', data.length);

    // Calculate the average score percentage
    let totalPercentage = 0;
    let validAttempts = 0;

    for (const attempt of data) {
      // Skip invalid attempts (where total_questions is 0)
      if (!attempt.total_questions) continue;

      // Ensure score doesn't exceed total questions
      const validScore = Math.min(attempt.score, attempt.total_questions);
      const percentage = (validScore / attempt.total_questions) * 100;
      // Only add valid percentages (not NaN or Infinity)
      if (!isNaN(percentage) && isFinite(percentage)) {
        totalPercentage += percentage;
        validAttempts++;
      }
    }

    // If no valid attempts, return 0
    if (validAttempts === 0) {
      console.log('No valid quiz attempts found');
      return 0;
    }

    // Calculate average and ensure it doesn't exceed 100%
    const averageScore = Math.min(100, Math.round(totalPercentage / validAttempts));
    console.log('Average score calculated:', averageScore);
    return averageScore;
  } catch (error) {
    console.error('Error in getAverageScore:', error);
    return 0;
  }
}
