
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_ANON_KEY") ?? ""
    );

    // Get the authorization header from the request
    const authHeader = req.headers.get("Authorization");
    if (!authHeader) {
      throw new Error("Missing Authorization header");
    }

    // Get the user from the auth header
    const { data: { user }, error: userError } = await supabaseClient.auth.getUser(
      authHeader.replace("Bearer ", "")
    );

    if (userError || !user) {
      throw new Error("Unauthorized");
    }

    // Initialize Paystack (you should add your Paystack secret key to edge function secrets)
    const PAYSTACK_SECRET_KEY = Deno.env.get("PAYSTACK_SECRET_KEY");
    if (!PAYSTACK_SECRET_KEY) {
      throw new Error("Missing Paystack secret key");
    }

    // Get subscription price from settings
    const { data: settingsData, error: settingsError } = await supabaseClient
      .from("settings")
      .select("setting_value")
      .eq("setting_key", "subscription_price")
      .single();

    if (settingsError) {
      throw new Error("Error fetching subscription price");
    }

    const amount = parseInt(settingsData.setting_value) * 100; // Convert to kobo (smallest currency unit)

    // Create Paystack payment initialization
    const response = await fetch("https://api.paystack.co/transaction/initialize", {
      method: "POST",
      headers: {
        Authorization: `Bearer ${PAYSTACK_SECRET_KEY}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        email: user.email,
        amount: amount, // Amount in kobo (₦1000 = 100000 kobo)
        callback_url: `${req.headers.get("origin")}/payment-success`,
        metadata: {
          user_id: user.id,
          subscription_type: "monthly",
        },
      }),
    });

    const paymentData = await response.json();

    if (!paymentData.status) {
      throw new Error("Failed to initialize payment");
    }

    // Record the payment attempt in the database
    await supabaseClient.from("payments").insert({
      user_id: user.id,
      amount: amount / 100, // Store in naira
      status: "pending",
      provider: "paystack",
      provider_payment_id: paymentData.data.reference,
    });

    return new Response(
      JSON.stringify({
        success: true,
        payment_url: paymentData.data.authorization_url,
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      }
    );
  } catch (error) {
    console.error("Payment error:", error.message);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
      }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 400,
      }
    );
  }
});
