// Script to extract body content from HTML documents in learning materials
import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';
import { JSDOM } from 'jsdom';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Extracts the body content from an HTML document
 * @param {string} htmlContent - The full HTML content
 * @returns {string} - The extracted body content or the original content if extraction fails
 */
function extractBodyContent(htmlContent) {
  try {
    // Check if this is a full HTML document
    if (!htmlContent.includes('<!DOCTYPE html>') && !htmlContent.includes('<html')) {
      return htmlContent; // Already just content, no need to extract
    }

    // Parse the HTML
    const dom = new JSDOM(htmlContent);
    const bodyContent = dom.window.document.body.innerHTML;

    return bodyContent || htmlContent;
  } catch (error) {
    console.error('Error extracting body content:', error);
    return htmlContent; // Return original content if extraction fails
  }
}

async function extractBodyFromLearningMaterials() {
  try {
    console.log('Extracting body content from learning materials...');

    // Fetch all learning materials
    const { data, error } = await supabase
      .from('learning_materials')
      .select('*');

    if (error) {
      console.error('Error fetching learning materials:', error);
      return;
    }

    console.log(`Found ${data.length} learning materials`);

    // Process each material
    for (const material of data) {
      console.log(`\nProcessing material: ${material.title}`);

      // Check if content exists
      if (!material.content) {
        console.log('No content found for this material');
        continue;
      }

      // Check if content is a full HTML document
      const isFullHtml = material.content.includes('<!DOCTYPE html>') || material.content.includes('<html');

      if (!isFullHtml) {
        console.log('Content is not a full HTML document, skipping');
        continue;
      }

      // Extract body content
      const bodyContent = extractBodyContent(material.content);

      // Update the material with the extracted body content
      const { error: updateError } = await supabase
        .from('learning_materials')
        .update({ content: bodyContent })
        .eq('id', material.id);

      if (updateError) {
        console.error(`Error updating material ${material.id}:`, updateError);
      } else {
        console.log(`Successfully updated material: ${material.title}`);
        console.log(`Original content length: ${material.content.length}`);
        console.log(`New content length: ${bodyContent.length}`);
      }
    }

    console.log('\nExtraction complete');

  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the function
extractBodyFromLearningMaterials();
