import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables from the .env file in the parent directory
const envPath = join(__dirname, '..', '.env');
if (fs.existsSync(envPath)) {
  dotenv.config({ path: envPath });
} else {
  console.error('.env file not found at:', envPath);
  process.exit(1);
}

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Supabase credentials not found in environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkHtmlContent() {
  try {
    console.log('Checking HTML content in learning materials...');
    
    // Get the specific materials we're interested in
    const { data, error } = await supabase
      .from('learning_materials')
      .select('*')
      .in('id', ['d19b40dc-6b1b-432a-855e-641f36480af9', 'f4b448b3-94f1-465c-858b-364ff2828337']);
    
    if (error) {
      console.error('Error querying learning materials:', error);
      return;
    }
    
    // Check for label elements with for attributes
    data.forEach(material => {
      console.log(`\nAnalyzing material: ${material.title}`);
      
      // Check if content exists
      if (!material.content) {
        console.log('No content found for this material');
        return;
      }
      
      // Look for label elements with for attributes
      const labelRegex = /<label[^>]*for=["']([^"']*)["'][^>]*>/g;
      const matches = [...material.content.matchAll(labelRegex)];
      
      if (matches.length > 0) {
        console.log(`Found ${matches.length} label elements with for attributes:`);
        
        matches.forEach((match, index) => {
          const forValue = match[1];
          console.log(`${index + 1}. Label with for="${forValue}"`);
          
          // Check if there's a corresponding element with that ID
          const idRegex = new RegExp(`id=["']${forValue}["']`, 'g');
          const idMatches = material.content.match(idRegex);
          
          if (!idMatches) {
            console.log(`   WARNING: No element found with id="${forValue}"`);
          } else {
            console.log(`   Found ${idMatches.length} element(s) with id="${forValue}"`);
          }
        });
      } else {
        console.log('No label elements with for attributes found');
      }
      
      // Check for HTML structure issues
      if (material.content.includes('<!DOCTYPE html>')) {
        console.log('Material contains a complete HTML document structure');
        
        // Check if there are any forms
        const formRegex = /<form[^>]*>/g;
        const formMatches = material.content.match(formRegex);
        
        if (formMatches) {
          console.log(`Found ${formMatches.length} form elements`);
        } else {
          console.log('No form elements found');
        }
      } else {
        console.log('Material does not contain a complete HTML document structure');
      }
    });
    
  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the function
checkHtmlContent();
