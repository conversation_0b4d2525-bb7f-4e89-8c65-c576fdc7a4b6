export interface QuizTopic {
  id: string;
  title: string;
  description: string;
  questionCount: number;
  points: number;
  difficulty: "easy" | "medium" | "hard";
  isPremium: boolean;
  icon?: string;
  category: string;
}

export const featuredQuizzes: QuizTopic[] = [
  {
    id: "cissp",
    title: "CISSP Prep",
    description: "Comprehensive questions to prepare for the CISSP certification exam. Test your knowledge and get ready!",
    questionCount: 20,
    points: 100,
    difficulty: "medium",
    isPremium: true,
    category: "Cybersecurity"
  },
  {
    id: "ethical-hacking",
    title: "Ethical Hacking",
    description: "Test your knowledge of penetration testing techniques and ethical hacking methodologies.",
    questionCount: 15,
    points: 150,
    difficulty: "hard",
    isPremium: false,
    category: "Cybersecurity"
  },
  {
    id: "gdpr",
    title: "GDPR",
    description: "Stay up-to-date with data protection regulations and compliance requirements for the EU.",
    questionCount: 10,
    points: 80,
    difficulty: "easy",
    isPremium: true,
    category: "Compliance"
  }
];

export const popularQuizzes: QuizTopic[] = [
  {
    id: "network-security",
    title: "Network Security",
    description: "Test your knowledge of network security concepts, protocols, and best practices.",
    questionCount: 25,
    points: 120,
    difficulty: "medium",
    isPremium: true,
    category: "Cybersecurity"
  },
  {
    id: "web-security",
    title: "Web Security",
    description: "Learn about common web vulnerabilities, OWASP Top 10, and secure coding practices.",
    questionCount: 18,
    points: 90,
    difficulty: "medium",
    isPremium: false,
    category: "Cybersecurity"
  },
  {
    id: "cryptography",
    title: "Cryptography",
    description: "Explore encryption algorithms, hashing functions, and cryptographic protocols.",
    questionCount: 15,
    points: 110,
    difficulty: "hard",
    isPremium: true,
    category: "Cybersecurity"
  }
];

export const allQuizzes: QuizTopic[] = [
  ...featuredQuizzes,
  ...popularQuizzes,
  {
    id: "cloud-security",
    title: "Cloud Security",
    description: "Learn about securing cloud environments, shared responsibility models, and cloud-specific threats.",
    questionCount: 22,
    points: 130,
    difficulty: "medium",
    isPremium: true,
    category: "Cloud"
  },
  {
    id: "incident-response",
    title: "Incident Response",
    description: "Test your knowledge of incident handling, digital forensics, and security operations.",
    questionCount: 20,
    points: 100,
    difficulty: "medium",
    isPremium: false,
    category: "Operations"
  },
  {
    id: "security-awareness",
    title: "Security Awareness",
    description: "Learn about social engineering, phishing, and security best practices for end users.",
    questionCount: 12,
    points: 60,
    difficulty: "easy",
    isPremium: false,
    category: "Awareness"
  }
];
