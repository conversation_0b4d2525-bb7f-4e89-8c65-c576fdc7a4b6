import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import BottomNavigation from "@/components/BottomNavigation";
import DesktopSideNav from "@/components/DesktopSideNav";
import { UserCircle, LogOut, Settings, CreditCard, Calendar, CheckCircle, Lock } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { useNavigate, Link } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { getUserSubscriptionStatus, getUserSubscriptionDetails } from "@/utils/auth-helpers";
import { getQuizzesTaken, getAverageScore } from "@/utils/user-stats";

const ProfilePage = () => {
  const { toast } = useToast();
  const { user, signOut } = useAuth();
  const navigate = useNavigate();
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [subscriptionStatus, setSubscriptionStatus] = useState<"Free" | "Premium" | "Expired">("Free");
  const [subscriptionDetails, setSubscriptionDetails] = useState<any>(null);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
  });
  const [quizzesTaken, setQuizzesTaken] = useState(0);
  const [averageScore, setAverageScore] = useState(0);

  // Check if user is authenticated and redirect if not
  useEffect(() => {
    if (!user && !isLoading) {
      console.log("No user found, redirecting to login");
      navigate("/auth");
      return;
    }
  }, [user, isLoading, navigate]);

  // Initialize form data with user information when available
  useEffect(() => {
    setIsLoading(true);

    if (user) {
      // Get the user's full name from metadata if available
      const fullName = user.user_metadata?.full_name || user.user_metadata?.name || "";

      setFormData({
        name: fullName,
        email: user.email || "",
      });

      // Main async function to fetch all user data
      const fetchUserData = async () => {
        try {
          // Fetch subscription data
          const status = await getUserSubscriptionStatus(user);
          setSubscriptionStatus(status);

          // Get subscription details if user is subscribed
          if (status === "Premium") {
            const details = await getUserSubscriptionDetails(user);
            setSubscriptionDetails(details);
          }

          // Fetch user statistics
          console.log("Fetching user quiz statistics...");
          const quizCount = await getQuizzesTaken(user);
          console.log("Quizzes taken:", quizCount);

          const avgScore = await getAverageScore(user);
          console.log("Average score:", avgScore);

          setQuizzesTaken(quizCount);
          setAverageScore(avgScore);
        } catch (error) {
          console.error("Error fetching user data:", error);
          // Set default values in case of error
          setQuizzesTaken(0);
          setAverageScore(0);
        } finally {
          setIsLoading(false);
        }
      };

      // Execute the main fetch function
      fetchUserData();
    } else {
      setIsLoading(false);
    }
  }, [user]);

  // User statistics are now fetched from the database

  const handleSaveProfile = async () => {
    try {
      if (!user) return;

      // Update user metadata in Supabase
      const { error } = await supabase.auth.updateUser({
        data: { full_name: formData.name }
      });

      if (error) {
        throw error;
      }

      setIsEditing(false);
      toast({
        title: "Profile updated",
        description: "Your profile has been successfully updated.",
      });
    } catch (error) {
      console.error("Error updating profile:", error);
      toast({
        title: "Update failed",
        description: "There was an error updating your profile. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleLogout = async () => {
    try {
      await signOut();
      toast({
        title: "Logged out",
        description: "You have been successfully logged out.",
      });
      // Navigate to homepage after logout
      navigate("/");
    } catch (error) {
      toast({
        title: "Logout failed",
        description: "An error occurred while logging out.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="flex min-h-screen cyber-grid-bg">
      {/* Desktop Side Navigation */}
      <DesktopSideNav />

      <div className="flex flex-col flex-1 pb-16 w-full md:max-w-[calc(100%-16rem)]">
        <header className="p-4 border-b bg-background/95 backdrop-blur-sm sticky top-0 z-10">
          <div className="flex items-center justify-between">
            <h1 className="text-lg font-medium">Profile</h1>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleLogout}
              className="text-muted-foreground"
            >
              <LogOut className="h-4 w-4 mr-1" />
              Logout
            </Button>
          </div>
        </header>

        <div className="p-4">
          {/* Profile Card */}
          <Card className="cyber-card p-6 mb-6">
            <div className="flex flex-col items-center mb-4">
              <div className="relative mb-3">
                <div className="h-20 w-20 rounded-full bg-cyber-primary/10 flex items-center justify-center border-2 border-cyber-primary/40">
                  <UserCircle className="h-12 w-12 text-cyber-primary" />
                </div>
                {!isLoading && subscriptionStatus === "Premium" && (
                  <div className="absolute -top-1 -right-1 bg-cyber-accent text-white text-xs px-2 py-0.5 rounded-full">
                    PRO
                  </div>
                )}
              </div>
              {isLoading ? (
                <div className="space-y-2 w-full">
                  <div className="h-6 w-32 mx-auto bg-muted animate-pulse rounded"></div>
                  <div className="h-4 w-48 mx-auto bg-muted animate-pulse rounded"></div>
                </div>
              ) : (
                <>
                  <h2 className="text-xl font-bold">{formData.name || "No name provided"}</h2>
                  <p className="text-sm text-muted-foreground">{formData.email || "No email available"}</p>
                </>
              )}
            </div>

            {isLoading ? (
              <div className="h-10 w-full bg-muted animate-pulse rounded mt-4"></div>
            ) : isEditing ? (
              <div className="space-y-4">
                <div>
                  <Label htmlFor="name">Display Name</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    disabled={isLoading}
                  />
                </div>
                <div>
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    disabled={true} // Email cannot be changed directly
                    title="Email cannot be changed directly. Please contact support."
                  />
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    className="flex-1"
                    onClick={() => setIsEditing(false)}
                    disabled={isLoading}
                  >
                    Cancel
                  </Button>
                  <Button
                    className="flex-1 bg-cyber-primary hover:bg-cyber-primary/90"
                    onClick={handleSaveProfile}
                    disabled={isLoading}
                  >
                    Save Changes
                  </Button>
                </div>
              </div>
            ) : (
              <Button
                variant="outline"
                className="w-full"
                onClick={() => setIsEditing(true)}
                disabled={isLoading}
              >
                <Settings className="h-4 w-4 mr-2" />
                Edit Profile
              </Button>
            )}
          </Card>

          {/* Subscription Card */}
          <Card className="cyber-card mb-6">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Subscription</h3>
                <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                  subscriptionStatus === "Premium"
                    ? "bg-cyber-accent/20 text-cyber-accent"
                    : subscriptionStatus === "Expired"
                      ? "bg-amber-500/20 text-amber-600"
                      : "bg-cyber-neutral/20 text-cyber-neutral"
                }`}>
                  {subscriptionStatus === "Premium"
                    ? "Premium"
                    : subscriptionStatus === "Expired"
                      ? "Expired"
                      : "Free"}
                </div>
              </div>

              {(subscriptionStatus === "Premium" || subscriptionStatus === "Expired") && subscriptionDetails ? (
                <div className="space-y-4">
                  <div className="flex items-center">
                    <CreditCard className="h-5 w-5 text-cyber-primary mr-3" />
                    <div>
                      <p className="text-sm font-medium">Plan: {subscriptionDetails.plan_id ? subscriptionDetails.plan_id.charAt(0).toUpperCase() + subscriptionDetails.plan_id.slice(1) : 'Premium'}</p>
                      <p className="text-xs text-muted-foreground">₦{subscriptionDetails.amount_paid || '0'} paid</p>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <Calendar className="h-5 w-5 text-cyber-primary mr-3" />
                    <div>
                      <p className="text-sm font-medium">
                        {subscriptionStatus === "Expired" ? "Expired on" : "Valid until"}
                      </p>
                      <p className={`text-xs ${subscriptionStatus === "Expired" ? "text-amber-600" : "text-muted-foreground"}`}>
                        {subscriptionDetails.end_date ? new Date(subscriptionDetails.end_date).toLocaleDateString() : 'N/A'}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                    <div>
                      <p className="text-sm font-medium">Benefits</p>
                      <p className="text-xs text-muted-foreground">
                        {!subscriptionDetails.plan_id ? "Access to all premium features" :
                         subscriptionDetails.plan_id === "basic" ? "Access to 4 quiz domains, 400 questions weekly" :
                         subscriptionDetails.plan_id === "pro" ? "Access to all quiz domains, Unlimited questions, Cancel anytime" :
                         "Everything in Pro, Community Access, 24/7 Priority Support, and more"}
                      </p>
                    </div>
                  </div>

                  {subscriptionStatus === "Expired" && (
                    <Link to="/#pricing" className="block mt-4">
                      <Button className="w-full bg-amber-500 hover:bg-amber-600">
                        Renew Subscription
                      </Button>
                    </Link>
                  )}
                </div>
              ) : (
                <div className="space-y-4">
                  <p className="text-sm text-muted-foreground">You are currently on the free plan with limited access.</p>

                  <div className="flex items-center">
                    <Lock className="h-5 w-5 text-cyber-neutral mr-3" />
                    <p className="text-sm">Upgrade to unlock premium features</p>
                  </div>

                  <Link to="/#pricing" className="block">
                    <Button className="w-full bg-cyber-accent hover:bg-cyber-accent/90">
                      View Pricing Plans
                    </Button>
                  </Link>
                </div>
              )}
            </div>
          </Card>

          {/* Stats Card */}
          <Card className="cyber-card mb-6">
            <div className="grid grid-cols-2 divide-x">
              <div className="p-4 text-center">
                {isLoading ? (
                  <div className="flex flex-col items-center">
                    <div className="h-8 w-16 bg-muted animate-pulse rounded mb-2 mx-auto"></div>
                    <div className="h-4 w-24 bg-muted animate-pulse rounded mx-auto"></div>
                  </div>
                ) : (
                  <>
                    <p className="text-2xl font-bold text-cyber-primary">{quizzesTaken}</p>
                    <p className="text-xs text-muted-foreground">Quizzes Taken</p>
                  </>
                )}
              </div>
              <div className="p-4 text-center">
                {isLoading ? (
                  <div className="flex flex-col items-center">
                    <div className="h-8 w-16 bg-muted animate-pulse rounded mb-2 mx-auto"></div>
                    <div className="h-4 w-24 bg-muted animate-pulse rounded mx-auto"></div>
                  </div>
                ) : (
                  <>
                    <p className="text-2xl font-bold text-cyber-accent">{averageScore}%</p>
                    <p className="text-xs text-muted-foreground">Average Score</p>
                  </>
                )}
              </div>
            </div>
          </Card>




        </div>

        <BottomNavigation />
      </div>
    </div>
  );
};

export default ProfilePage;
