# Email Configuration Guide for SecQuiz

This guide explains how the email system is configured for SecQuiz, focusing on authentication emails (signup verification, password reset, magic links).

## Current Configuration

SecQuiz uses [Resend](https://resend.com) as the email service provider, configured through Supabase's SMTP settings.

### Key Components

1. **Custom Domain**: `secquiz.app`
   - Sender email: `<EMAIL>`
   - Sender name: `Secquiz`

2. **SMTP Configuration**:
   - Host: `smtp.resend.com`
   - Port: `465` (secure SMTP)
   - Username: `resend`
   - Password: Your Resend API key (starts with `re_`)

3. **Custom Email Templates**:
   - Located in `supabase/email-templates/`
   - `confirmation.html` - For email verification
   - `reset-password.html` - For password reset

## How to Test Email Delivery

We've created several scripts to help you test and troubleshoot email delivery:

### 1. Test Authentication Emails

```bash
npm run test:auth-email
```

This script will:
- Prompt you for an email address
- Ask which type of authentication email to send (signup, password reset, or magic link)
- Send the selected email type through Supabase Auth

### 2. Test Direct Resend Integration

```bash
npm run test:resend
```

This script will:
- Send a password reset email through Supabase Auth
- Use your configured Resend SMTP settings

## Troubleshooting Email Delivery Issues

If users are not receiving authentication emails, follow these steps:

### 1. Check Resend Dashboard

1. Log in to your [Resend Dashboard](https://resend.com/dashboard)
2. Check the "Events" tab to see if emails are being sent
3. Look for any delivery failures or bounces

### 2. Verify SMTP Configuration

1. Run the SMTP configuration script:
   ```bash
   npm run apply:smtp
   ```
2. Enter your Resend API key when prompted
3. This will update your `supabase/config.toml` file with the correct settings

### 3. Test Email Delivery

1. Run the authentication email test:
   ```bash
   npm run test:auth-email
   ```
2. Send a test email to yourself
3. Check both your inbox and spam folder

### 4. Common Issues and Solutions

#### Emails Going to Spam

- Ensure your domain has proper SPF, DKIM, and DMARC records
- Use a recognizable sender name and email address
- Include clear unsubscribe options in marketing emails

#### Emails Not Being Sent

- Check if your Resend API key is valid and not expired
- Verify that the SMTP settings in Supabase match those in this guide
- Ensure your Resend account is in good standing (not suspended)

#### Incorrect Email Content

- Check the email templates in `supabase/email-templates/`
- Verify that the templates are properly formatted
- Test with different email clients to ensure compatibility

## Maintaining Email Configuration

### Updating Email Templates

1. Edit the HTML files in `supabase/email-templates/`
2. Test the changes using the test scripts
3. Apply the changes to your Supabase project

### Changing Email Provider

If you need to switch from Resend to another provider:

1. Update the SMTP settings in `supabase/config.toml`
2. Update the documentation to reflect the new provider
3. Test thoroughly before deploying to production

## Email Service Comparison

| Feature | Resend | SendGrid | Mailgun | Postmark |
|---------|--------|----------|---------|----------|
| Free Tier | 100/day | 100/day | 5,000/month | No |
| Pricing | $0.80/1000 | $0.80/1000 | $0.80/1000 | $1.50/1000 |
| Deliverability | Excellent | Good | Good | Excellent |
| API | Simple | Complex | Moderate | Simple |
| Dashboard | Clean | Complex | Moderate | Clean |

## Resources

- [Resend Documentation](https://resend.com/docs)
- [Supabase Auth Documentation](https://supabase.com/docs/guides/auth)
- [Email Deliverability Guide](https://www.twilio.com/blog/email-deliverability-best-practices)
