import { useState, useEffect } from "react";
import { Loader2, <PERSON><PERSON><PERSON>, Search } from "lucide-react";
import Navbar from "@/components/Navbar";
import BottomNavigation from "@/components/BottomNavigation";
import LearningCard from "@/components/LearningCard";
import { Input } from "@/components/ui/input";

import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useAuth } from "@/hooks/use-auth";
import { fetchAllLearningMaterials, convertToUIFormat, LearningMaterialForUI } from "@/utils/fetch-learning-materials";
import { canAccessTopicSync, canAccessTopic, PUBLIC_TOPICS, AUTHENTICATED_TOPICS } from "@/utils/topic-access";
import { motion } from "framer-motion";

const LearningMaterialsPage = () => {
  const { user } = useAuth();
  const [materials, setMaterials] = useState<LearningMaterialForUI[]>([]);
  const [filteredMaterials, setFilteredMaterials] = useState<LearningMaterialForUI[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [filter, setFilter] = useState("all"); // all, free, premium

  useEffect(() => {
    const loadMaterials = async () => {
      try {
        setIsLoading(true);
        const allMaterials = await fetchAllLearningMaterials();
        const uiMaterials = convertToUIFormat(allMaterials);
        setMaterials(uiMaterials);
        setFilteredMaterials(uiMaterials);
      } catch (error) {
        console.error("Error loading learning materials:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadMaterials();
  }, []);

  // Filter materials based on search query and filter type
  useEffect(() => {
    let filtered = [...materials];

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (material) =>
          material.title.toLowerCase().includes(query) ||
          material.summary.toLowerCase().includes(query) ||
          (material.topicTitle && material.topicTitle.toLowerCase().includes(query))
      );
    }

    // Apply type filter
    if (filter === "free") {
      filtered = filtered.filter((material) => !material.isPremium);
    } else if (filter === "premium") {
      filtered = filtered.filter((material) => material.isPremium);
    }

    setFilteredMaterials(filtered);
  }, [searchQuery, filter, materials]);

  // Check if user can access a material
  const canAccessMaterial = (material: LearningMaterialForUI) => {
    // Always allow access if isPremium is false
    if (!material.isPremium) {
      return true;
    }

    // If material has no topic title, check based on premium status
    if (!material.topicTitle) {
      return !material.isPremium;
    }

    // Check if this is a free material (either public or for authenticated users)
    const isFreeForRegistered = PUBLIC_TOPICS.includes(material.topicTitle) ||
                               AUTHENTICATED_TOPICS.includes(material.topicTitle);

    // If the material is public, allow access to everyone
    if (PUBLIC_TOPICS.includes(material.topicTitle)) {
      return true;
    }

    // If the user is logged in and the material is free for registered users, allow access
    if (user && isFreeForRegistered) {
      return true;
    }

    // For premium content, do a synchronous check first
    const syncAccess = canAccessTopicSync(material.topicTitle, material.topicId, user);

    // If sync check says user can access, return true immediately
    if (syncAccess) return true;

    // If sync check says user can't access, do an async check that will update the UI
    // Store the material ID to track which ones we're checking
    const materialKey = `checking_access_${material.id}`;

    // Only do the check if we haven't already started it
    if (!localStorage.getItem(materialKey)) {
      localStorage.setItem(materialKey, "true");

      // Do the async check
      canAccessTopic(material.topicTitle, material.topicId, user)
        .then(asyncAccess => {
          if (asyncAccess) {
            // If async check says user can access, update the UI
            // We'll do this by forcing a re-render of the component
            setFilteredMaterials(prev => [...prev]);
          }
          // Clean up
          localStorage.removeItem(materialKey);
        })
        .catch(error => {
          console.error("Error checking async access:", error);
          localStorage.removeItem(materialKey);
        });
    }

    return syncAccess;
  };

  if (isLoading) {
    return (
      <div className="flex flex-col min-h-screen">
        <Navbar />
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p>Loading learning materials...</p>
          </div>
        </div>
        <BottomNavigation />
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-indigo-900">
      <Navbar />
      <main className="flex-1 container px-4 py-6 mx-auto">
        <div className="mb-8">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h1 className="text-3xl font-bold text-white mb-2">Learning Materials</h1>
            <p className="text-indigo-200 mb-6">
              Explore our comprehensive learning materials to master cybersecurity concepts
            </p>
          </motion.div>

          {/* Search and filter */}
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                type="text"
                placeholder="Search learning materials..."
                className="pl-10"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Tabs defaultValue="all" value={filter} onValueChange={setFilter} className="w-full md:w-auto">
              <TabsList className="w-full md:w-auto">
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="free">Free</TabsTrigger>
                <TabsTrigger value="premium">Premium</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          {/* Learning materials grid */}
          {filteredMaterials.length > 0 ? (
            <motion.div
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              {filteredMaterials.map((material) => {
                // Check if the user can access this material
                const canAccess = canAccessMaterial(material);

                return (
                  <LearningCard
                    key={material.id}
                    id={material.id}
                    title={material.title}
                    summary={material.summary}
                    topicId={material.topicId}
                    topicTitle={material.topicTitle}
                    isPremium={material.isPremium}
                  />
                );
              })}
            </motion.div>
          ) : (
            <div className="text-center py-12">
              <BookOpen className="h-12 w-12 text-indigo-300 mx-auto mb-4" />
              <h3 className="text-xl font-medium text-white mb-2">No learning materials found</h3>
              <p className="text-indigo-200 mb-6">
                {searchQuery
                  ? "Try adjusting your search or filters"
                  : "We'll be adding more learning materials soon!"}
              </p>
            </div>
          )}
        </div>
      </main>
      <BottomNavigation />
    </div>
  );
};

export default LearningMaterialsPage;
