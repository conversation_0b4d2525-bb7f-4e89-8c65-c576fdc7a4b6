import { supabase } from "@/integrations/supabase/client";

// Function to ensure the user_profiles table exists
export async function ensureUserProfilesTable() {
  try {
    // Check if the table already exists by directly querying it
    const { data, error: checkError } = await supabase
      .from('user_profiles')
      .select('id')
      .limit(1);

    // If there's no error, the table exists
    const existingTable = !checkError;

    if (checkError && !checkError.message.includes('No rows found')) {
      console.error("Error checking if user_profiles table exists:", checkError);
      return { success: false, error: checkError.message };
    }

    // If the table already exists, we're done
    if (existingTable) {
      console.log("user_profiles table already exists");
      return { success: true };
    }

    // Create the user_profiles table
    console.log("Creating user_profiles table...");

    // Try to create the table using SQL
    const createTableSQL = `
    CREATE TABLE IF NOT EXISTS public.user_profiles (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL UNIQUE,
      is_subscribed BOOLEAN DEFAULT false,
      is_admin BOOLEAN DEFAULT false,
      subscription_expires_at TIMESTAMPTZ,
      created_at TIMESTAMPTZ DEFAULT now(),
      updated_at TIMESTAMPTZ DEFAULT now()
    );

    -- Enable Row Level Security
    ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;

    -- Check if user_id column exists in the table
    DO $$
    DECLARE
        user_id_exists BOOLEAN;
    BEGIN
        -- Check if user_id column exists
        SELECT EXISTS (
            SELECT FROM information_schema.columns
            WHERE table_schema = 'public'
            AND table_name = 'user_profiles'
            AND column_name = 'user_id'
        ) INTO user_id_exists;

        IF user_id_exists THEN
            -- Create policies using user_id
            CREATE POLICY "Users can view their own profile"
              ON public.user_profiles
              FOR SELECT
              USING (auth.uid() = user_id);

            CREATE POLICY "Admins can view all profiles"
              ON public.user_profiles
              FOR SELECT
              USING (EXISTS (SELECT 1 FROM public.admin_users WHERE user_id = auth.uid()));

            CREATE POLICY "Admins can update all profiles"
              ON public.user_profiles
              FOR UPDATE
              USING (EXISTS (SELECT 1 FROM public.admin_users WHERE user_id = auth.uid()));
        ELSE
            -- Create policies using id
            CREATE POLICY "Users can view their own profile"
              ON public.user_profiles
              FOR SELECT
              USING (auth.uid() = id);

            CREATE POLICY "Admins can view all profiles"
              ON public.user_profiles
              FOR SELECT
              USING (EXISTS (SELECT 1 FROM public.admin_users WHERE user_id = auth.uid()));

            CREATE POLICY "Admins can update all profiles"
              ON public.user_profiles
              FOR UPDATE
              USING (EXISTS (SELECT 1 FROM public.admin_users WHERE user_id = auth.uid()));
        END IF;
    END $$;
    `;

    // Execute the SQL
    const { error: sqlError } = await supabase.rpc('run_sql', { sql: createTableSQL });

    if (sqlError) {
      // If the error is that the function doesn't exist, we'll try a different approach
      if (sqlError.message.includes('function "run_sql" does not exist')) {
        // Try creating the table using the REST API
        const { error: createError } = await supabase
          .from('user_profiles')
          .insert([{
            user_id: '00000000-0000-0000-0000-000000000000', // Dummy ID that will be replaced
            is_subscribed: false,
            is_admin: false
          }]);

        // If the error is that the table doesn't exist, that's expected
        if (createError && createError.message.includes('relation "user_profiles" does not exist')) {
          console.log("Could not create user_profiles table automatically. Please run migrations.");
          return { success: false, error: "Could not create user_profiles table automatically. Please run migrations." };
        } else if (createError) {
          console.error("Error creating user_profiles table:", createError);
          return { success: false, error: createError.message };
        }
      } else {
        console.error("Error executing SQL to create user_profiles table:", sqlError);
        return { success: false, error: sqlError.message };
      }
    }

    console.log("user_profiles table created successfully");
    return { success: true };
  } catch (error: any) {
    console.error("Error ensuring user_profiles table exists:", error);
    return { success: false, error: error.message };
  }
}

// Function to check the structure of the admin_users table
export async function checkAdminUsersTable() {
  try {
    console.log("Checking admin_users table structure...");

    // Get the columns of the admin_users table
    const { data, error } = await supabase
      .from('admin_users')
      .select('*')
      .limit(1);

    if (error) {
      console.error("Error checking admin_users table:", error);
      return { success: false, error: error.message, columns: [] };
    }

    // Log the structure
    console.log("admin_users table data sample:", data);

    // Get the column names
    const columns = data && data.length > 0 ? Object.keys(data[0]) : [];
    console.log("admin_users table columns:", columns);

    return { success: true, columns };
  } catch (error: any) {
    console.error("Error checking admin_users table:", error);
    return { success: false, error: error.message, columns: [] };
  }
}

// Function to use the admin_users table for user management
export async function useAdminUsersTable() {
  try {
    // First check if the admin_users table exists and get its structure
    const { success, columns, error } = await checkAdminUsersTable();

    if (!success) {
      return { success: false, error };
    }

    // Get all users from auth.users
    const { data: authUsers, error: authError } = await supabase.rpc('get_all_users_simple');

    if (authError) {
      console.error("Error getting auth users:", authError);
      return { success: false, error: authError.message };
    }

    // Get all admin users
    const { data: adminUsers, error: adminError } = await supabase
      .from('admin_users')
      .select('*');

    if (adminError) {
      console.error("Error getting admin users:", adminError);
      return { success: false, error: adminError.message };
    }

    // Create a map of admin users by user_id
    const adminMap = {};
    if (adminUsers) {
      adminUsers.forEach(admin => {
        // Assuming admin_users has a user_id column
        if (admin.user_id) {
          adminMap[admin.user_id] = admin;
        }
      });
    }

    // Ensure user_profiles table exists
    const { success: tableExists } = await ensureUserProfilesTable();

    let userProfiles = [];
    const profileMap = {};

    // If user_profiles table exists, get subscription data
    if (tableExists) {
      // We'll assume the user_id column exists if the table exists
      // This is a safer approach than trying to query information_schema
      const hasUserIdColumn = true;

      // Get all user profiles
      const { data: profiles, error: profilesError } = await supabase
        .from('user_profiles')
        .select('*');

      if (profilesError) {
        console.error("Error getting user profiles:", profilesError);
        // Continue without profiles data
      } else if (profiles) {
        userProfiles = profiles;

        // Create a map of user profiles by the appropriate key
        profiles.forEach(profile => {
          if (hasUserIdColumn && profile.user_id) {
            profileMap[profile.user_id] = profile;
          } else if (profile.id) {
            profileMap[profile.id] = profile;
          }
        });
      }
    }

    // Get all subscriptions from the subscriptions table if it exists
    const activeSubscriptionsMap = {};
    const expiredSubscriptionsMap = {};
    try {
      // Check if the subscriptions table exists by directly querying it
      const { data, error: subscriptionsError } = await supabase
        .from('subscriptions')
        .select('id')
        .limit(1);

      // If there's no error, the table exists
      const subscriptionsTableExists = !subscriptionsError;

      if (subscriptionsTableExists) {
        // Get all active subscriptions
        const { data: activeSubscriptions } = await supabase
          .from('subscriptions')
          .select('*')
          .eq('is_active', true)
          .gte('end_date', new Date().toISOString());

        if (activeSubscriptions && activeSubscriptions.length > 0) {
          // Create a map of active subscriptions by user_id
          activeSubscriptions.forEach(sub => {
            if (sub.user_id) {
              activeSubscriptionsMap[sub.user_id] = sub;
            }
          });
        }

        // Get all expired subscriptions
        const { data: expiredSubscriptions } = await supabase
          .from('subscriptions')
          .select('*')
          .or('is_active.eq.false,end_date.lt.' + new Date().toISOString());

        if (expiredSubscriptions && expiredSubscriptions.length > 0) {
          // Create a map of expired subscriptions by user_id
          expiredSubscriptions.forEach(sub => {
            if (sub.user_id) {
              expiredSubscriptionsMap[sub.user_id] = sub;
            }
          });
        }
      }
    } catch (subError) {
      console.error("Error getting subscriptions:", subError);
      // Continue anyway, we'll use the user_profiles data
    }

    // Combine the data
    const users = authUsers.map(user => {
      const profile = profileMap[user.id];
      const activeSubscription = activeSubscriptionsMap[user.id];
      const expiredSubscription = expiredSubscriptionsMap[user.id];

      // Check admin status from both tables - a user is admin if they're in admin_users OR if their profile has is_admin=true
      const isAdmin = adminMap[user.id] ? true : (profile && profile.is_admin ? true : false);

      // Determine subscription status
      let isSubscribed = false;
      let subscriptionStatus = 'free';
      let expiresAt = null;

      // Check if user has an active subscription
      if (activeSubscription) {
        isSubscribed = true;
        subscriptionStatus = 'active';
        expiresAt = activeSubscription.end_date;
      }
      // Check if user has a profile with active subscription
      else if (profile && profile.is_subscribed) {
        // Check if the subscription has expired
        if (profile.subscription_expires_at) {
          const expDate = new Date(profile.subscription_expires_at);
          const now = new Date();

          if (expDate > now) {
            isSubscribed = true;
            subscriptionStatus = 'active';
            expiresAt = profile.subscription_expires_at;
          } else {
            isSubscribed = false;
            subscriptionStatus = 'expired';
            expiresAt = profile.subscription_expires_at;
          }
        } else {
          isSubscribed = true;
          subscriptionStatus = 'active';
        }
      }
      // Check if user has an expired subscription
      else if (expiredSubscription) {
        isSubscribed = false;
        subscriptionStatus = 'expired';
        expiresAt = expiredSubscription.end_date;
      }

      return {
        id: user.id,
        email: user.email,
        created_at: user.created_at,
        last_sign_in_at: user.last_sign_in_at,
        is_admin: isAdmin,
        is_subscribed: isSubscribed,
        subscription_expires_at: expiresAt,
        subscription_status: subscriptionStatus
      };
    });

    return { success: true, users };
  } catch (error: any) {
    console.error("Error using admin_users table:", error);
    return { success: false, error: error.message };
  }
}
