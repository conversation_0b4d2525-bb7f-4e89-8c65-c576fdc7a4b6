import <PERSON> from 'papaparse';
import { v4 as uuidv4 } from 'uuid';

// Define the expected CSV structure
export interface QuestionCSVRow {
  question_text: string;
  option_a: string;
  option_b: string;
  option_c: string;
  option_d: string;
  correct_answer: string; // Should be A, B, C, or D
  explanation: string;
  difficulty?: string; // Optional, defaults to 'medium'
}

// Define the structure for a validated question ready for database insertion
export interface ValidatedQuestion {
  id: string;
  topic_id: string;
  question_text: string;
  options: {
    A: string;
    B: string;
    C: string;
    D: string;
  };
  correct_answer: string;
  explanation: string;
  difficulty: string;
  created_at: string;
  updated_at: string;
}

// Define the structure for import results
export interface ImportResult {
  success: boolean;
  totalRows: number;
  validQuestions: ValidatedQuestion[];
  errors: { row: number; message: string }[];
}

/**
 * Parse a CSV file and validate the questions
 * @param file The CSV file to parse
 * @param topicId The ID of the topic to associate the questions with
 * @returns A promise that resolves to the import result
 */
export async function parseQuestionCSV(file: File, topicId: string): Promise<ImportResult> {
  return new Promise((resolve) => {
    const result: ImportResult = {
      success: false,
      totalRows: 0,
      validQuestions: [],
      errors: [],
    };

    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      complete: (results) => {
        result.totalRows = results.data.length;
        
        // Validate each row
        results.data.forEach((row: any, index: number) => {
          try {
            const validatedQuestion = validateQuestionRow(row, topicId, index);
            if (validatedQuestion) {
              result.validQuestions.push(validatedQuestion);
            }
          } catch (error) {
            result.errors.push({
              row: index + 1, // +1 to account for header row
              message: error instanceof Error ? error.message : String(error),
            });
          }
        });

        // Set success flag if we have at least one valid question and no errors
        result.success = result.validQuestions.length > 0;
        
        resolve(result);
      },
      error: (error) => {
        result.errors.push({ row: 0, message: error.message });
        resolve(result);
      },
    });
  });
}

/**
 * Validate a single row from the CSV
 * @param row The row data
 * @param topicId The topic ID
 * @param rowIndex The row index for error reporting
 * @returns A validated question object or throws an error
 */
function validateQuestionRow(
  row: any,
  topicId: string,
  rowIndex: number
): ValidatedQuestion {
  // Check required fields
  const requiredFields = [
    'question_text',
    'option_a',
    'option_b',
    'option_c',
    'option_d',
    'correct_answer',
    'explanation',
  ];

  for (const field of requiredFields) {
    if (!row[field] || row[field].trim() === '') {
      throw new Error(`Row ${rowIndex + 1}: Missing required field "${field}"`);
    }
  }

  // Validate correct_answer format
  const correctAnswer = row.correct_answer.trim().toUpperCase();
  if (!['A', 'B', 'C', 'D'].includes(correctAnswer)) {
    throw new Error(
      `Row ${rowIndex + 1}: Invalid correct_answer "${row.correct_answer}". Must be A, B, C, or D.`
    );
  }

  // Validate difficulty if provided
  let difficulty = 'medium'; // Default
  if (row.difficulty) {
    const validDifficulties = ['easy', 'medium', 'hard'];
    const normalizedDifficulty = row.difficulty.trim().toLowerCase();
    
    if (!validDifficulties.includes(normalizedDifficulty)) {
      throw new Error(
        `Row ${rowIndex + 1}: Invalid difficulty "${row.difficulty}". Must be easy, medium, or hard.`
      );
    }
    
    difficulty = normalizedDifficulty;
  }

  // Create the validated question object
  const now = new Date().toISOString();
  
  return {
    id: uuidv4(),
    topic_id: topicId,
    question_text: row.question_text.trim(),
    options: {
      A: row.option_a.trim(),
      B: row.option_b.trim(),
      C: row.option_c.trim(),
      D: row.option_d.trim(),
    },
    correct_answer: correctAnswer,
    explanation: row.explanation.trim(),
    difficulty,
    created_at: now,
    updated_at: now,
  };
}

/**
 * Generate a CSV template for downloading
 * @returns A CSV string with headers
 */
export function generateCSVTemplate(): string {
  const headers = [
    'question_text',
    'option_a',
    'option_b',
    'option_c',
    'option_d',
    'correct_answer',
    'explanation',
    'difficulty',
  ];
  
  const sampleRow = [
    'What is the primary purpose of a firewall?',
    'To prevent physical access to a network',
    'To control network traffic based on security rules',
    'To encrypt data during transmission',
    'To detect malware on endpoints',
    'B',
    'A firewall controls network traffic flow based on predetermined security rules, allowing or blocking traffic based on these rules.',
    'medium',
  ];
  
  // Create CSV content
  const csvContent = Papa.unparse({
    fields: headers,
    data: [sampleRow],
  });
  
  return csvContent;
}
