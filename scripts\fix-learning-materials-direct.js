import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables from the .env file in the parent directory
const envPath = join(__dirname, '..', '.env');
if (fs.existsSync(envPath)) {
  dotenv.config({ path: envPath });
} else {
  console.error('.env file not found at:', envPath);
  process.exit(1);
}

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Supabase credentials not found in environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function fixLearningMaterialsDirect() {
  try {
    console.log('Fixing learning materials with direct SQL...');

    // DPO and DPIA in NDPR
    const dpoId = 'd19b40dc-6b1b-432a-855e-641f36480af9';
    const dpoContent = `
      <div class="learning-content">
        <h1>DPO and DPIA in NDPR</h1>
        <p>This premium content is currently being updated. Please check back later.</p>
        <p>If you continue to experience issues, please contact support.</p>
      </div>
    `;

    // NDPR - Nigeria Data Protection Regulation
    const ndprId = 'f4b448b3-94f1-465c-858b-364ff2828337';
    const ndprContent = `
      <div class="learning-content">
        <h1>NDPR - Nigeria Data Protection Regulation</h1>
        <p>This premium content is currently being updated. Please check back later.</p>
        <p>If you continue to experience issues, please contact support.</p>
      </div>
    `;

    // Update DPO content
    const { data: dpoData, error: dpoError } = await supabase
      .from('learning_materials')
      .update({ content: dpoContent })
      .eq('id', dpoId);

    if (dpoError) {
      console.error('Error updating DPO content:', dpoError);
    } else {
      console.log('Successfully updated DPO content');
    }

    // Update NDPR content
    const { data: ndprData, error: ndprError } = await supabase
      .from('learning_materials')
      .update({ content: ndprContent })
      .eq('id', ndprId);

    if (ndprError) {
      console.error('Error updating NDPR content:', ndprError);
    } else {
      console.log('Successfully updated NDPR content');
    }

    console.log('\nFinished fixing learning materials directly');

  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the function
fixLearningMaterialsDirect();
