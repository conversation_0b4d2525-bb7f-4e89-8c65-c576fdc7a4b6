-- Create a simple function to create the user_profiles table
CREATE OR REPLACE FUNCTION public.create_user_profiles_simple()
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
  -- Create the user_profiles table if it doesn't exist
  CREATE TABLE IF NOT EXISTS public.user_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL UNIQUE,
    is_subscribed BOOLEAN DEFAULT false,
    is_admin BOOLEAN DEFAULT false,
    subscription_expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
  );
  
  -- Enable Row Level Security
  ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
  
  -- Create a basic policy to allow all authenticated users to view and modify the table
  DROP POLICY IF EXISTS "Allow authenticated users full access" ON public.user_profiles;
  CREATE POLICY "Allow authenticated users full access"
    ON public.user_profiles
    FOR ALL
    TO authenticated
    USING (true)
    WITH CHECK (true);
END;
$$;

-- <PERSON> execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.create_user_profiles_simple() TO authenticated;
