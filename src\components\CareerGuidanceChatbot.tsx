import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Progress } from "@/components/ui/progress";
import {
  Bot,
  User,
  Send,
  RotateCcw,
  GraduationCap,
  ArrowRight,
  ExternalLink
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { useAuth } from "@/hooks/use-auth";
import { careerPaths, type CareerPath } from "@/data/careerPaths";
import { Link } from "react-router-dom";

// Types
interface Message {
  id: string;
  type: 'bot' | 'user';
  content: string;
  timestamp: Date;
  options?: string[];
  isTyping?: boolean;
}

interface AssessmentScore {
  technical: number;
  business: number;
  handsOn: number;
  strategic: number;
  communication: number;
  leadership: number;
  analytical: number;
  creative: number;
}

// Remove the local career paths data since we're importing from the data file

// Assessment questions
const assessmentQuestions = [
  {
    id: 1,
    question: "What type of work environment do you prefer?",
    options: [
      { text: "Hands-on technical work with systems and code", scores: { technical: 3, handsOn: 3 } },
      { text: "Strategic planning and policy development", scores: { business: 3, strategic: 3 } },
      { text: "Mix of technical and business activities", scores: { technical: 2, business: 2 } },
      { text: "Client-facing consulting and communication", scores: { communication: 3, business: 2 } }
    ]
  },
  {
    id: 2,
    question: "How do you prefer to solve problems?",
    options: [
      { text: "Deep technical analysis and testing", scores: { technical: 3, analytical: 3 } },
      { text: "Research and documentation review", scores: { analytical: 2, business: 2 } },
      { text: "Collaborative team discussions", scores: { communication: 3, leadership: 2 } },
      { text: "Creative and innovative approaches", scores: { creative: 3, technical: 1 } }
    ]
  },
  {
    id: 3,
    question: "What motivates you most in your work?",
    options: [
      { text: "Finding and fixing security vulnerabilities", scores: { technical: 3, handsOn: 3 } },
      { text: "Ensuring organizational compliance", scores: { business: 3, analytical: 2 } },
      { text: "Leading and mentoring others", scores: { leadership: 3, communication: 2 } },
      { text: "Continuous learning and skill development", scores: { technical: 2, creative: 2 } }
    ]
  },
  {
    id: 4,
    question: "How comfortable are you with high-pressure situations?",
    options: [
      { text: "Thrive under pressure, quick decision-making", scores: { handsOn: 3, analytical: 2 } },
      { text: "Prefer structured, planned approaches", scores: { strategic: 3, business: 2 } },
      { text: "Good with pressure when leading a team", scores: { leadership: 3, communication: 2 } },
      { text: "Better with steady, consistent workload", scores: { analytical: 2, strategic: 2 } }
    ]
  },
  {
    id: 5,
    question: "What's your preferred learning style?",
    options: [
      { text: "Hands-on experimentation and practice", scores: { handsOn: 3, technical: 2 } },
      { text: "Reading documentation and research", scores: { analytical: 3, business: 1 } },
      { text: "Interactive workshops and discussions", scores: { communication: 3, leadership: 1 } },
      { text: "Online courses and structured learning", scores: { strategic: 2, analytical: 2 } }
    ]
  }
];

const CareerGuidanceChatbot = () => {
  const { user } = useAuth();
  const [messages, setMessages] = useState<Message[]>([]);
  const [currentInput, setCurrentInput] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [assessmentStarted, setAssessmentStarted] = useState(false);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [assessmentScores, setAssessmentScores] = useState<AssessmentScore>({
    technical: 0,
    business: 0,
    handsOn: 0,
    strategic: 0,
    communication: 0,
    leadership: 0,
    analytical: 0,
    creative: 0
  });
  const [assessmentComplete, setAssessmentComplete] = useState(false);
  const [recommendedPaths, setRecommendedPaths] = useState<CareerPath[]>([]);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages are added
  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [messages]);

  // Initialize chatbot with welcome message
  useEffect(() => {
    const welcomeMessage: Message = {
      id: '1',
      type: 'bot',
      content: `Hello${user ? ` ${user.user_metadata?.full_name || 'there'}` : ''}! 👋 I'm your Cybersecurity Career Guide. I'll help you discover which cybersecurity career path aligns best with your personality, interests, and skills.\n\nWould you like to start the career assessment?`,
      timestamp: new Date(),
      options: ['Start Assessment', 'Learn More', 'View Career Paths']
    };
    setMessages([welcomeMessage]);
  }, [user]);

  const addMessage = (message: Omit<Message, 'id' | 'timestamp'>) => {
    const newMessage: Message = {
      ...message,
      id: Date.now().toString(),
      timestamp: new Date()
    };
    setMessages(prev => [...prev, newMessage]);
  };

  const simulateTyping = (callback: () => void, delay = 1000) => {
    setIsTyping(true);
    setTimeout(() => {
      setIsTyping(false);
      callback();
    }, delay);
  };

  return (
    <div className="flex flex-col h-full max-w-4xl mx-auto">
      {/* Chat Header */}
      <div className="flex items-center gap-3 p-4 border-b bg-background/95 backdrop-blur-sm">
        <Avatar>
          <AvatarFallback className="bg-cyber-primary text-white">
            <Bot className="h-5 w-5" />
          </AvatarFallback>
        </Avatar>
        <div>
          <h3 className="font-semibold">Career Guidance Assistant</h3>
          <p className="text-sm text-muted-foreground">
            Discover your ideal cybersecurity career path
          </p>
        </div>
        <div className="ml-auto">
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.location.reload()}
            className="gap-2"
          >
            <RotateCcw className="h-4 w-4" />
            Reset
          </Button>
        </div>
      </div>

      {/* Progress Bar (shown during assessment) */}
      {assessmentStarted && !assessmentComplete && (
        <div className="p-4 border-b">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">Assessment Progress</span>
            <span className="text-sm text-muted-foreground">
              {currentQuestionIndex + 1} of {assessmentQuestions.length}
            </span>
          </div>
          <Progress 
            value={((currentQuestionIndex + 1) / assessmentQuestions.length) * 100} 
            className="h-2"
          />
        </div>
      )}

      {/* Chat Messages */}
      <ScrollArea className="flex-1 p-4" ref={scrollAreaRef}>
        <div className="space-y-4">
          <AnimatePresence>
            {messages.map((message) => (
              <motion.div
                key={message.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className={`flex gap-3 ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                {message.type === 'bot' && (
                  <Avatar className="h-8 w-8">
                    <AvatarFallback className="bg-cyber-primary text-white">
                      <Bot className="h-4 w-4" />
                    </AvatarFallback>
                  </Avatar>
                )}
                
                <div className={`max-w-[80%] ${message.type === 'user' ? 'order-first' : ''}`}>
                  <Card className={`p-3 ${
                    message.type === 'user' 
                      ? 'bg-cyber-primary text-white ml-auto' 
                      : 'bg-background'
                  }`}>
                    <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                    
                    {/* Message options */}
                    {message.options && message.type === 'bot' && (
                      <div className="flex flex-wrap gap-2 mt-3">
                        {message.options.map((option, index) => (
                          <Button
                            key={index}
                            variant="outline"
                            size="sm"
                            className="text-xs"
                            onClick={() => handleOptionClick(option)}
                          >
                            {option}
                          </Button>
                        ))}
                      </div>
                    )}
                  </Card>
                  
                  <p className="text-xs text-muted-foreground mt-1 px-1">
                    {message.timestamp.toLocaleTimeString([], { 
                      hour: '2-digit', 
                      minute: '2-digit' 
                    })}
                  </p>
                </div>

                {message.type === 'user' && (
                  <Avatar className="h-8 w-8">
                    <AvatarFallback className="bg-muted">
                      <User className="h-4 w-4" />
                    </AvatarFallback>
                  </Avatar>
                )}
              </motion.div>
            ))}
          </AnimatePresence>

          {/* Typing indicator */}
          {isTyping && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex gap-3"
            >
              <Avatar className="h-8 w-8">
                <AvatarFallback className="bg-cyber-primary text-white">
                  <Bot className="h-4 w-4" />
                </AvatarFallback>
              </Avatar>
              <Card className="p-3 bg-background">
                <div className="flex gap-1">
                  <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" />
                  <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                  <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                </div>
              </Card>
            </motion.div>
          )}
        </div>
      </ScrollArea>

      {/* Input Area */}
      {!assessmentStarted && (
        <div className="p-4 border-t">
          <div className="flex gap-2">
            <Input
              value={currentInput}
              onChange={(e) => setCurrentInput(e.target.value)}
              placeholder="Type your message..."
              onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
              className="flex-1"
            />
            <Button onClick={handleSendMessage} disabled={!currentInput.trim()}>
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );

  const handleOptionClick = (option: string) => {
    // Add user message
    addMessage({
      type: 'user',
      content: option
    });

    // Handle different option types
    if (option === 'Start Assessment') {
      startAssessment();
    } else if (option === 'Learn More') {
      showLearnMore();
    } else if (option === 'View Career Paths') {
      showCareerPaths();
    } else if (option === 'Restart Assessment') {
      restartAssessment();
    } else if (option === 'View Recommendations') {
      showRecommendations();
    } else if (option === 'Explore Learning Materials') {
      showLearningMaterials();
    } else if (option === 'View Detailed Career Info') {
      showDetailedCareerInfo();
    } else if (option === 'Browse Quizzes') {
      handleNavigateToQuizzes();
    } else if (option === 'View Learning Materials') {
      handleNavigateToLearning();
    } else if (option === 'Get Career Advice') {
      showCareerAdvice();
    } else if (option === 'View Other Recommendations') {
      showAllRecommendations();
    } else {
      // Handle assessment answers
      handleAssessmentAnswer(option);
    }
  };

  const handleSendMessage = () => {
    if (!currentInput.trim()) return;

    addMessage({
      type: 'user',
      content: currentInput
    });

    // Simple response for free-form messages
    simulateTyping(() => {
      addMessage({
        type: 'bot',
        content: "I understand you'd like to chat! For the best career guidance, I recommend taking the structured assessment. Would you like to start?",
        options: ['Start Assessment', 'View Career Paths']
      });
    });

    setCurrentInput("");
  };

  const startAssessment = () => {
    setAssessmentStarted(true);
    setCurrentQuestionIndex(0);

    simulateTyping(() => {
      const firstQuestion = assessmentQuestions[0];
      addMessage({
        type: 'bot',
        content: `Great! Let's begin the assessment. This will take about 2-3 minutes.\n\n**Question 1 of ${assessmentQuestions.length}:**\n\n${firstQuestion.question}`,
        options: firstQuestion.options.map(opt => opt.text)
      });
    });
  };

  const handleAssessmentAnswer = (answer: string) => {
    const currentQuestion = assessmentQuestions[currentQuestionIndex];
    const selectedOption = currentQuestion.options.find(opt => opt.text === answer);

    if (selectedOption) {
      // Update scores
      const newScores = { ...assessmentScores };
      Object.entries(selectedOption.scores).forEach(([key, value]) => {
        newScores[key as keyof AssessmentScore] += value;
      });
      setAssessmentScores(newScores);

      // Move to next question or complete assessment
      if (currentQuestionIndex < assessmentQuestions.length - 1) {
        setCurrentQuestionIndex(prev => prev + 1);

        simulateTyping(() => {
          const nextQuestion = assessmentQuestions[currentQuestionIndex + 1];
          addMessage({
            type: 'bot',
            content: `**Question ${currentQuestionIndex + 2} of ${assessmentQuestions.length}:**\n\n${nextQuestion.question}`,
            options: nextQuestion.options.map(opt => opt.text)
          });
        });
      } else {
        completeAssessment(newScores);
      }
    }
  };

  const completeAssessment = (finalScores: AssessmentScore) => {
    setAssessmentComplete(true);

    // Calculate career path matches
    const pathsWithScores = careerPaths.map(path => {
      let matchScore = 0;

      // Scoring logic based on career path requirements
      switch (path.id) {
        case 'grc':
          matchScore = finalScores.business * 0.4 + finalScores.analytical * 0.3 + finalScores.strategic * 0.3;
          break;
        case 'pentest':
          matchScore = finalScores.technical * 0.4 + finalScores.handsOn * 0.4 + finalScores.analytical * 0.2;
          break;
        case 'soc':
          matchScore = finalScores.analytical * 0.4 + finalScores.handsOn * 0.3 + finalScores.technical * 0.3;
          break;
        case 'security-engineering':
          matchScore = finalScores.technical * 0.5 + finalScores.creative * 0.3 + finalScores.handsOn * 0.2;
          break;
        case 'security-consulting':
          matchScore = finalScores.communication * 0.4 + finalScores.business * 0.3 + finalScores.technical * 0.3;
          break;
        case 'security-management':
          matchScore = finalScores.leadership * 0.4 + finalScores.strategic * 0.3 + finalScores.communication * 0.3;
          break;
        default:
          matchScore = 0;
      }

      return {
        ...path,
        matchPercentage: Math.min(Math.round((matchScore / 15) * 100), 100)
      };
    });

    // Sort by match percentage and take top 3
    const topMatches = pathsWithScores
      .sort((a, b) => (b.matchPercentage || 0) - (a.matchPercentage || 0))
      .slice(0, 3);

    setRecommendedPaths(topMatches);

    simulateTyping(() => {
      addMessage({
        type: 'bot',
        content: `🎉 Assessment complete! Based on your responses, I've identified your top cybersecurity career matches. Your personality profile shows strong alignment with these paths:`,
        options: ['View Recommendations', 'Restart Assessment']
      });
    }, 1500);
  };

  const showRecommendations = () => {
    if (recommendedPaths.length === 0) {
      addMessage({
        type: 'bot',
        content: "Please complete the assessment first to get personalized recommendations!",
        options: ['Start Assessment']
      });
      return;
    }

    simulateTyping(() => {
      const recommendationText = recommendedPaths.map((path, index) => {
        return `**${index + 1}. ${path.title}** (${path.matchPercentage}% match)\n${path.description}\n\n💰 Salary: ${path.averageSalary}\n📈 Growth: ${path.growthRate}\n\n**Key Skills:** ${path.skills.join(', ')}\n**Learning Path:** ${path.learningPath.join(' → ')}\n\n**Day-to-Day Activities:**\n${path.dayInLife.slice(0, 3).map(activity => `• ${activity}`).join('\n')}\n\n**Career Progression:**\n• Entry: ${path.careerProgression.entry}\n• Mid-Level: ${path.careerProgression.mid}\n• Senior: ${path.careerProgression.senior}`;
      }).join('\n\n---\n\n');

      addMessage({
        type: 'bot',
        content: `🎯 Here are your top career path recommendations:\n\n${recommendationText}\n\nReady to start learning? I can connect you to relevant quizzes and materials!`,
        options: ['Explore Learning Materials', 'View Detailed Career Info', 'Restart Assessment']
      });
    });
  };

  const showCareerPaths = () => {
    simulateTyping(() => {
      const pathsText = careerPaths.map(path => {
        return `**${path.title}**\n${path.description}\n💰 ${path.averageSalary}`;
      }).join('\n\n');

      addMessage({
        type: 'bot',
        content: `Here are the main cybersecurity career paths I can help you explore:\n\n${pathsText}\n\nTo get personalized recommendations based on your interests and personality, I recommend taking the assessment!`,
        options: ['Start Assessment', 'Learn More']
      });
    });
  };

  const showLearnMore = () => {
    simulateTyping(() => {
      addMessage({
        type: 'bot',
        content: `I'm here to help you discover your ideal cybersecurity career path! Here's how it works:\n\n🔍 **Assessment**: I'll ask you 5 quick questions about your work preferences, problem-solving style, and interests.\n\n🎯 **Analysis**: I'll analyze your responses across 8 personality dimensions to understand what motivates you.\n\n📊 **Recommendations**: You'll get personalized career path suggestions with match percentages, salary info, and learning resources.\n\n🚀 **Next Steps**: I'll connect you to relevant learning materials and quizzes in the app to start your journey!\n\nReady to discover your cybersecurity calling?`,
        options: ['Start Assessment', 'View Career Paths']
      });
    });
  };

  const showLearningMaterials = () => {
    if (recommendedPaths.length === 0) {
      addMessage({
        type: 'bot',
        content: "Please complete the assessment first to get personalized learning recommendations!",
        options: ['Start Assessment']
      });
      return;
    }

    simulateTyping(() => {
      const topPath = recommendedPaths[0];
      const learningContent = `🎓 **Learning Roadmap for ${topPath.title}**\n\nBased on your assessment results, here's your personalized learning path:\n\n${topPath.learningPath.map((step, index) => `${index + 1}. ${step}`).join('\n')}\n\n**Related Topics to Explore:**\n${topPath.relatedTopics.map(topic => `• ${topic}`).join('\n')}\n\n**Recommended Certifications:**\n${topPath.certifications.map(cert => `• ${cert}`).join('\n')}\n\nReady to start your cybersecurity journey? Check out our quizzes and learning materials!`;

      addMessage({
        type: 'bot',
        content: learningContent
      });

      // Add action buttons as a separate message for better UX
      setTimeout(() => {
        addMessage({
          type: 'bot',
          content: "🚀 **Next Steps:**",
          options: ['Browse Quizzes', 'View Learning Materials', 'Get Career Advice']
        });
      }, 1000);
    });
  };

  const showDetailedCareerInfo = () => {
    if (recommendedPaths.length === 0) {
      addMessage({
        type: 'bot',
        content: "Please complete the assessment first!",
        options: ['Start Assessment']
      });
      return;
    }

    simulateTyping(() => {
      const topPath = recommendedPaths[0];
      const detailedInfo = `📋 **Detailed Career Information: ${topPath.title}**\n\n**Overview:**\n${topPath.detailedDescription}\n\n**Work Environment:**\n${topPath.workEnvironment}\n\n**Key Challenges:**\n${topPath.challenges.map(challenge => `• ${challenge}`).join('\n')}\n\n**Rewards & Benefits:**\n${topPath.rewards.map(reward => `• ${reward}`).join('\n')}\n\n**Typical Day Activities:**\n${topPath.dayInLife.map(activity => `• ${activity}`).join('\n')}`;

      addMessage({
        type: 'bot',
        content: detailedInfo,
        options: ['Explore Learning Materials', 'View Other Recommendations', 'Restart Assessment']
      });
    });
  };

  const restartAssessment = () => {
    setAssessmentStarted(false);
    setAssessmentComplete(false);
    setCurrentQuestionIndex(0);
    setAssessmentScores({
      technical: 0,
      business: 0,
      handsOn: 0,
      strategic: 0,
      communication: 0,
      leadership: 0,
      analytical: 0,
      creative: 0
    });
    setRecommendedPaths([]);

    simulateTyping(() => {
      addMessage({
        type: 'bot',
        content: "Assessment reset! Ready to start fresh?",
        options: ['Start Assessment', 'View Career Paths']
      });
    });
  };

  const handleNavigateToQuizzes = () => {
    addMessage({
      type: 'bot',
      content: `🎯 Perfect! I'll take you to our quiz section where you can practice cybersecurity concepts related to your recommended career paths.\n\nYou'll find quizzes on topics like:\n${recommendedPaths[0]?.relatedTopics.map(topic => `• ${topic}`).join('\n') || '• Network Security\n• Risk Management\n• Incident Response'}\n\nClick the link below to start practicing!`
    });

    // Add a clickable link
    setTimeout(() => {
      addMessage({
        type: 'bot',
        content: `🔗 **[Browse All Quizzes →](/quizzes)**\n\nGood luck with your learning journey! Feel free to come back anytime for more career guidance.`,
        options: ['Restart Assessment', 'Get More Career Advice']
      });
    }, 1000);
  };

  const handleNavigateToLearning = () => {
    addMessage({
      type: 'bot',
      content: `📚 Excellent choice! Our learning materials section has comprehensive resources to help you build the skills needed for your recommended career path.\n\nYou'll find materials covering:\n${recommendedPaths[0]?.learningPath.map(path => `• ${path}`).join('\n') || '• Security Fundamentals\n• Risk Assessment\n• Compliance Frameworks'}\n\nClick the link below to explore!`
    });

    setTimeout(() => {
      addMessage({
        type: 'bot',
        content: `🔗 **[Explore Learning Materials →](/learn)**\n\nHappy learning! Remember, consistent practice is key to success in cybersecurity.`,
        options: ['Browse Quizzes', 'Get Career Advice', 'Restart Assessment']
      });
    }, 1000);
  };

  const showCareerAdvice = () => {
    if (recommendedPaths.length === 0) {
      addMessage({
        type: 'bot',
        content: "Please complete the assessment first to get personalized career advice!",
        options: ['Start Assessment']
      });
      return;
    }

    simulateTyping(() => {
      const topPath = recommendedPaths[0];
      const advice = `💡 **Career Advice for ${topPath.title}**\n\n**Getting Started:**\n• Start with foundational knowledge in ${topPath.learningPath[0]}\n• Practice with hands-on labs and simulations\n• Join cybersecurity communities and forums\n\n**Building Experience:**\n• Look for entry-level positions or internships\n• Contribute to open-source security projects\n• Attend cybersecurity conferences and meetups\n\n**Certification Path:**\n• Begin with: ${topPath.certifications[0]}\n• Progress to: ${topPath.certifications[1] || 'Advanced certifications'}\n\n**Networking Tips:**\n• Connect with professionals on LinkedIn\n• Join local cybersecurity groups\n• Participate in Capture The Flag (CTF) competitions\n\n**Next Steps:**\nStart with our quizzes and learning materials to build your foundation!`;

      addMessage({
        type: 'bot',
        content: advice,
        options: ['Browse Quizzes', 'View Learning Materials', 'Get Detailed Career Info']
      });
    });
  };

  const showAllRecommendations = () => {
    if (recommendedPaths.length === 0) {
      addMessage({
        type: 'bot',
        content: "Please complete the assessment first!",
        options: ['Start Assessment']
      });
      return;
    }

    simulateTyping(() => {
      const allRecommendations = recommendedPaths.map((path, index) => {
        return `**${index + 1}. ${path.title}** (${path.matchPercentage}% match)\n${path.description}\n💰 ${path.averageSalary} | 📈 ${path.growthRate}`;
      }).join('\n\n');

      addMessage({
        type: 'bot',
        content: `📊 **All Your Career Recommendations:**\n\n${allRecommendations}\n\nWould you like detailed information about any specific path?`,
        options: ['Get Learning Roadmap', 'Browse Quizzes', 'View Learning Materials']
      });
    });
  };
};

export default CareerGuidanceChatbot;
