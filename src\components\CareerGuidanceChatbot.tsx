import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Progress } from "@/components/ui/progress";
import {
  Bo<PERSON>,
  User,
  Send,
  RotateCcw,
  GraduationCap,
  ArrowRight,
  ExternalLink,
  Sparkles,
  Zap,
  Star,
  MessageCircle
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { useAuth } from "@/hooks/use-auth";
import { careerPaths, type CareerPath } from "@/data/careerPaths";
import { Link } from "react-router-dom";

// Types
interface Message {
  id: string;
  type: 'bot' | 'user';
  content: string;
  timestamp: Date;
  options?: string[];
  isTyping?: boolean;
}

interface AssessmentScore {
  technical: number;
  business: number;
  handsOn: number;
  strategic: number;
  communication: number;
  leadership: number;
  analytical: number;
  creative: number;
}

// Assessment questions
const assessmentQuestions = [
  {
    question: "What type of work environment do you prefer?",
    options: [
      { text: "Structured corporate environment with clear policies", scores: { business: 3, strategic: 2, leadership: 1 } },
      { text: "Dynamic startup with hands-on technical challenges", scores: { technical: 3, handsOn: 3, creative: 2 } },
      { text: "Remote work with flexible schedule", scores: { analytical: 2, creative: 2, technical: 1 } },
      { text: "Team-based collaborative environment", scores: { communication: 3, leadership: 2, business: 1 } }
    ]
  },
  {
    question: "Which activity sounds most appealing to you?",
    options: [
      { text: "Writing detailed security policies and procedures", scores: { business: 3, strategic: 2, analytical: 2 } },
      { text: "Finding vulnerabilities in systems and applications", scores: { technical: 3, handsOn: 3, analytical: 2 } },
      { text: "Monitoring security alerts and investigating incidents", scores: { analytical: 3, technical: 2, handsOn: 1 } },
      { text: "Designing secure software architectures", scores: { technical: 3, strategic: 2, creative: 2 } }
    ]
  },
  {
    question: "How do you prefer to solve problems?",
    options: [
      { text: "Following established frameworks and methodologies", scores: { business: 3, strategic: 2, analytical: 1 } },
      { text: "Hands-on experimentation and testing", scores: { handsOn: 3, technical: 2, creative: 2 } },
      { text: "Research and data analysis", scores: { analytical: 3, technical: 1, strategic: 1 } },
      { text: "Collaborative brainstorming with team members", scores: { communication: 3, creative: 2, leadership: 1 } }
    ]
  },
  {
    question: "What motivates you most in your career?",
    options: [
      { text: "Ensuring organizational compliance and risk management", scores: { business: 3, strategic: 3, leadership: 1 } },
      { text: "Discovering new attack vectors and defense techniques", scores: { technical: 3, creative: 2, analytical: 2 } },
      { text: "Protecting organizations from real-time threats", scores: { analytical: 3, handsOn: 2, technical: 1 } },
      { text: "Building secure systems from the ground up", scores: { technical: 3, strategic: 2, creative: 2 } }
    ]
  },
  {
    question: "Which skill set do you want to develop further?",
    options: [
      { text: "Risk assessment and regulatory compliance", scores: { business: 3, analytical: 2, strategic: 2 } },
      { text: "Penetration testing and ethical hacking", scores: { technical: 3, handsOn: 3, creative: 1 } },
      { text: "Incident response and threat hunting", scores: { analytical: 3, technical: 2, handsOn: 2 } },
      { text: "Secure coding and architecture design", scores: { technical: 3, strategic: 2, creative: 2 } }
    ]
  },
  {
    question: "How do you prefer to communicate your findings?",
    options: [
      { text: "Formal reports to executives and stakeholders", scores: { business: 3, communication: 3, leadership: 2 } },
      { text: "Technical documentation for other security professionals", scores: { technical: 2, analytical: 2, communication: 2 } },
      { text: "Real-time alerts and incident summaries", scores: { analytical: 3, communication: 2, handsOn: 1 } },
      { text: "Code reviews and architectural recommendations", scores: { technical: 3, strategic: 2, communication: 1 } }
    ]
  },
  {
    question: "What type of challenges excite you most?",
    options: [
      { text: "Developing comprehensive security strategies", scores: { strategic: 3, business: 2, leadership: 2 } },
      { text: "Breaking into systems to find weaknesses", scores: { technical: 3, handsOn: 3, creative: 3 } },
      { text: "Analyzing patterns in security data", scores: { analytical: 3, technical: 1, strategic: 1 } },
      { text: "Architecting resilient security solutions", scores: { technical: 3, strategic: 3, creative: 2 } }
    ]
  },
  {
    question: "Which work style describes you best?",
    options: [
      { text: "Methodical and process-oriented", scores: { business: 3, analytical: 2, strategic: 1 } },
      { text: "Hands-on and experimental", scores: { handsOn: 3, technical: 2, creative: 2 } },
      { text: "Detail-oriented and investigative", scores: { analytical: 3, technical: 1, handsOn: 1 } },
      { text: "Collaborative and communicative", scores: { communication: 3, leadership: 2, business: 1 } }
    ]
  }
];

const CareerGuidanceChatbot = () => {
  const { user } = useAuth();
  const [messages, setMessages] = useState<Message[]>([]);
  const [currentInput, setCurrentInput] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [assessmentStarted, setAssessmentStarted] = useState(false);
  const [assessmentComplete, setAssessmentComplete] = useState(false);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [assessmentScores, setAssessmentScores] = useState<AssessmentScore>({
    technical: 0,
    business: 0,
    handsOn: 0,
    strategic: 0,
    communication: 0,
    leadership: 0,
    analytical: 0,
    creative: 0
  });
  const [recommendedPaths, setRecommendedPaths] = useState<CareerPath[]>([]);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Initial welcome message
    const welcomeMessage: Message = {
      id: '1',
      type: 'bot',
      content: `👋 Hello${user?.email ? ` ${user.email.split('@')[0]}` : ''}! I'm your AI Career Guidance Assistant.\n\nI'm here to help you discover the perfect cybersecurity career path based on your personality, interests, and skills.\n\n🎯 **What I can do:**\n• Conduct a personalized career assessment\n• Recommend suitable cybersecurity roles\n• Provide learning roadmaps and resources\n• Connect you with relevant quizzes and materials\n\nReady to find your ideal cybersecurity career?`,
      timestamp: new Date(),
      options: ['Start Assessment', 'Learn More', 'View Career Paths']
    };
    
    setMessages([welcomeMessage]);
  }, [user]);

  useEffect(() => {
    // Auto-scroll to bottom when new messages are added
    if (scrollAreaRef.current) {
      const scrollContainer = scrollAreaRef.current.querySelector('[data-radix-scroll-area-viewport]');
      if (scrollContainer) {
        scrollContainer.scrollTop = scrollContainer.scrollHeight;
      }
    }
  }, [messages]);

  const addMessage = (message: Omit<Message, 'id' | 'timestamp'>) => {
    const newMessage: Message = {
      ...message,
      id: Date.now().toString(),
      timestamp: new Date()
    };
    setMessages(prev => [...prev, newMessage]);
  };

  const simulateTyping = (callback: () => void, delay = 1000) => {
    setIsTyping(true);
    setTimeout(() => {
      setIsTyping(false);
      callback();
    }, delay);
  };

  const handleOptionClick = (option: string) => {
    // Add user message
    addMessage({
      type: 'user',
      content: option
    });

    // Handle different option types
    if (option === 'Start Assessment') {
      startAssessment();
    } else if (option === 'Learn More') {
      showLearnMore();
    } else if (option === 'View Career Paths') {
      showCareerPaths();
    } else if (option === 'Restart Assessment') {
      restartAssessment();
    } else if (option === 'View All Recommendations') {
      showAllRecommendations();
    } else if (option === 'Explore Learning Materials') {
      showLearningMaterials();
    } else if (option === 'Browse Quizzes') {
      handleNavigateToQuizzes();
    } else if (option === 'Get Career Advice') {
      showCareerAdvice();
    } else {
      // Handle assessment answers
      handleAssessmentAnswer(option);
    }
  };

  const handleSendMessage = () => {
    if (!currentInput.trim()) return;

    addMessage({
      type: 'user',
      content: currentInput
    });

    // Simple response for free-form messages
    simulateTyping(() => {
      addMessage({
        type: 'bot',
        content: "I understand you'd like to chat! For the best career guidance, I recommend taking the structured assessment. Would you like to start?",
        options: ['Start Assessment', 'View Career Paths']
      });
    });

    setCurrentInput("");
  };

  const startAssessment = () => {
    setAssessmentStarted(true);
    setCurrentQuestionIndex(0);
    setAssessmentScores({
      technical: 0,
      business: 0,
      handsOn: 0,
      strategic: 0,
      communication: 0,
      leadership: 0,
      analytical: 0,
      creative: 0
    });

    simulateTyping(() => {
      const firstQuestion = assessmentQuestions[0];
      addMessage({
        type: 'bot',
        content: `Great! Let's begin the assessment. This will take about 2-3 minutes.\n\n**Question 1 of ${assessmentQuestions.length}:**\n\n${firstQuestion.question}`,
        options: firstQuestion.options.map((opt: any) => opt.text)
      });
    });
  };

  const handleAssessmentAnswer = (answer: string) => {
    const currentQuestion = assessmentQuestions[currentQuestionIndex];
    const selectedOption = currentQuestion.options.find((opt: any) => opt.text === answer);

    if (selectedOption) {
      // Update scores
      const newScores = { ...assessmentScores };
      Object.entries(selectedOption.scores).forEach(([key, value]) => {
        newScores[key as keyof AssessmentScore] += value as number;
      });
      setAssessmentScores(newScores);

      // Move to next question or complete assessment
      if (currentQuestionIndex < assessmentQuestions.length - 1) {
        setCurrentQuestionIndex(prev => prev + 1);

        simulateTyping(() => {
          const nextQuestion = assessmentQuestions[currentQuestionIndex + 1];
          addMessage({
            type: 'bot',
            content: `**Question ${currentQuestionIndex + 2} of ${assessmentQuestions.length}:**\n\n${nextQuestion.question}`,
            options: nextQuestion.options.map((opt: any) => opt.text)
          });
        });
      } else {
        // Complete assessment
        completeAssessment(newScores);
      }
    }
  };

  const completeAssessment = (finalScores: AssessmentScore) => {
    setAssessmentComplete(true);

    // Calculate career path recommendations
    const pathScores = careerPaths.map(path => {
      let score = 0;
      let totalWeight = 0;

      Object.entries(path.requiredSkills).forEach(([skill, weight]) => {
        if (skill in finalScores) {
          score += finalScores[skill as keyof AssessmentScore] * weight;
          totalWeight += weight;
        }
      });

      const matchPercentage = totalWeight > 0 ? Math.round((score / (totalWeight * 3)) * 100) : 0;

      return {
        ...path,
        matchPercentage,
        score
      };
    });

    // Sort by match percentage and take top 3
    const topPaths = pathScores
      .sort((a, b) => b.matchPercentage - a.matchPercentage)
      .slice(0, 3);

    setRecommendedPaths(topPaths);

    simulateTyping(() => {
      const topPath = topPaths[0];
      addMessage({
        type: 'bot',
        content: `🎉 **Assessment Complete!**\n\nBased on your responses, here are your top cybersecurity career recommendations:\n\n**🥇 ${topPath.title}** (${topPath.matchPercentage}% match)\n${topPath.description}\n\n💰 **Average Salary:** ${topPath.averageSalary}\n📈 **Job Growth:** ${topPath.growthRate}\n\n**Key Skills Needed:**\n${topPath.keySkills.slice(0, 3).map(skill => `• ${skill}`).join('\n')}\n\nWould you like to see all recommendations or explore learning resources?`,
        options: ['View All Recommendations', 'Explore Learning Materials', 'Browse Quizzes', 'Get Career Advice']
      });
    }, 1500);
  };

  const showLearnMore = () => {
    simulateTyping(() => {
      addMessage({
        type: 'bot',
        content: `📚 **About Career Guidance**\n\nOur AI-powered career assessment analyzes your:\n• Work preferences and environment\n• Problem-solving approach\n• Communication style\n• Technical interests\n• Leadership potential\n\nBased on these factors, we recommend cybersecurity roles that align with your personality and skills.\n\n**Available Career Paths:**\n• GRC (Governance, Risk & Compliance) Specialist\n• Penetration Tester / Ethical Hacker\n• SOC (Security Operations Center) Analyst\n• Security Architect\n• And more!\n\nReady to discover your path?`,
        options: ['Start Assessment', 'View Career Paths']
      });
    });
  };

  const showCareerPaths = () => {
    simulateTyping(() => {
      const pathsList = careerPaths.map(path =>
        `**${path.title}**\n${path.description}\n💰 ${path.averageSalary} | 📈 ${path.growthRate}`
      ).join('\n\n');

      addMessage({
        type: 'bot',
        content: `🚀 **Available Cybersecurity Career Paths:**\n\n${pathsList}\n\nTake our assessment to find which path suits you best!`,
        options: ['Start Assessment', 'Learn More']
      });
    });
  };

  const restartAssessment = () => {
    setAssessmentStarted(false);
    setAssessmentComplete(false);
    setCurrentQuestionIndex(0);
    setRecommendedPaths([]);
    setAssessmentScores({
      technical: 0,
      business: 0,
      handsOn: 0,
      strategic: 0,
      communication: 0,
      leadership: 0,
      analytical: 0,
      creative: 0
    });

    simulateTyping(() => {
      addMessage({
        type: 'bot',
        content: "Assessment reset! Ready to start fresh and discover your ideal cybersecurity career path?",
        options: ['Start Assessment', 'View Career Paths']
      });
    });
  };

  const showAllRecommendations = () => {
    if (recommendedPaths.length === 0) {
      addMessage({
        type: 'bot',
        content: "Please complete the assessment first!",
        options: ['Start Assessment']
      });
      return;
    }

    simulateTyping(() => {
      const allRecommendations = recommendedPaths.map((path, index) => {
        return `**${index + 1}. ${path.title}** (${path.matchPercentage}% match)\n${path.description}\n💰 ${path.averageSalary} | 📈 ${path.growthRate}`;
      }).join('\n\n');

      addMessage({
        type: 'bot',
        content: `📊 **All Your Career Recommendations:**\n\n${allRecommendations}\n\nWould you like detailed information about any specific path?`,
        options: ['Get Career Advice', 'Browse Quizzes', 'Explore Learning Materials']
      });
    });
  };

  const showLearningMaterials = () => {
    simulateTyping(() => {
      addMessage({
        type: 'bot',
        content: `📚 **Learning Materials Available:**\n\nOur platform offers comprehensive learning resources including:\n• Interactive tutorials\n• Hands-on labs\n• Video courses\n• Practice exercises\n• Real-world scenarios\n\nThese materials are designed to help you build the skills needed for your recommended career path.`,
        options: ['Browse Quizzes', 'Get Career Advice', 'Restart Assessment']
      });
    });
  };

  const handleNavigateToQuizzes = () => {
    addMessage({
      type: 'bot',
      content: `🧠 **Practice Quizzes**\n\nOur quiz section contains hundreds of practice questions covering all major cybersecurity domains. Perfect for building knowledge and preparing for certifications!\n\nTopics include:\n• Network Security\n• Risk Management\n• Incident Response\n• Cryptography\n• And much more!`
    });

    setTimeout(() => {
      addMessage({
        type: 'bot',
        content: `🔗 **[Browse All Quizzes →](/quizzes)**\n\nGood luck with your learning journey! Feel free to come back anytime for more career guidance.`,
        options: ['Restart Assessment', 'Get Career Advice']
      });
    }, 1000);
  };

  const handleNavigateToLearning = () => {
    addMessage({
      type: 'bot',
      content: `📚 Excellent choice! Our learning materials section has comprehensive resources to help you build the skills needed for your recommended career path.\n\nYou'll find materials covering:\n${recommendedPaths[0]?.learningPath?.map(path => `• ${path}`).join('\n') || '• Security Fundamentals\n• Risk Assessment\n• Compliance Frameworks'}\n\nClick the link below to explore!`
    });

    setTimeout(() => {
      addMessage({
        type: 'bot',
        content: `🔗 **[Explore Learning Materials →](/learn)**\n\nHappy learning! Remember, consistent practice is key to success in cybersecurity.`,
        options: ['Browse Quizzes', 'Get Career Advice', 'Restart Assessment']
      });
    }, 1000);
  };

  const showCareerAdvice = () => {
    if (recommendedPaths.length === 0) {
      addMessage({
        type: 'bot',
        content: "Please complete the assessment first to get personalized career advice!",
        options: ['Start Assessment']
      });
      return;
    }

    simulateTyping(() => {
      const topPath = recommendedPaths[0];
      const advice = `💡 **Career Advice for ${topPath.title}**\n\n**Getting Started:**\n• Start with foundational knowledge in ${topPath.learningPath?.[0] || 'cybersecurity fundamentals'}\n• Practice with hands-on labs and simulations\n• Join cybersecurity communities and forums\n\n**Building Experience:**\n• Look for entry-level positions or internships\n• Contribute to open-source security projects\n• Attend cybersecurity conferences and meetups\n\n**Certification Path:**\n• Begin with: ${topPath.certifications?.[0] || 'Security+'}\n• Progress to: ${topPath.certifications?.[1] || 'Advanced certifications'}\n\n**Networking Tips:**\n• Connect with professionals on LinkedIn\n• Join local cybersecurity groups\n• Participate in Capture The Flag (CTF) competitions\n\n**Next Steps:**\nStart with our quizzes and learning materials to build your foundation!`;

      addMessage({
        type: 'bot',
        content: advice,
        options: ['Browse Quizzes', 'Explore Learning Materials', 'View All Recommendations']
      });
    });
  };

  return (
    <div className="flex flex-col h-full max-w-4xl mx-auto bg-gradient-to-br from-rose-50 via-orange-50 to-yellow-50 rounded-2xl shadow-2xl overflow-hidden relative">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-20 -right-20 w-40 h-40 bg-gradient-to-br from-pink-400/15 to-rose-400/15 rounded-full blur-2xl animate-pulse"></div>
        <div className="absolute -bottom-20 -left-20 w-40 h-40 bg-gradient-to-br from-cyan-400/15 to-teal-400/15 rounded-full blur-2xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-gradient-to-br from-violet-400/10 to-purple-400/10 rounded-full blur-2xl animate-pulse delay-500"></div>
        <div className="absolute top-20 left-20 w-24 h-24 bg-gradient-to-br from-emerald-400/12 to-green-400/12 rounded-full blur-xl animate-pulse delay-2000"></div>
        <div className="absolute bottom-32 right-32 w-28 h-28 bg-gradient-to-br from-amber-400/12 to-orange-400/12 rounded-full blur-xl animate-pulse delay-1500"></div>
      </div>
      
      {/* Chat Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="flex items-center gap-3 p-6 bg-gradient-to-r from-rose-500/15 via-orange-500/15 to-amber-500/15 backdrop-blur-sm border-b border-rose-200/50 relative z-10"
      >
        <motion.div
          whileHover={{ scale: 1.1, rotate: 5 }}
          transition={{ duration: 0.2 }}
        >
          <Avatar className="h-12 w-12">
            <AvatarFallback className="bg-gradient-to-br from-emerald-600 via-teal-600 to-cyan-600 text-white">
              <Bot className="h-6 w-6" />
            </AvatarFallback>
          </Avatar>
        </motion.div>
        <div>
          <h3 className="font-bold text-lg bg-gradient-to-r from-violet-600 via-purple-600 to-fuchsia-600 bg-clip-text text-transparent">
            Career Guidance Assistant
          </h3>
          <p className="text-sm text-slate-700 flex items-center gap-1">
            <Sparkles className="h-3 w-3 text-amber-500" />
            Discover your ideal cybersecurity career path
          </p>
        </div>
        <div className="ml-auto">
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.location.reload()}
              className="gap-2 border-rose-300 hover:border-orange-400 hover:bg-gradient-to-r hover:from-rose-50 hover:to-orange-50 transition-all duration-300"
            >
              <RotateCcw className="h-4 w-4 text-rose-600" />
              Reset
            </Button>
          </motion.div>
        </div>
      </motion.div>

      {/* Progress Bar (shown during assessment) */}
      {assessmentStarted && !assessmentComplete && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: "auto" }}
          exit={{ opacity: 0, height: 0 }}
          className="p-6 bg-gradient-to-r from-emerald-50 via-teal-50 to-cyan-50 border-b border-emerald-200/50 relative z-10"
        >
          <div className="flex items-center justify-between mb-3">
            <span className="text-sm font-semibold text-slate-700 flex items-center gap-2">
              <Star className="h-4 w-4 text-yellow-500" />
              Assessment Progress
            </span>
            <span className="text-sm text-slate-600 bg-gradient-to-r from-violet-100 to-purple-100 px-3 py-1 rounded-full border border-violet-200">
              {currentQuestionIndex + 1} of {assessmentQuestions.length}
            </span>
          </div>
          <Progress
            value={((currentQuestionIndex + 1) / assessmentQuestions.length) * 100}
            className="h-3 bg-gradient-to-r from-white/60 to-emerald-100/60"
          />
        </motion.div>
      )}

      {/* Chat Messages */}
      <ScrollArea className="flex-1 p-4 relative z-10" ref={scrollAreaRef}>
        <div className="space-y-4">
          <AnimatePresence>
            {messages.map((message) => (
              <motion.div
                key={message.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className={`flex gap-3 ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                {message.type === 'bot' && (
                  <Avatar className="h-8 w-8 mt-1">
                    <AvatarFallback className="bg-gradient-to-br from-emerald-600 via-teal-600 to-cyan-600 text-white text-xs">
                      <Bot className="h-4 w-4" />
                    </AvatarFallback>
                  </Avatar>
                )}

                <div className={`max-w-[80%] ${message.type === 'user' ? 'order-1' : ''}`}>
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    className={`p-4 rounded-2xl shadow-lg ${
                      message.type === 'user'
                        ? 'bg-gradient-to-br from-violet-500 via-purple-500 to-fuchsia-500 text-white ml-auto'
                        : 'bg-gradient-to-br from-white/90 to-rose-50/90 backdrop-blur-sm border border-rose-200/50'
                    }`}
                  >
                    <div className="whitespace-pre-wrap text-sm leading-relaxed">
                      {message.content}
                    </div>
                    
                    {message.options && (
                      <div className="flex flex-wrap gap-2 mt-4">
                        {message.options.map((option, index) => {
                          // Define different color schemes for buttons
                          const colorSchemes = [
                            'bg-gradient-to-r from-rose-50 to-pink-50 border-rose-300 hover:border-rose-400 hover:bg-gradient-to-r hover:from-rose-100 hover:to-pink-100 text-rose-700',
                            'bg-gradient-to-r from-emerald-50 to-teal-50 border-emerald-300 hover:border-emerald-400 hover:bg-gradient-to-r hover:from-emerald-100 hover:to-teal-100 text-emerald-700',
                            'bg-gradient-to-r from-violet-50 to-purple-50 border-violet-300 hover:border-violet-400 hover:bg-gradient-to-r hover:from-violet-100 hover:to-purple-100 text-violet-700',
                            'bg-gradient-to-r from-amber-50 to-orange-50 border-amber-300 hover:border-amber-400 hover:bg-gradient-to-r hover:from-amber-100 hover:to-orange-100 text-amber-700',
                            'bg-gradient-to-r from-cyan-50 to-blue-50 border-cyan-300 hover:border-cyan-400 hover:bg-gradient-to-r hover:from-cyan-100 hover:to-blue-100 text-cyan-700',
                            'bg-gradient-to-r from-lime-50 to-green-50 border-lime-300 hover:border-lime-400 hover:bg-gradient-to-r hover:from-lime-100 hover:to-green-100 text-lime-700'
                          ];
                          const colorScheme = colorSchemes[index % colorSchemes.length];

                          return (
                            <motion.div
                              key={index}
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                            >
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleOptionClick(option)}
                                className={`${colorScheme} transition-all duration-300 font-medium`}
                              >
                                {option}
                              </Button>
                            </motion.div>
                          );
                        })}
                      </div>
                    )}
                  </motion.div>
                </div>

                {message.type === 'user' && (
                  <Avatar className="h-8 w-8 mt-1">
                    <AvatarFallback className="bg-gradient-to-br from-orange-600 via-red-600 to-pink-600 text-white text-xs">
                      <User className="h-4 w-4" />
                    </AvatarFallback>
                  </Avatar>
                )}
              </motion.div>
            ))}
          </AnimatePresence>

          {isTyping && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="flex gap-3"
            >
              <Avatar className="h-8 w-8 mt-1">
                <AvatarFallback className="bg-gradient-to-br from-emerald-600 via-teal-600 to-cyan-600 text-white text-xs">
                  <Bot className="h-4 w-4" />
                </AvatarFallback>
              </Avatar>
              <div className="bg-gradient-to-br from-white/90 to-yellow-50/90 backdrop-blur-sm border border-yellow-200/50 p-4 rounded-2xl shadow-lg">
                <div className="flex space-x-1">
                  <motion.div
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 0.6, repeat: Infinity, delay: 0 }}
                    className="w-2 h-2 bg-gradient-to-r from-rose-500 to-pink-500 rounded-full"
                  />
                  <motion.div
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 0.6, repeat: Infinity, delay: 0.2 }}
                    className="w-2 h-2 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full"
                  />
                  <motion.div
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 0.6, repeat: Infinity, delay: 0.4 }}
                    className="w-2 h-2 bg-gradient-to-r from-violet-500 to-purple-500 rounded-full"
                  />
                </div>
              </div>
            </motion.div>
          )}
        </div>
      </ScrollArea>

      {/* Input Area */}
      {!assessmentStarted && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="p-6 bg-gradient-to-r from-indigo-50 via-purple-50 to-pink-50 border-t border-indigo-200/50 relative z-10"
        >
          <div className="flex gap-3">
            <Input
              value={currentInput}
              onChange={(e) => setCurrentInput(e.target.value)}
              placeholder="Type your message..."
              onKeyDown={(e) => e.key === 'Enter' && handleSendMessage()}
              className="flex-1 bg-gradient-to-r from-white/90 to-cyan-50/90 backdrop-blur-sm border-cyan-300 focus:border-emerald-400 focus:ring-emerald-400/20 placeholder:text-slate-500"
            />
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                onClick={handleSendMessage}
                disabled={!currentInput.trim()}
                className="bg-gradient-to-r from-emerald-500 via-teal-500 to-cyan-500 hover:from-emerald-600 hover:via-teal-600 hover:to-cyan-600 text-white shadow-lg"
              >
                <Send className="h-4 w-4" />
              </Button>
            </motion.div>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default CareerGuidanceChatbot;
