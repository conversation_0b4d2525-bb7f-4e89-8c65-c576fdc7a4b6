
import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Mail, Eye, EyeOff } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/hooks/use-auth";
import * as authService from "@/services/auth-service";
import { useNavigate, useSearchParams } from "react-router-dom";
import Navbar from "@/components/Navbar";
import BottomNavigation from "@/components/BottomNavigation";
import { motion } from "framer-motion";
import { sanitizeInput, isValidEmail, validatePasswordStrength } from "@/utils/security";

// Function to generate a random password
const generatePassword = () => {
  const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
  let password = "";
  for (let i = 0; i < 12; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return password;
}

const AuthPage = () => {
  const { toast } = useToast();
  const { signIn, signUp, user } = useAuth();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const defaultTab = searchParams.get("tab") === "register" ? "register" : "login";

  const [showPassword, setShowPassword] = useState(false);
  const [loginForm, setLoginForm] = useState({ email: "", password: "" });

  // Initialize with blank form values
  const [registerForm, setRegisterForm] = useState({
    name: "",
    email: "",
    password: "",
    confirmPassword: ""
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  // Check if there's an admin request in URL params
  const isAdminSignup = searchParams.get("admin") === "true";

  // If admin signup is requested, pre-fill the form with admin data
  useEffect(() => {
    if (isAdminSignup) {
      const adminPassword = generatePassword();

      setRegisterForm({
        name: "Admin User",
        email: "<EMAIL>",
        password: adminPassword,
        confirmPassword: adminPassword
      });

      // Auto-switch to register tab
      const registerTab = document.querySelector('[data-state="inactive"][value="register"]') as HTMLButtonElement;
      if (registerTab) registerTab.click();
    }
  }, [isAdminSignup]);

  // Redirect if already logged in
  if (user) {
    navigate("/");
    return null;
  }

  const togglePasswordVisibility = () => setShowPassword(!showPassword);

  const handleLoginSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Sanitize and validate inputs
      const sanitizedEmail = sanitizeInput(loginForm.email.trim());

      if (!isValidEmail(sanitizedEmail)) {
        toast({
          title: "Invalid Email",
          description: "Please enter a valid email address.",
          variant: "destructive",
        });
        setIsSubmitting(false);
        return;
      }

      const { error } = await authService.signIn(sanitizedEmail, loginForm.password);

      if (error) {
        // Check for email verification errors
        if (error.message.includes("Email not confirmed") ||
            error.message.includes("Invalid login credentials")) {
          toast({
            title: "Email not verified",
            description: "Please check your email and click the verification link before logging in. ⚠️ If you don't see the email, check your spam/junk folder.",
            variant: "destructive",
            duration: 8000,
          });
        } else {
          toast({
            title: "Login failed",
            description: error.message,
            variant: "destructive",
          });
        }
      } else {
        toast({
          title: "Login successful",
          description: "Welcome back to SecQuiz!",
        });
        navigate("/");
      }
    } catch (err) {
      toast({
        title: "Login failed",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRegisterSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Sanitize and validate inputs
      const sanitizedName = sanitizeInput(registerForm.name.trim());
      const sanitizedEmail = sanitizeInput(registerForm.email.trim());

      if (!sanitizedName) {
        toast({
          title: "Invalid Name",
          description: "Please enter your full name.",
          variant: "destructive",
        });
        setIsSubmitting(false);
        return;
      }

      if (!isValidEmail(sanitizedEmail)) {
        toast({
          title: "Invalid Email",
          description: "Please enter a valid email address.",
          variant: "destructive",
        });
        setIsSubmitting(false);
        return;
      }

      // Validate password strength
      const passwordValidation = validatePasswordStrength(registerForm.password);
      if (!passwordValidation.valid) {
        toast({
          title: "Weak Password",
          description: passwordValidation.message,
          variant: "destructive",
        });
        setIsSubmitting(false);
        return;
      }

      if (registerForm.password !== registerForm.confirmPassword) {
        toast({
          title: "Registration failed",
          description: "Passwords do not match.",
          variant: "destructive",
        });
        setIsSubmitting(false);
        return;
      }

      const { error } = await authService.signUp(sanitizedEmail, registerForm.password, {
        full_name: sanitizedName
      });

      if (error) {
        // Special handling for email confirmation errors
        if (error.message && error.message.includes("confirmation email")) {
          // Treat this as a success case since we're handling it in the auth hook
          handleSuccessfulRegistration();
        } else {
          toast({
            title: "Registration failed",
            description: error.message,
            variant: "destructive",
          });
        }
      } else {
        handleSuccessfulRegistration();
      }
    } catch (err) {
      toast({
        title: "Registration failed",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleLoginChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setLoginForm({ ...loginForm, [name]: value });
  };

  const handleRegisterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setRegisterForm({ ...registerForm, [name]: value });
  };

  // Helper function to handle successful registration
  const handleSuccessfulRegistration = () => {
    // Display admin credentials if this was an admin signup
    if (isAdminSignup) {
      toast({
        title: "Admin Registration successful",
        description: `Admin account created with email: ${registerForm.email} and password: ${registerForm.password}`,
        duration: 10000, // Show for 10 seconds so user can copy
      });
    } else {
      toast({
        title: "Registration successful",
        description: "Please check your email for a verification link. ⚠️ IMPORTANT: If you don't see the email within a few minutes, please check your spam/junk folder.",
        duration: 15000, // Show longer so user can read the message
      });
    }

    // Auto-switch to login tab after successful registration
    const loginTab = document.querySelector('[data-state="inactive"][value="login"]') as HTMLButtonElement;
    if (loginTab) loginTab.click();

    // For admin signup, automatically populate the login form
    if (isAdminSignup) {
      setLoginForm({
        email: registerForm.email,
        password: registerForm.password
      });
    } else {
      // For regular users, populate email but not password
      setLoginForm({
        ...loginForm,
        email: registerForm.email
      });
    }
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Navbar />

      <div className="flex-1 flex flex-col items-center justify-center p-4 cyber-grid-bg">
        <motion.div
          className="w-full max-w-md"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {isAdminSignup && (
            <div className="mb-4 p-3 bg-yellow-100 border border-yellow-300 text-yellow-800 rounded-md">
              <h3 className="font-bold">Admin Registration</h3>
              <p className="text-sm">Creating admin account with email: {registerForm.email}</p>
              <p className="text-sm">Password will be displayed after successful registration.</p>
            </div>
          )}

          <div className="text-center mb-6">
            <div className="inline-flex mb-4 p-3 rounded-full bg-cyber-primary/10">
              <img src="/secquiz-logo.svg" alt="SecQuiz Logo" className="h-12 w-12" />
            </div>
            <h1 className="text-2xl font-bold bg-gradient-to-r from-cyber-primary to-cyber-accent bg-clip-text text-transparent">
              SecQuiz
            </h1>
            <p className="text-sm text-muted-foreground mt-1">
              Your Cybersecurity Knowledge Partner
            </p>
          </div>

          <Card className="cyber-card p-6 shadow-lg">
            <Tabs defaultValue={defaultTab}>
              <TabsList className="grid grid-cols-2 mb-6">
                <TabsTrigger value="login">Login</TabsTrigger>
                <TabsTrigger value="register">Register</TabsTrigger>
              </TabsList>

              <TabsContent value="login">
                <form onSubmit={handleLoginSubmit} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="login-email">Email</Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="login-email"
                        name="email"
                        type="email"
                        placeholder="<EMAIL>"
                        className="pl-9"
                        value={loginForm.email}
                        onChange={handleLoginChange}
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <Label htmlFor="login-password">Password</Label>
                      <button
                        type="button"
                        className="text-xs text-cyber-primary hover:underline"
                        onClick={() => {
                          if (loginForm.email) {
                            authService.resetPassword(loginForm.email);
                            toast({
                              title: "Password reset email sent",
                              description: "Please check your email for a password reset link. ⚠️ If you don't see the email within a few minutes, check your spam/junk folder.",
                              duration: 8000,
                            });
                          } else {
                            toast({
                              title: "Email required",
                              description: "Please enter your email address first.",
                              variant: "destructive",
                            });
                          }
                        }}
                      >
                        Forgot Password?
                      </button>
                    </div>
                    <div className="relative">
                      <Input
                        id="login-password"
                        name="password"
                        type={showPassword ? "text" : "password"}
                        placeholder="••••••••"
                        value={loginForm.password}
                        onChange={handleLoginChange}
                        required
                      />
                      <button
                        type="button"
                        className="absolute right-3 top-2.5 text-muted-foreground"
                        onClick={togglePasswordVisibility}
                      >
                        {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </button>
                    </div>
                  </div>

                  <Button
                    type="submit"
                    className="w-full bg-cyber-primary hover:bg-cyber-primary/90"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? "Logging in..." : "Login"}
                  </Button>
                </form>
              </TabsContent>

              <TabsContent value="register">
                <form onSubmit={handleRegisterSubmit} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="register-name">Full Name</Label>
                    <Input
                      id="register-name"
                      name="name"
                      placeholder="John Doe"
                      value={registerForm.name}
                      onChange={handleRegisterChange}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="register-email">Email</Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="register-email"
                        name="email"
                        type="email"
                        placeholder="<EMAIL>"
                        className="pl-9"
                        value={registerForm.email}
                        onChange={handleRegisterChange}
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="register-password">Password</Label>
                    <div className="relative">
                      <Input
                        id="register-password"
                        name="password"
                        type={showPassword ? "text" : "password"}
                        placeholder="••••••••"
                        value={registerForm.password}
                        onChange={handleRegisterChange}
                        required
                      />
                      <button
                        type="button"
                        className="absolute right-3 top-2.5 text-muted-foreground"
                        onClick={togglePasswordVisibility}
                      >
                        {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="register-confirm-password">Confirm Password</Label>
                    <Input
                      id="register-confirm-password"
                      name="confirmPassword"
                      type={showPassword ? "text" : "password"}
                      placeholder="••••••••"
                      value={registerForm.confirmPassword}
                      onChange={handleRegisterChange}
                      required
                    />
                  </div>

                  <Button
                    type="submit"
                    className="w-full bg-cyber-primary hover:bg-cyber-primary/90"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? "Creating Account..." : "Create Account"}
                  </Button>
                </form>
              </TabsContent>
            </Tabs>
          </Card>

          <p className="text-center text-xs text-muted-foreground mt-4">
            By using SecQuiz, you agree to our Terms of Service and Privacy Policy.
          </p>
        </motion.div>
      </div>

      <BottomNavigation />
    </div>
  );
};

export default AuthPage;
