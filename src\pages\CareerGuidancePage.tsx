import { useState } from "react";
import Navbar from "@/components/Navbar";
import BottomNavigation from "@/components/BottomNavigation";
import CareerGuidanceChatbot from "@/components/CareerGuidanceChatbot";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Bot,
  Target,
  Users,
  TrendingUp,
  BookOpen,
  ArrowRight,
  Shield,
  Search,
  Eye,
  Code,
  Briefcase,
  GraduationCap,
  Sparkles,
  Zap,
  Star
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

const CareerGuidancePage = () => {
  const [showChatbot, setShowChatbot] = useState(false);

  const features = [
    {
      icon: Target,
      title: "Personalized Assessment",
      description: "5-minute personality assessment tailored for cybersecurity careers",
      gradient: "from-purple-500 to-pink-500",
      bgColor: "bg-gradient-to-br from-purple-50 to-pink-50",
      iconBg: "bg-gradient-to-br from-purple-500 to-pink-500"
    },
    {
      icon: Users,
      title: "Expert Guidance",
      description: "AI-powered recommendations based on industry insights",
      gradient: "from-blue-500 to-cyan-500",
      bgColor: "bg-gradient-to-br from-blue-50 to-cyan-50",
      iconBg: "bg-gradient-to-br from-blue-500 to-cyan-500"
    },
    {
      icon: TrendingUp,
      title: "Career Growth Data",
      description: "Real salary ranges and growth projections for each path",
      gradient: "from-green-500 to-emerald-500",
      bgColor: "bg-gradient-to-br from-green-50 to-emerald-50",
      iconBg: "bg-gradient-to-br from-green-500 to-emerald-500"
    },
    {
      icon: BookOpen,
      title: "Learning Roadmap",
      description: "Customized learning paths connected to our quiz materials",
      gradient: "from-orange-500 to-red-500",
      bgColor: "bg-gradient-to-br from-orange-50 to-red-50",
      iconBg: "bg-gradient-to-br from-orange-500 to-red-500"
    }
  ];

  const careerPaths = [
    {
      icon: Shield,
      title: "GRC Specialist",
      description: "Governance, Risk & Compliance",
      salary: "$85K - $140K",
      growth: "13%",
      gradient: "from-indigo-500 to-purple-600",
      bgGradient: "from-indigo-50 to-purple-50",
      hoverGradient: "from-indigo-600 to-purple-700"
    },
    {
      icon: Search,
      title: "Penetration Tester",
      description: "Ethical Hacking & Security Testing",
      salary: "$95K - $160K",
      growth: "18%",
      gradient: "from-red-500 to-pink-600",
      bgGradient: "from-red-50 to-pink-50",
      hoverGradient: "from-red-600 to-pink-700"
    },
    {
      icon: Eye,
      title: "SOC Analyst",
      description: "Security Operations Center",
      salary: "$55K - $95K",
      growth: "15%",
      gradient: "from-blue-500 to-cyan-600",
      bgGradient: "from-blue-50 to-cyan-50",
      hoverGradient: "from-blue-600 to-cyan-700"
    },
    {
      icon: Code,
      title: "Security Engineer",
      description: "Secure Systems & Applications",
      salary: "$110K - $180K",
      growth: "20%",
      gradient: "from-green-500 to-teal-600",
      bgGradient: "from-green-50 to-teal-50",
      hoverGradient: "from-green-600 to-teal-700"
    },
    {
      icon: Users,
      title: "Security Consultant",
      description: "Advisory & Risk Assessment",
      salary: "$100K - $200K",
      growth: "16%",
      gradient: "from-orange-500 to-amber-600",
      bgGradient: "from-orange-50 to-amber-50",
      hoverGradient: "from-orange-600 to-amber-700"
    },
    {
      icon: Briefcase,
      title: "Security Manager",
      description: "Leadership & Strategy",
      salary: "$130K - $250K",
      growth: "14%",
      gradient: "from-violet-500 to-fuchsia-600",
      bgGradient: "from-violet-50 to-fuchsia-50",
      hoverGradient: "from-violet-600 to-fuchsia-700"
    }
  ];

  if (showChatbot) {
    return (
      <div className="flex flex-col min-h-screen cyber-grid-bg">
        <Navbar />
        <div className="flex-1 container py-4">
          <div className="h-[calc(100vh-200px)]">
            <CareerGuidanceChatbot />
          </div>
        </div>
        <BottomNavigation />
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 pb-16 relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-purple-400/20 to-pink-400/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-cyan-400/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-green-400/10 to-emerald-400/10 rounded-full blur-3xl animate-pulse delay-2000"></div>
      </div>

      <Navbar />

      {/* Hero Section */}
      <section className="container py-16 px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
          className="text-center max-w-4xl mx-auto"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-flex items-center gap-2 bg-gradient-to-r from-purple-500/10 to-pink-500/10 backdrop-blur-sm border border-purple-200/50 text-purple-700 px-6 py-3 rounded-full text-sm font-medium mb-8 shadow-lg"
          >
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            >
              <Bot className="h-4 w-4" />
            </motion.div>
            AI-Powered Career Guidance
            <Sparkles className="h-4 w-4" />
          </motion.div>

          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent leading-tight"
          >
            Find Your Perfect
            <br />
            <motion.span
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="bg-gradient-to-r from-cyan-500 to-blue-600 bg-clip-text text-transparent"
            >
              Cybersecurity Career
            </motion.span>
          </motion.h1>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="text-xl text-slate-600 mb-8 max-w-2xl mx-auto leading-relaxed"
          >
            Take our AI-powered assessment to discover which cybersecurity career path
            aligns with your personality, skills, and interests. Get personalized recommendations
            and learning roadmaps.
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1 }}
            className="flex flex-col sm:flex-row gap-4 justify-center"
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                size="lg"
                className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white gap-2 shadow-lg hover:shadow-xl transition-all duration-300 px-8 py-6 text-lg"
                onClick={() => setShowChatbot(true)}
              >
                <Bot className="h-5 w-5" />
                Start Career Assessment
                <ArrowRight className="h-5 w-5" />
              </Button>
            </motion.div>
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                size="lg"
                variant="outline"
                className="gap-2 border-2 border-indigo-200 hover:border-indigo-300 hover:bg-indigo-50 transition-all duration-300 px-8 py-6 text-lg"
                onClick={() => {
                  const pathsSection = document.getElementById('career-paths');
                  pathsSection?.scrollIntoView({ behavior: 'smooth' });
                }}
              >
                <GraduationCap className="h-5 w-5" />
                Explore Career Paths
              </Button>
            </motion.div>
          </motion.div>
        </motion.div>
      </section>

      {/* Features Section */}
      <section className="container py-16 px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="text-center mb-12"
        >
          <motion.h2
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.3, duration: 0.6 }}
            className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent"
          >
            How It Works
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.6 }}
            className="text-slate-600 max-w-2xl mx-auto text-lg"
          >
            Our AI-powered career guidance system uses advanced personality assessment
            to match you with the perfect cybersecurity role.
          </motion.p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30, scale: 0.9 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              transition={{
                delay: 0.5 + index * 0.1,
                duration: 0.6,
                type: "spring",
                stiffness: 100
              }}
              whileHover={{
                y: -8,
                transition: { duration: 0.2 }
              }}
            >
              <Card className={`${feature.bgColor} border-0 p-6 text-center h-full shadow-lg hover:shadow-xl transition-all duration-300 group relative overflow-hidden`}>
                {/* Animated background gradient */}
                <div className={`absolute inset-0 bg-gradient-to-br ${feature.gradient} opacity-0 group-hover:opacity-5 transition-opacity duration-300`}></div>

                <motion.div
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  transition={{ duration: 0.2 }}
                  className={`h-16 w-16 rounded-2xl ${feature.iconBg} flex items-center justify-center mx-auto mb-6 shadow-lg relative z-10`}
                >
                  <feature.icon className="h-8 w-8 text-white" />
                </motion.div>

                <h3 className="font-bold mb-3 text-slate-800 text-lg relative z-10">{feature.title}</h3>
                <p className="text-slate-600 leading-relaxed relative z-10">{feature.description}</p>

                {/* Decorative elements */}
                <div className="absolute top-4 right-4 opacity-10 group-hover:opacity-20 transition-opacity duration-300">
                  <Sparkles className="h-6 w-6 text-current" />
                </div>
              </Card>
            </motion.div>
          ))}
        </div>
      </section>

      {/* Career Paths Section */}
      <section id="career-paths" className="container py-16 px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="text-center mb-12"
        >
          <motion.h2
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.5, duration: 0.6 }}
            className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-slate-800 to-slate-600 bg-clip-text text-transparent"
          >
            Cybersecurity Career Paths
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.6 }}
            className="text-slate-600 max-w-2xl mx-auto text-lg"
          >
            Discover the diverse opportunities in cybersecurity. Each path offers unique challenges,
            growth potential, and the chance to make a real impact in protecting digital assets.
          </motion.p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {careerPaths.map((path, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30, scale: 0.9 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              transition={{
                delay: 0.7 + index * 0.1,
                duration: 0.6,
                type: "spring",
                stiffness: 100
              }}
              whileHover={{
                y: -10,
                transition: { duration: 0.3 }
              }}
            >
              <Card className={`bg-gradient-to-br ${path.bgGradient} border-0 p-6 h-full shadow-lg hover:shadow-2xl transition-all duration-300 group relative overflow-hidden`}>
                {/* Animated background gradient on hover */}
                <div className={`absolute inset-0 bg-gradient-to-br ${path.hoverGradient} opacity-0 group-hover:opacity-10 transition-opacity duration-300`}></div>

                <div className="flex items-start gap-4 mb-6 relative z-10">
                  <motion.div
                    whileHover={{ scale: 1.1, rotate: 10 }}
                    transition={{ duration: 0.2 }}
                    className={`h-16 w-16 rounded-2xl bg-gradient-to-br ${path.gradient} flex items-center justify-center flex-shrink-0 shadow-lg`}
                  >
                    <path.icon className="h-8 w-8 text-white" />
                  </motion.div>
                  <div className="flex-1">
                    <h3 className="font-bold mb-2 text-slate-800 text-xl">{path.title}</h3>
                    <p className="text-slate-600 leading-relaxed">{path.description}</p>
                  </div>
                </div>

                <div className="space-y-3 mb-6 relative z-10">
                  <div className="flex justify-between items-center p-3 bg-white/50 rounded-lg backdrop-blur-sm">
                    <span className="text-sm font-medium text-slate-700">Salary Range</span>
                    <Badge className={`bg-gradient-to-r ${path.gradient} text-white border-0 font-semibold`}>
                      {path.salary}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-white/50 rounded-lg backdrop-blur-sm">
                    <span className="text-sm font-medium text-slate-700">Growth Rate</span>
                    <Badge className="bg-gradient-to-r from-green-500 to-emerald-500 text-white border-0 font-semibold">
                      {path.growth}
                    </Badge>
                  </div>
                </div>

                <motion.div
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="relative z-10"
                >
                  <Button
                    className={`w-full gap-2 bg-gradient-to-r ${path.gradient} hover:opacity-90 text-white border-0 shadow-lg font-semibold py-6`}
                    onClick={() => setShowChatbot(true)}
                  >
                    Learn More
                    <ArrowRight className="h-4 w-4" />
                  </Button>
                </motion.div>

                {/* Decorative elements */}
                <div className="absolute top-4 right-4 opacity-10 group-hover:opacity-20 transition-opacity duration-300">
                  <Star className="h-6 w-6 text-current" />
                </div>
                <div className="absolute bottom-4 left-4 opacity-5 group-hover:opacity-10 transition-opacity duration-300">
                  <Zap className="h-8 w-8 text-current" />
                </div>
              </Card>
            </motion.div>
          ))}
        </div>
      </section>

      {/* CTA Section */}
      <section className="container py-16 px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8, duration: 0.8 }}
          whileHover={{ scale: 1.02 }}
        >
          <Card className="border-0 p-12 text-center bg-gradient-to-br from-indigo-500/10 via-purple-500/10 to-pink-500/10 backdrop-blur-sm shadow-2xl relative overflow-hidden">
            {/* Animated background elements */}
            <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/5 to-purple-500/5 animate-pulse"></div>
            <div className="absolute -top-20 -right-20 w-40 h-40 bg-gradient-to-br from-pink-400/20 to-purple-400/20 rounded-full blur-2xl"></div>
            <div className="absolute -bottom-20 -left-20 w-40 h-40 bg-gradient-to-br from-blue-400/20 to-indigo-400/20 rounded-full blur-2xl"></div>

            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
              className="relative z-10"
            >
              <div className="h-20 w-20 bg-gradient-to-br from-purple-600 to-pink-600 rounded-2xl flex items-center justify-center mx-auto mb-8 shadow-lg">
                <Bot className="h-10 w-10 text-white" />
              </div>
            </motion.div>

            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1, duration: 0.6 }}
              className="text-3xl md:text-4xl font-bold mb-6 bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent relative z-10"
            >
              Ready to Find Your Path?
            </motion.h2>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.2, duration: 0.6 }}
              className="text-slate-600 mb-8 max-w-2xl mx-auto text-lg leading-relaxed relative z-10"
            >
              Take our comprehensive career assessment and get personalized recommendations
              tailored to your unique personality and interests. Start your cybersecurity journey today!
            </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.4, duration: 0.6 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="relative z-10"
            >
              <Button
                size="lg"
                className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white gap-3 shadow-lg hover:shadow-xl transition-all duration-300 px-10 py-6 text-lg font-semibold"
                onClick={() => setShowChatbot(true)}
              >
                <Sparkles className="h-5 w-5" />
                Start Your Assessment
                <ArrowRight className="h-5 w-5" />
              </Button>
            </motion.div>
          </Card>
        </motion.div>
      </section>

      <BottomNavigation />
    </div>
  );
};

export default CareerGuidancePage;
