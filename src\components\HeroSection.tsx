
import { ArrowRight } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useAuth } from "@/hooks/use-auth";
import { Link } from "react-router-dom";
import { motion } from "framer-motion";
import { getRemainingFreeQuestions, isUserSubscribed, FREE_QUESTIONS_LIMIT } from "@/utils/auth-helpers";
import QuizCard from "./QuizCard";
import { QuizTopic } from "@/data/mockQuizzes";

interface HeroSectionProps {
  featuredTopics?: QuizTopic[];
}

const HeroSection = ({ featuredTopics = [] }: HeroSectionProps) => {
  const { user } = useAuth();
  const remainingFreeQuestions = getRemainingFreeQuestions();

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5 }
    }
  };

  return (
    <div className="relative overflow-hidden bg-indigo-900 py-12 md:py-24">
      {/* Animated background elements - reduced opacity */}
      <div className="absolute inset-0 cyber-grid-bg opacity-10"></div>
      <div className="absolute top-20 left-10 w-32 h-32 bg-blue-400/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-20 right-10 w-40 h-40 bg-purple-400/10 rounded-full blur-3xl"></div>
      <div className="absolute top-40 right-30 w-24 h-24 bg-cyan-400/10 rounded-full blur-3xl"></div>

      <div className="relative container px-4 mx-auto">
        <motion.div
          className="max-w-4xl mx-auto text-center mb-12"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <motion.h1
            className="text-3xl md:text-5xl font-bold mb-6 text-white leading-tight"
            variants={itemVariants}
          >
            Learn, Practice, Master <span className="text-cyan-400">Cybersecurity</span>
          </motion.h1>

          <motion.p
            className="text-lg md:text-xl text-white/80 mb-8"
            variants={itemVariants}
          >
            Expert-crafted learning materials paired with 1,000+ interactive quizzes
            <span className="block mt-2 text-white font-medium">
              {!user ?
                "Build knowledge, test your skills, and become a cybersecurity expert!" :
                isUserSubscribed(user) ?
                  'Enjoy unlimited access to all premium content and quizzes!' :
                  "Access free learning materials and quizzes. Upgrade for premium content!"
              }
            </span>
            <span className="text-sm block mt-1 text-white/70">
              {!user ?
                "The complete cybersecurity education platform for your journey" :
                !isUserSubscribed(user) ?
                  "Premium content requires a subscription" :
                  "Your premium subscription gives you access to all learning materials and quizzes"
              }
            </span>
            {(!user || (user && !isUserSubscribed(user))) && remainingFreeQuestions < FREE_QUESTIONS_LIMIT && (
              <span className="text-sm block mt-1 text-white/70">
                {remainingFreeQuestions} free questions remaining
              </span>
            )}
          </motion.p>

          <motion.div
            className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-12"
            variants={itemVariants}
          >
            {!user ? (
              <>
                <Button asChild size="lg" className="bg-cyan-500 hover:bg-cyan-600 transition-all duration-300 shadow-lg text-white">
                  <Link to="/learn">
                    Start Learning <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
                </Button>
                <Button asChild size="lg" className="bg-green-500 hover:bg-green-600 text-white font-medium transition-all duration-300 shadow-lg">
                  <Link to="/auth?tab=register">
                    Create Account
                  </Link>
                </Button>
              </>
            ) : isUserSubscribed(user) ? (
              <div className="flex flex-col sm:flex-row gap-4">
                <Button asChild size="lg" className="bg-cyan-500 hover:bg-cyan-600 transition-all duration-300 shadow-lg text-white">
                  <Link to="/learn">
                    Browse Learning Materials <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
                </Button>
                <Button asChild size="lg" className="bg-purple-500 hover:bg-purple-600 transition-all duration-300 shadow-lg text-white">
                  <Link to="/quizzes">
                    Access Quizzes <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
                </Button>
              </div>
            ) : (
              <>
                <Button asChild size="lg" className="bg-cyan-500 hover:bg-cyan-600 transition-all duration-300 shadow-lg text-white">
                  <Link to="/learn">
                    Explore Free Content <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
                </Button>
                <Button asChild size="lg" className="bg-green-500 hover:bg-green-600 text-white font-medium transition-all duration-300 shadow-lg">
                  <Link to="/#pricing">
                    Upgrade to Pro
                  </Link>
                </Button>
              </>
            )}
          </motion.div>

          {featuredTopics.length > 0 && (
            <motion.div
              className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-5xl mx-auto"
              variants={containerVariants}
            >
              {featuredTopics.map((quiz) => (
                <QuizCard
                  key={quiz.id}
                  id={quiz.id}
                  title={quiz.title}
                  description={quiz.description}
                  questionCount={quiz.questionCount}
                  points={quiz.points}
                  difficulty={quiz.difficulty}
                  isPremium={quiz.isPremium}
                />
              ))}
            </motion.div>
          )}
        </motion.div>

        <motion.div
          className="mt-24 mb-12 max-w-5xl mx-auto"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <div className="bg-white rounded-xl shadow-xl overflow-hidden">
            <div className="flex flex-col md:flex-row">
              {/* Left side - Pro info */}
              <div className="p-8 md:p-10 md:w-1/2 flex flex-col justify-center">
                <div className="flex items-center mb-4">
                  <div className="bg-purple-100 p-2 rounded-lg mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-cyber-accent" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M12 2L15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2z"/>
                    </svg>
                  </div>
                  <h3 className="text-2xl font-bold bg-gradient-to-r from-cyber-accent to-cyber-primary bg-clip-text text-transparent">Pro</h3>
                </div>

                <h2 className="text-3xl font-bold text-gray-800 mb-6">Join thousands of professionals preparing for certifications</h2>


                <ul className="space-y-3 mb-8">
                  <li className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-cyber-success mr-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <polyline points="20 6 9 17 4 12"></polyline>
                    </svg>
                    <span className="text-gray-600">Community Access</span>
                  </li>
                  <li className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-cyber-success mr-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <polyline points="20 6 9 17 4 12"></polyline>
                    </svg>
                    <span className="text-gray-600">Referrals for Job Openings</span>
                  </li>
                  <li className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-cyber-success mr-3" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <polyline points="20 6 9 17 4 12"></polyline>
                    </svg>
                    <span className="text-gray-600">24/7 Priority Mentorship & Support</span>
                  </li>
                </ul>

                <Button asChild size="lg" className="bg-cyber-accent hover:bg-cyber-accent/90 text-white font-medium transition-all duration-300 shadow-lg w-full md:w-auto">
                  <Link to="/#pricing">
                    Start Free Trial
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <line x1="5" y1="12" x2="19" y2="12"></line>
                      <polyline points="12 5 19 12 12 19"></polyline>
                    </svg>
                  </Link>
                </Button>
              </div>

              {/* Right side - Image/Illustration */}
              <div className="bg-gradient-to-br from-indigo-500 via-purple-500 to-cyber-accent p-8 md:p-10 md:w-1/2 flex items-center justify-center relative overflow-hidden">
                <div className="absolute top-0 left-0 w-full h-full opacity-10">
                  <div className="cyber-grid-bg w-full h-full"></div>
                </div>

                <div className="relative z-10 text-white">
                  <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 shadow-lg">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-semibold">Quiz Performance</h4>
                      <span className="text-xs bg-white/20 px-2 py-1 rounded-full">Premium</span>
                    </div>

                    <div className="space-y-4">
                      <div className="h-16 flex items-end justify-between gap-2">
                        {[60, 45, 80, 30, 65, 75, 50].map((height, i) => (
                          <div key={i} className="w-full bg-white/20 rounded-t-sm" style={{ height: `${height}%` }}></div>
                        ))}
                      </div>

                      <div className="flex justify-between text-xs text-white/70">
                        <span>Mon</span>
                        <span>Tue</span>
                        <span>Wed</span>
                        <span>Thu</span>
                        <span>Fri</span>
                        <span>Sat</span>
                        <span>Sun</span>
                      </div>
                    </div>

                    <div className="mt-6 pt-4 border-t border-white/10 flex justify-between items-center">
                      <div>
                        <div className="text-xs text-white/70">Total Score</div>
                        <div className="text-xl font-bold">2,648</div>
                      </div>
                      <div className="flex items-center text-sm text-green-300">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <polyline points="22 7 13.5 15.5 8.5 10.5 2 17"></polyline>
                          <polyline points="16 7 22 7 22 13"></polyline>
                        </svg>
                        +8.5%
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

// FREE_QUESTIONS_LIMIT is now imported from auth-helpers

export default HeroSection;
