import { useState } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '@/hooks/use-auth';
import { usePaystackPayment } from 'react-paystack';
import {
  toKobo,
  generateReference,
  SubscriptionPlan
} from '@/utils/paystack';
import { toast } from 'sonner';

interface SecurePaystackButtonProps {
  plan: SubscriptionPlan;
  className?: string;
  buttonText?: string;
}

const SecurePaystackButton = ({ plan, className = '', buttonText = 'Select' }: SecurePaystackButtonProps) => {
  const { user } = useAuth();
  const [isProcessing, setIsProcessing] = useState(false);

  // Initialize payment configuration
  const config = {
    reference: generateReference(),
    email: user?.email || '',
    amount: toKobo(plan.amount),
    publicKey: import.meta.env.VITE_PAYSTACK_PUBLIC_KEY,
    currency: 'NGN',
    metadata: {
      custom_fields: [
        {
          display_name: 'Plan',
          variable_name: 'plan',
          value: plan.id
        }
      ]
    }
  };

  const onSuccess = (reference: any) => {
    console.log('Payment successful:', reference);
    toast.success(`Successfully subscribed to ${plan.name} plan`);

    // Redirect to success page
    window.location.href = `/payment/success?reference=${reference.reference}&plan=${plan.name}`;

    setIsProcessing(false);
  };

  const onClose = () => {
    console.log('Payment window closed');
    toast.error('Payment was cancelled');
    setIsProcessing(false);
  };

  const initializePayment = usePaystackPayment(config);

  const handlePayment = () => {
    if (!user) {
      // Redirect to login if user is not authenticated
      toast.error('Please log in to subscribe to a plan');
      return;
    }

    setIsProcessing(true);

    try {
      // @ts-ignore - The type definitions for react-paystack are not complete
      initializePayment(onSuccess, onClose);
    } catch (error) {
      console.error('Payment initialization error:', error);
      toast.error('Failed to initialize payment. Please try again later.');
      setIsProcessing(false);
    }
  };

  return (
    <motion.button
      className={`w-full py-3 px-6 rounded-full font-medium transition-colors duration-200 uppercase text-sm tracking-wider ${className}`}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      onClick={handlePayment}
      disabled={isProcessing || !user}
    >
      {isProcessing ? 'Processing...' : buttonText}
    </motion.button>
  );
};

export default SecurePaystackButton;
