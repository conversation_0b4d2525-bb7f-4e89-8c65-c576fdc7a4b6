import { motion } from "framer-motion";
import { Check } from "lucide-react";
import SecurePaystackButton from "./SecurePaystackButton";
import { subscriptionPlans } from "@/utils/paystack";
import { useAuth } from "@/hooks/use-auth";
import { Link } from "react-router-dom";
import { useEffect, useRef } from "react";
import { useLocation } from "react-router-dom";

const PricingSection = () => {
  const { user } = useAuth();
  const location = useLocation();
  const sectionRef = useRef<HTMLElement>(null);

  // Handle direct scrolling to this section when the component mounts
  useEffect(() => {
    if (location.hash === '#pricing' && sectionRef.current) {
      setTimeout(() => {
        sectionRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 100);
    }
  }, [location.hash]);

  return (
    <section
      id="pricing"
      ref={sectionRef}
      className="relative py-20 overflow-hidden">

      <div className="container relative z-10 mx-auto px-4 bg-transparent">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-white">Choose Your Plan</h2>
          <p className="text-lg text-white max-w-2xl mx-auto">
            Select the perfect plan to accelerate your cybersecurity learning journey
          </p>
        </div>

        <div className="flex flex-col lg:flex-row gap-6 justify-center items-center lg:items-stretch max-w-5xl mx-auto">
          {/* Basic Plan */}
          <motion.div
            className="w-full max-w-sm bg-white rounded-3xl shadow-xl overflow-hidden relative"
            initial={{ opacity: 0, y: 50, scale: 0.9 }}
            whileInView={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.1, type: "spring", stiffness: 100 }}
            viewport={{ once: true }}
            whileHover={{ y: -10, boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)" }}
          >
            <div className="p-8 pb-20">
              <div className="text-center mb-8">
                <span className="text-blue-500 font-medium uppercase text-4xl tracking-wider">Basic</span>
                <div className="relative mt-6">
                  <motion.div
                    className="w-24 h-24 rounded-full bg-blue-100/50 mx-auto flex items-center justify-center"
                    whileHover={{ scale: 1.05, backgroundColor: "rgba(219, 234, 254, 0.8)" }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <div className="flex items-baseline justify-center">
                      <span className="text-6xl font-bold text-amber-600">₦998</span>
                    </div>
                  </motion.div>
                  <div className="mt-2 text-gray-500 font-medium uppercase text-sm">ONE WEEK PLAN</div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center">
                  <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                  <span className="text-gray-600">Access to all quiz domains</span>
                </div>
                <div className="flex items-center">
                  <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                  <span className="text-gray-600">1000+ questions weekly</span>
                </div>
              </div>
            </div>

            <div className="absolute bottom-0 left-0 right-0 p-4">
              {user ? (
                <SecurePaystackButton
                  plan={subscriptionPlans.basic}
                  className="bg-blue-300 hover:bg-blue-400 text-white"
                  buttonText="Subscribe"
                />
              ) : (
                <Link to="/auth?tab=login" className="block">
                  <motion.button
                    className="w-full py-3 px-6 rounded-full bg-blue-300 hover:bg-blue-400 text-white font-medium transition-colors duration-200 uppercase text-sm tracking-wider"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Login to Subscribe
                  </motion.button>
                </Link>
              )}
            </div>
          </motion.div>

          {/* Pro Plan (Featured) */}
          <motion.div
            className="w-full max-w-sm bg-white rounded-3xl shadow-xl overflow-hidden relative lg:scale-110 z-10"
            initial={{ opacity: 0, y: 50, scale: 0.9 }}
            whileInView={{ opacity: 1, y: 0, scale: 1.1 }}
            transition={{ duration: 0.6, type: "spring", stiffness: 100 }}
            viewport={{ once: true }}
            whileHover={{ y: -10, boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)" }}
          >
            <div className="bg-gradient-to-r from-indigo-500 to-cyan-400 py-2 text-center">
              <span className="text-white font-medium">MOST POPULAR</span>
            </div>
            <div className="p-8 pb-20">
              <div className="text-center mb-8">
                <span className="text-green-500 font-medium uppercase text-4xl tracking-wider">Pro</span>
                <div className="relative mt-6">
                  <motion.div
                    className="w-24 h-24 rounded-full bg-green-100/50 mx-auto flex items-center justify-center"
                    whileHover={{ scale: 1.05, backgroundColor: "rgba(220, 252, 231, 0.8)" }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <div className="flex items-baseline justify-center">
                      <span className="text-6xl font-bold text-green-600">₦1,979</span>
                    </div>
                  </motion.div>
                  <div className="mt-2 text-gray-500 font-medium uppercase text-sm">PER WEEK</div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center">
                  <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                  <span className="text-gray-600">Access to all quiz domains</span>
                </div>
                <div className="flex items-center">
                  <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                  <span className="text-gray-600">Unlimited questions</span>
                </div>

                <div className="flex items-center">
                  <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                  <span className="text-gray-600">Free international Certification</span>
                </div>
              </div>
            </div>

            <div className="absolute bottom-0 left-0 right-0 p-4">
              {user ? (
                <SecurePaystackButton
                  plan={subscriptionPlans.pro}
                  className="bg-gradient-to-r from-indigo-500 to-cyan-400 hover:from-indigo-600 hover:to-cyan-500 text-white shadow-md"
                  buttonText="Subscribe"
                />
              ) : (
                <Link to="/auth?tab=login" className="block">
                  <motion.button
                    className="w-full py-3 px-6 rounded-full bg-gradient-to-r from-indigo-500 to-cyan-400 hover:from-indigo-600 hover:to-cyan-500 text-white font-medium transition-colors duration-200 uppercase text-sm tracking-wider shadow-md"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Login to Subscribe
                  </motion.button>
                </Link>
              )}
            </div>
          </motion.div>

          {/* Elite Plan */}
          <motion.div
            className="w-full max-w-sm bg-white rounded-3xl shadow-xl overflow-hidden relative"
            initial={{ opacity: 0, y: 50, scale: 0.9 }}
            whileInView={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.2, type: "spring", stiffness: 100 }}
            viewport={{ once: true }}
            whileHover={{ y: -10, boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)" }}
          >
            <div className="p-8 pb-20">
              <div className="text-center mb-8">
                <span className="text-pink-500 font-medium uppercase text-4xl tracking-wider">Elite</span>
                <div className="relative mt-6">
                  <motion.div
                    className="w-24 h-24 rounded-full bg-pink-100/50 mx-auto flex items-center justify-center"
                    whileHover={{ scale: 1.05, backgroundColor: "rgba(252, 231, 243, 0.8)" }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <div className="flex items-baseline justify-center">
                      <span className="text-6xl font-bold text-blue-900">₦5,000</span>
                    </div>
                  </motion.div>
                  <div className="mt-2 text-gray-500 font-medium uppercase text-sm">PER MONTH </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center">
                  <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                  <span className="text-gray-600">Everything in Pro</span>
                </div>
                <div className="flex items-center">
                  <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                  <span className="text-gray-600">Community Access</span>
                </div>
                <div className="flex items-center">
                  <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                  <span className="text-gray-600">Free international Certification</span>
                </div>

                <div className="flex items-center">
                  <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                  <span className="text-gray-600">24/7 Priority Mentorship & Support</span>
                </div>

                <div className="flex items-center">
                  <Check className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
                  <span className="text-gray-600">Daily Cybersecurity Job Alert</span>
                </div>

              </div>
            </div>

            <div className="absolute bottom-0 left-0 right-0 p-4">
              {user ? (
                <SecurePaystackButton
                  plan={subscriptionPlans.elite}
                  className="bg-pink-400 hover:bg-pink-500 text-white"
                  buttonText="Purchase"
                />
              ) : (
                <Link to="/auth?tab=login" className="block">
                  <motion.button
                    className="w-full py-3 px-6 rounded-full bg-pink-400 hover:bg-pink-500 text-white font-medium transition-colors duration-200 uppercase text-sm tracking-wider"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Login to Purchase
                  </motion.button>
                </Link>
              )}
            </div>
          </motion.div>
        </div>

        <div className="text-center mt-12">
          <p className="text-white text-sm">
          Don't know where to start?
            <br />
            Need help choosing? <a href="/contact" className="text-indigo-200 font-medium hover:underline">Contact us</a> for assistance.
          </p>
        </div>
      </div>
    </section>
  );
};

export default PricingSection;
