import * as React from "react";
import * as TabsPrimitive from "@radix-ui/react-tabs";
import { cn } from "@/lib/utils";

const QuizFilterTabs = TabsPrimitive.Root;

const QuizFilterTabsList = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.List>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.List
    ref={ref}
    className={cn(
      "inline-flex h-12 items-center justify-center rounded-lg bg-indigo-950/50 backdrop-blur-sm p-1 w-full border border-indigo-800/30",
      className
    )}
    {...props}
  />
));
QuizFilterTabsList.displayName = "QuizFilterTabsList";

const QuizFilterTabsTrigger = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger> & {
    variant?: "all" | "free" | "premium";
  }
>(({ className, variant = "all", ...props }, ref) => {
  // Define background colors for active state based on variant
  const activeBackgrounds = {
    all: "data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=active]:border-indigo-400",
    free: "data-[state=active]:bg-green-600 data-[state=active]:text-white data-[state=active]:border-green-400",
    premium: "data-[state=active]:bg-purple-600 data-[state=active]:text-white data-[state=active]:border-purple-400",
  };

  // Define text shadow style for better readability
  const textShadow = "[text-shadow:0_1px_1px_rgba(0,0,0,0.3)]";

  return (
    <TabsPrimitive.Trigger
      ref={ref}
      className={cn(
        "inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-2 text-sm font-medium transition-colors",
        "text-white bg-indigo-900/60 border border-white/10",
        "data-[state=active]:font-semibold data-[state=active]:shadow-sm",
        textShadow,
        activeBackgrounds[variant],
        className
      )}
      {...props}
    />
  );
});
QuizFilterTabsTrigger.displayName = "QuizFilterTabsTrigger";

const QuizFilterTabsContent = React.forwardRef<
  React.ElementRef<typeof TabsPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>
>(({ className, ...props }, ref) => (
  <TabsPrimitive.Content
    ref={ref}
    className={cn(
      "mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
      className
    )}
    {...props}
  />
));
QuizFilterTabsContent.displayName = "QuizFilterTabsContent";

export { QuizFilterTabs, QuizFilterTabsList, QuizFilterTabsTrigger, QuizFilterTabsContent };
