/**
 * Content Security Policy middleware
 * This middleware adds CSP headers to enhance security
 */

export const cspMiddleware = (req, res, next) => {
  // Define CSP directives
  const cspDirectives = [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'", // Allow inline scripts for now
    "connect-src 'self' https://*.supabase.co https://api.paystack.co",
    "style-src 'self' 'unsafe-inline'",
    "img-src 'self' data: https://*.supabase.co",
    "font-src 'self' data:",
    "frame-src 'self' https://checkout.paystack.com",
    "object-src 'none'",
    "base-uri 'self'"
  ].join('; ');

  // Set CSP header
  res.setHeader('Content-Security-Policy', cspDirectives);
  
  // Set other security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  next();
};

export default cspMiddleware;
