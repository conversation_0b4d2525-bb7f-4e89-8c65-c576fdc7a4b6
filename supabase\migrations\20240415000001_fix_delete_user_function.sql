-- Drop the existing function if it exists
DROP FUNCTION IF EXISTS public.delete_user(uuid);

-- Create a new version of the function with dynamic column checks
CREATE OR REPLACE FUNCTION public.delete_user(user_id_param uuid)
RETURNS void
SECURITY DEFINER
AS $$
DECLARE
  column_exists boolean;
BEGIN
  -- Check if admin_users table exists
  IF EXISTS (
    SELECT FROM pg_tables
    WHERE schemaname = 'public' AND tablename = 'admin_users'
  ) THEN
    -- Check if user_id column exists in admin_users
    SELECT EXISTS (
      SELECT FROM information_schema.columns
      WHERE table_schema = 'public' AND table_name = 'admin_users' AND column_name = 'user_id'
    ) INTO column_exists;

    IF column_exists THEN
      DELETE FROM public.admin_users WHERE user_id = user_id_param;
    END IF;
  END IF;

  -- Check if user_profiles table exists
  IF EXISTS (
    SELECT FROM pg_tables
    WHERE schemaname = 'public' AND tablename = 'user_profiles'
  ) THEN
    -- Check if user_id column exists in user_profiles
    SELECT EXISTS (
      SELECT FROM information_schema.columns
      WHERE table_schema = 'public' AND table_name = 'user_profiles' AND column_name = 'user_id'
    ) INTO column_exists;

    IF column_exists THEN
      DELETE FROM public.user_profiles WHERE user_id = user_id_param;
    END IF;
  END IF;

  -- Check if subscriptions table exists
  IF EXISTS (
    SELECT FROM pg_tables
    WHERE schemaname = 'public' AND tablename = 'subscriptions'
  ) THEN
    -- Check if user_id column exists in subscriptions
    SELECT EXISTS (
      SELECT FROM information_schema.columns
      WHERE table_schema = 'public' AND table_name = 'subscriptions' AND column_name = 'user_id'
    ) INTO column_exists;

    IF column_exists THEN
      DELETE FROM public.subscriptions WHERE user_id = user_id_param;
    END IF;
  END IF;

  -- Check if payment_transactions table exists
  IF EXISTS (
    SELECT FROM pg_tables
    WHERE schemaname = 'public' AND tablename = 'payment_transactions'
  ) THEN
    -- Check if user_id column exists in payment_transactions
    SELECT EXISTS (
      SELECT FROM information_schema.columns
      WHERE table_schema = 'public' AND table_name = 'payment_transactions' AND column_name = 'user_id'
    ) INTO column_exists;

    IF column_exists THEN
      DELETE FROM public.payment_transactions WHERE user_id = user_id_param;
    END IF;
  END IF;

  -- Delete user from auth.users (requires admin privileges)
  DELETE FROM auth.users WHERE id = user_id_param;
END;
$$ LANGUAGE plpgsql;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.delete_user(uuid) TO authenticated;
