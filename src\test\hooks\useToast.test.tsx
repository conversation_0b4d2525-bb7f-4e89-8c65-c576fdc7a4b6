import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useToast, toast, reducer, ToastActionElement, ToastProps } from '@/hooks/use-toast';
import React from 'react';

// Mock the useToast hook before importing it
vi.mock('@/hooks/use-toast', async () => {
  const actual = await vi.importActual('@/hooks/use-toast');
  return {
    ...actual,
    useToast: () => ({
      toast: vi.fn().mockImplementation((props) => {
        const id = String(Date.now());
        return {
          id,
          update: vi.fn(),
        };
      }),
      dismiss: vi.fn(),
      toasts: [],
    }),
  };
});

// Create a simple wrapper component for the tests
const createWrapper = () => {
  return { wrapper: ({ children }: { children: React.ReactNode }) => <>{children}</> };
};

describe('useToast hook', () => {
  it('initializes with empty toasts array', () => {
    const { wrapper } = createWrapper();
    const { result } = renderHook(() => useToast(), { wrapper });

    expect(result.current.toasts).toEqual([]);
  });

  it('adds a toast when toast() is called', () => {
    const { wrapper } = createWrapper();
    const { result } = renderHook(() => useToast(), { wrapper });

    act(() => {
      result.current.toast({
        title: 'Test Toast',
        description: 'This is a test',
      });
    });

    // Since we're using mocks, we can verify the toast function was called
    expect(result.current.toast).toHaveBeenCalledWith({
      title: 'Test Toast',
      description: 'This is a test',
    });
  });

  it('dismisses a toast when dismiss() is called with id', () => {
    const { wrapper } = createWrapper();
    const { result } = renderHook(() => useToast(), { wrapper });

    const toastId = 'test-id';

    act(() => {
      result.current.dismiss(toastId);
    });

    // Verify dismiss was called with the correct ID
    expect(result.current.dismiss).toHaveBeenCalledWith(toastId);
  });

  it('dismisses all toasts when dismiss() is called without id', () => {
    const { wrapper } = createWrapper();
    const { result } = renderHook(() => useToast(), { wrapper });

    act(() => {
      result.current.dismiss();
    });

    // Verify dismiss was called with no arguments
    expect(result.current.dismiss).toHaveBeenCalledWith();
  });

  describe('reducer', () => {
    it('handles ADD_TOAST action', () => {
      const initialState = { toasts: [] };
      const newToast = { id: '1', title: 'Test', open: true };

      const newState = reducer(initialState, {
        type: 'ADD_TOAST',
        toast: newToast,
      });

      expect(newState.toasts).toEqual([newToast]);
    });

    it('handles UPDATE_TOAST action', () => {
      const initialState = {
        toasts: [
          { id: '1', title: 'Original', open: true },
        ],
      };

      const newState = reducer(initialState, {
        type: 'UPDATE_TOAST',
        toast: { id: '1', title: 'Updated' },
      });

      expect(newState.toasts[0].title).toBe('Updated');
      expect(newState.toasts[0].open).toBe(true); // Other properties preserved
    });

    it('handles DISMISS_TOAST action for specific toast', () => {
      const initialState = {
        toasts: [
          { id: '1', title: 'Toast 1', open: true },
          { id: '2', title: 'Toast 2', open: true },
        ],
      };

      const newState = reducer(initialState, {
        type: 'DISMISS_TOAST',
        toastId: '1',
      });

      expect(newState.toasts[0].open).toBe(false);
      expect(newState.toasts[1].open).toBe(true);
    });

    it('handles DISMISS_TOAST action for all toasts', () => {
      const initialState = {
        toasts: [
          { id: '1', title: 'Toast 1', open: true },
          { id: '2', title: 'Toast 2', open: true },
        ],
      };

      const newState = reducer(initialState, {
        type: 'DISMISS_TOAST',
      });

      expect(newState.toasts.every(t => !t.open)).toBe(true);
    });

    it('handles REMOVE_TOAST action for specific toast', () => {
      const initialState = {
        toasts: [
          { id: '1', title: 'Toast 1', open: true },
          { id: '2', title: 'Toast 2', open: true },
        ],
      };

      const newState = reducer(initialState, {
        type: 'REMOVE_TOAST',
        toastId: '1',
      });

      expect(newState.toasts.length).toBe(1);
      expect(newState.toasts[0].id).toBe('2');
    });

    it('handles REMOVE_TOAST action for all toasts', () => {
      const initialState = {
        toasts: [
          { id: '1', title: 'Toast 1', open: true },
          { id: '2', title: 'Toast 2', open: true },
        ],
      };

      const newState = reducer(initialState, {
        type: 'REMOVE_TOAST',
      });

      expect(newState.toasts.length).toBe(0);
    });
  });
});
