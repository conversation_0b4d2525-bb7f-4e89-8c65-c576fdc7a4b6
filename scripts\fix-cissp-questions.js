import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { createInterface } from 'readline';

dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Topic ID for CISSP Fundamentals
const CISSP_TOPIC_ID = '1de31e9d-97e5-4ee7-9e89-968615011645';

// Manual corrections for specific questions
// Format: questionText (partial match) -> correct answer key
const MANUAL_CORRECTIONS = [
  // Add your corrections here
  {
    questionPartialText: "Which of the following is NOT one of the states of data",
    correctAnswer: "3", // Data in disposal
    reason: "The explanation states the three states are: data at rest, data in transit, and data in process/use. Data in disposal is NOT one of them."
  },
  {
    questionPartialText: "What is the primary purpose of a firewall",
    correctAnswer: "1", // Control network traffic flow
    reason: "The explanation states that firewalls control network traffic flow based on predetermined security rules."
  },
  {
    questionPartialText: "Which risk management strategy involves transferring risk",
    correctAnswer: "3", // Risk transference
    reason: "The explanation states that risk transference involves shifting the risk to another entity."
  },
  {
    questionPartialText: "What is defense in depth",
    correctAnswer: "1", // Multiple layers of security controls
    reason: "The explanation states that defense in depth implements multiple layers of security controls throughout the system."
  },
  {
    questionPartialText: "What is the purpose of non-repudiation",
    correctAnswer: "2", // Prevent denial of an action
    reason: "The explanation states that non-repudiation ensures that a party cannot deny the authenticity of their signature or sending a message."
  },
  {
    questionPartialText: "Which access control model is based on the sensitivity of information",
    correctAnswer: "1", // Mandatory access control
    reason: "The explanation states that Mandatory access control (MAC) assigns sensitivity labels to resources and clearance levels to users."
  },
  {
    questionPartialText: "What is the purpose of separation of duties",
    correctAnswer: "1", // Reduce the potential for unauthorized actions
    reason: "The explanation states that separation of duties ensures that no single individual has control over all parts of a critical process, reducing risk of fraud or error."
  }
];

async function getQuestionsForTopic(topicId) {
  const { data, error } = await supabase
    .from('questions')
    .select('*')
    .eq('topic_id', topicId);

  if (error) {
    console.error('Error fetching questions:', error);
    return [];
  }

  return data;
}

async function updateQuestionCorrectAnswer(questionId, correctAnswer) {
  const { data, error } = await supabase
    .from('questions')
    .update({ correct_answer: correctAnswer })
    .eq('id', questionId)
    .select();

  if (error) {
    console.error(`Error updating question ${questionId}:`, error);
    return false;
  }

  return true;
}

async function main() {
  try {
    console.log('Fetching CISSP Fundamentals questions...');
    const questions = await getQuestionsForTopic(CISSP_TOPIC_ID);
    console.log(`Found ${questions.length} questions.`);

    const questionsToFix = [];

    // Find questions that need fixing based on manual corrections
    for (const question of questions) {
      for (const correction of MANUAL_CORRECTIONS) {
        if (question.question_text.includes(correction.questionPartialText)) {
          questionsToFix.push({
            question,
            newAnswer: correction.correctAnswer,
            reason: correction.reason
          });
          break;
        }
      }
    }

    console.log(`\n${questionsToFix.length} questions need fixing.`);

    // Display all questions that will be fixed
    for (const item of questionsToFix) {
      const { question, newAnswer, reason } = item;

      console.log('\n' + '-'.repeat(80));
      console.log(`QUESTION: ${question.question_text}`);

      // Display all options
      console.log('\nOPTIONS:');
      const options = question.options;
      let optionKeys = [];

      if (typeof options === 'object') {
        // Handle both numeric and letter keys
        if ('A' in options || 'B' in options) {
          // Letter keys (A, B, C, D)
          optionKeys = Object.keys(options).filter(key => ['A', 'B', 'C', 'D'].includes(key));
        } else {
          // Numeric keys (0, 1, 2, 3)
          optionKeys = Object.keys(options).filter(key => ['0', '1', '2', '3'].includes(key));
        }
      }

      for (const key of optionKeys) {
        const isCurrentAnswer = key === question.correct_answer;
        const isNewAnswer = key === newAnswer;
        let marker = '';
        if (isCurrentAnswer) marker = '(Current Answer)';
        if (isNewAnswer && !isCurrentAnswer) marker = '(Will be set as correct)';
        if (isCurrentAnswer && isNewAnswer) marker = '(Already correct)';

        console.log(`  ${key}: ${options[key]} ${marker}`);
      }

      console.log(`\nCURRENT ANSWER: ${question.correct_answer} - "${options[question.correct_answer]}"`);
      if (question.correct_answer !== newAnswer) {
        console.log(`NEW ANSWER: ${newAnswer} - "${options[newAnswer]}"`);
      } else {
        console.log('Answer is already correct.');
      }
      console.log(`EXPLANATION: ${question.explanation}`);
      console.log(`REASON FOR CHANGE: ${reason}`);
    }

    // Ask for confirmation before updating
    if (questionsToFix.length > 0) {
      const readline = createInterface({
        input: process.stdin,
        output: process.stdout
      });

      const answer = await new Promise(resolve => {
        readline.question(`\nUpdate all ${questionsToFix.length} questions? (y/n): `, resolve);
      });

      if (answer.toLowerCase() === 'y') {
        console.log('Updating questions...');

        let successCount = 0;
        let failCount = 0;

        for (const item of questionsToFix) {
          const { question, newAnswer } = item;

          // Skip if the answer is already correct
          if (question.correct_answer === newAnswer) {
            console.log(`Question ${question.id} already has the correct answer.`);
            continue;
          }

          const success = await updateQuestionCorrectAnswer(question.id, newAnswer);
          if (success) {
            successCount++;
            console.log(`Updated question: ${question.id}`);
          } else {
            failCount++;
            console.log(`Failed to update question: ${question.id}`);
          }
        }

        console.log(`\nUpdate complete. ${successCount} questions updated successfully, ${failCount} failed.`);
      } else {
        console.log('Update cancelled.');
      }

      readline.close();
    }

  } catch (error) {
    console.error('Error:', error);
  }
}

main();
