-- Create a function to run SQL statements (admin only)
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION public.run_sql(sql text)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the user is an admin
  IF EXISTS (
    SELECT 1 FROM public.admin_users
    WHERE user_id = auth.uid()
  ) THEN
    EXECUTE sql;
  ELSE
    RAISE EXCEPTION 'Permission denied. Only admins can run SQL statements.';
  END IF;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.run_sql(text) TO authenticated;
