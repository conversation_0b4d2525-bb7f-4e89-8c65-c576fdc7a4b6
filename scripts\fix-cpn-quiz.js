import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { createInterface } from 'readline';

dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Topic ID for CPN Cybersecurity Certification Exam
const TOPIC_ID = 'd7b04d9e-10e7-4e1c-9708-e558809c3fd2';

async function getQuestionsForTopic(topicId) {
  const { data, error } = await supabase
    .from('questions')
    .select('*')
    .eq('topic_id', topicId);
  
  if (error) {
    console.error('Error fetching questions:', error);
    return [];
  }
  
  return data;
}

async function updateQuestion(questionId, updates) {
  const { data, error } = await supabase
    .from('questions')
    .update(updates)
    .eq('id', questionId)
    .select();
  
  if (error) {
    console.error(`Error updating question ${questionId}:`, error);
    return false;
  }
  
  return true;
}

function findCorrectAnswerFromAsterisk(options) {
  // Find which option has an asterisk
  for (const key in options) {
    if (options[key].includes('*')) {
      return {
        correctKey: key,
        cleanedOptions: Object.fromEntries(
          Object.entries(options).map(([k, v]) => [k, v.replace('*', '')])
        )
      };
    }
  }
  
  // If no asterisk found, return null
  return null;
}

async function main() {
  try {
    console.log('Starting to fix CPN Cybersecurity Certification Exam quiz...');
    
    // Get all questions for the topic
    const questions = await getQuestionsForTopic(TOPIC_ID);
    console.log(`Found ${questions.length} questions.`);
    
    // Analyze questions and prepare updates
    const questionsToUpdate = [];
    
    for (const question of questions) {
      const result = findCorrectAnswerFromAsterisk(question.options);
      
      if (result) {
        // Found an asterisk, need to update
        questionsToUpdate.push({
          id: question.id,
          updates: {
            options: result.cleanedOptions,
            correct_answer: result.correctKey
          }
        });
      } else if (question.id === 'c3070a91-dcc0-4daf-b900-57c5b4eee465' || question.id === 'a6b77f48-d1c2-42ec-8b45-b45ead859f33') {
        // These questions already have correct answers and no asterisks
        console.log(`Question ${question.id} already fixed.`);
      } else {
        console.log(`No asterisk found in question ${question.id}: "${question.question_text}"`);
      }
    }
    
    console.log(`Found ${questionsToUpdate.length} questions to update.`);
    
    // Ask for confirmation before making changes
    const readline = createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    const answer = await new Promise(resolve => {
      readline.question(`This will update ${questionsToUpdate.length} questions. Continue? (y/n): `, resolve);
    });
    
    if (answer.toLowerCase() !== 'y') {
      console.log('Operation cancelled.');
      readline.close();
      return;
    }
    
    // Update questions
    console.log('\nUpdating questions...');
    let successCount = 0;
    
    for (const question of questionsToUpdate) {
      const success = await updateQuestion(question.id, question.updates);
      if (success) {
        successCount++;
        console.log(`Successfully updated question: ${question.id}`);
      }
    }
    
    console.log(`\nSummary: Updated ${successCount}/${questionsToUpdate.length} questions.`);
    
    readline.close();
  } catch (error) {
    console.error('Error:', error);
  }
}

main();
