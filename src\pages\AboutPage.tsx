
import Navbar from "@/components/Navbar";
import BottomNavigation from "@/components/BottomNavigation";
import { motion } from "framer-motion";
import { Lock, FileText, Award, BookOpen, GraduationCap } from "lucide-react";

const AboutPage = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5 }
    }
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Navbar />

      <main className="flex-1 container py-12">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="max-w-4xl mx-auto"
        >
          <motion.div variants={itemVariants} className="mb-8 text-center">
            <div className="inline-flex mb-4 p-3 rounded-full bg-cyber-primary/10">
              <img src="/secquiz-logo.svg" alt="SecQuiz Logo" className="h-14 w-14" />
            </div>
            <h1 className="text-3xl md:text-4xl font-bold mb-4">About SecQuiz</h1>
            <p className="text-muted-foreground">
              Your go-to platform for mastering cybersecurity concepts
            </p>
          </motion.div>

          <motion.div variants={itemVariants} className="mb-12">
            <div className="bg-background/60 backdrop-blur-sm border rounded-lg p-8">
              <h2 className="text-2xl font-bold mb-4">Our Mission</h2>
              <p className="text-muted-foreground mb-6">

                SecQuiz was created with a simple mission: to make cybersecurity education
                accessible, engaging, and effective for everyone.
                Cyber threats are rising, and the world urgently needs skilled defenders. As attacks grow more sophisticated, the demand for cybersecurity professionals has never been higher.
              </p>
              <p className="text-muted-foreground mb-6">
                SecQuiz was created to meet this need—making cybersecurity education accessible, engaging, and effective for everyone. Whether you're just starting out, preparing for certification, or advancing your skills, our platform helps you succeed through our unique Learn-Then-Quiz approach.
              </p>
              <p className="text-muted-foreground">
                Our comprehensive learning materials combined with interactive quizzes create a complete educational experience. First learn the concepts, then test your knowledge—all in one platform. Cybersecurity isn't just a career—it’s a mission. Start yours with SecQuiz.
              </p>
            </div>
          </motion.div>

          <motion.div variants={containerVariants} className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
            <motion.div
              variants={itemVariants}
              className="bg-background/60 backdrop-blur-sm border rounded-lg p-6 hover:border-cyber-primary/50 transition-all hover:shadow-md"
              whileHover={{ y: -5, transition: { duration: 0.2 } }}
            >
              <div className="mb-3 inline-flex items-center justify-center w-12 h-12 rounded-full bg-indigo-100">
                <GraduationCap className="h-6 w-6 text-indigo-600" />
              </div>
              <h3 className="text-xl font-medium mb-2">Learning Materials</h3>
              <p className="text-muted-foreground">
                Our comprehensive learning materials help you master cybersecurity concepts before testing your knowledge, creating a complete educational experience.
              </p>
            </motion.div>

            <motion.div
              variants={itemVariants}
              className="bg-background/60 backdrop-blur-sm border rounded-lg p-6 hover:border-cyber-primary/50 transition-all hover:shadow-md"
              whileHover={{ y: -5, transition: { duration: 0.2 } }}
            >
              <div className="mb-3 inline-flex items-center justify-center w-12 h-12 rounded-full bg-blue-100">
                <Lock className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="text-xl font-medium mb-2">Comprehensive Coverage</h3>
              <p className="text-muted-foreground">
                Our content covers all major cybersecurity domains, from network security
                to ethical hacking, encryption, compliance, and more.
              </p>
            </motion.div>

            <motion.div
              variants={itemVariants}
              className="bg-background/60 backdrop-blur-sm border rounded-lg p-6 hover:border-cyber-primary/50 transition-all hover:shadow-md"
              whileHover={{ y: -5, transition: { duration: 0.2 } }}
            >
              <div className="mb-3 inline-flex items-center justify-center w-12 h-12 rounded-full bg-purple-100">
                <FileText className="h-6 w-6 text-purple-600" />
              </div>
              <h3 className="text-xl font-medium mb-2">Certification-Focused</h3>
              <p className="text-muted-foreground">
                Prepare for industry certifications like CISSP, CEH, CompTIA Security+, and more
                with materials and questions that mirror the actual exam format.
              </p>
            </motion.div>

            <motion.div
              variants={itemVariants}
              className="bg-background/60 backdrop-blur-sm border rounded-lg p-6 hover:border-cyber-primary/50 transition-all hover:shadow-md"
              whileHover={{ y: -5, transition: { duration: 0.2 } }}
            >
              <div className="mb-3 inline-flex items-center justify-center w-12 h-12 rounded-full bg-green-100">
                <Award className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="text-xl font-medium mb-2">Track Your Progress</h3>
              <p className="text-muted-foreground">
                Monitor your performance, identify weak areas, and watch your skills improve
                over time with our detailed analytics.
              </p>
            </motion.div>

            <motion.div
              variants={itemVariants}
              className="bg-background/60 backdrop-blur-sm border rounded-lg p-6 hover:border-cyber-primary/50 transition-all hover:shadow-md"
              whileHover={{ y: -5, transition: { duration: 0.2 } }}
            >
              <div className="mb-3 inline-flex items-center justify-center w-12 h-12 rounded-full bg-cyan-100">
                <BookOpen className="h-6 w-6 text-cyan-600" />
              </div>
              <h3 className="text-xl font-medium mb-2">Learn-Then-Quiz Approach</h3>
              <p className="text-muted-foreground">
                Our unique approach ensures you understand concepts before being tested on them, creating a more effective learning experience.
              </p>
            </motion.div>

            <motion.div
              variants={itemVariants}
              className="bg-background/60 backdrop-blur-sm border rounded-lg p-6 hover:border-cyber-primary/50 transition-all hover:shadow-md"
              whileHover={{ y: -5, transition: { duration: 0.2 } }}
            >
              <div className="mb-3 inline-flex items-center justify-center w-12 h-12 rounded-full bg-orange-100">
                <img src="/secquiz-logo.svg" alt="SecQuiz Logo" className="h-6 w-6" />
              </div>
              <h3 className="text-xl font-medium mb-2">Expert-Crafted Content</h3>
              <p className="text-muted-foreground">
                All content is created and reviewed by cybersecurity professionals to
                ensure accuracy and relevance to industry standards.
              </p>
            </motion.div>
          </motion.div>
        </motion.div>
      </main>

      <BottomNavigation />
    </div>
  );
};

export default AboutPage;
