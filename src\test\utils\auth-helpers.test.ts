import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import {
  FREE_QUESTIONS_LIMIT,
  getRemainingFreeQuestions,
  getUsedFreeQuestions,
  incrementFreeQuestionsUsed,
  resetFreeQuestionsUsed,
} from '@/utils/auth-helpers';

describe('auth-helpers utility functions', () => {
  // Mock localStorage
  const localStorageMock = (() => {
    let store: Record<string, string> = {};
    return {
      getItem: vi.fn((key: string) => store[key] || null),
      setItem: vi.fn((key: string, value: string) => {
        store[key] = value.toString();
      }),
      clear: vi.fn(() => {
        store = {};
      }),
    };
  })();

  beforeEach(() => {
    // Setup localStorage mock
    Object.defineProperty(window, 'localStorage', {
      value: localStorageMock,
    });
    
    // Clear mock calls and localStorage before each test
    vi.clearAllMocks();
    localStorageMock.clear();
  });

  describe('getUsedFreeQuestions', () => {
    it('returns 0 when no questions have been used', () => {
      expect(getUsedFreeQuestions()).toBe(0);
      expect(localStorageMock.getItem).toHaveBeenCalledWith('free_questions_used');
    });

    it('returns the number of used questions from localStorage', () => {
      localStorageMock.getItem.mockReturnValueOnce('5');
      
      expect(getUsedFreeQuestions()).toBe(5);
      expect(localStorageMock.getItem).toHaveBeenCalledWith('free_questions_used');
    });

    it('returns 0 if localStorage value is not a number', () => {
      localStorageMock.getItem.mockReturnValueOnce('not-a-number');
      
      expect(getUsedFreeQuestions()).toBe(0);
      expect(localStorageMock.getItem).toHaveBeenCalledWith('free_questions_used');
    });

    it('handles localStorage errors gracefully', () => {
      localStorageMock.getItem.mockImplementationOnce(() => {
        throw new Error('localStorage error');
      });
      
      expect(getUsedFreeQuestions()).toBe(0);
      expect(localStorageMock.getItem).toHaveBeenCalledWith('free_questions_used');
    });
  });

  describe('getRemainingFreeQuestions', () => {
    it('returns the full limit when no questions have been used', () => {
      localStorageMock.getItem.mockReturnValueOnce('0');
      
      expect(getRemainingFreeQuestions()).toBe(FREE_QUESTIONS_LIMIT);
    });

    it('returns the remaining questions when some have been used', () => {
      localStorageMock.getItem.mockReturnValueOnce('3');
      
      expect(getRemainingFreeQuestions()).toBe(FREE_QUESTIONS_LIMIT - 3);
    });

    it('returns 0 when all questions have been used', () => {
      localStorageMock.getItem.mockReturnValueOnce(FREE_QUESTIONS_LIMIT.toString());
      
      expect(getRemainingFreeQuestions()).toBe(0);
    });

    it('returns 0 when more than the limit have been used', () => {
      localStorageMock.getItem.mockReturnValueOnce((FREE_QUESTIONS_LIMIT + 5).toString());
      
      expect(getRemainingFreeQuestions()).toBe(0);
    });
  });

  describe('incrementFreeQuestionsUsed', () => {
    it('increments the count when no questions have been used', () => {
      localStorageMock.getItem.mockReturnValueOnce('0');
      
      incrementFreeQuestionsUsed();
      
      expect(localStorageMock.setItem).toHaveBeenCalledWith('free_questions_used', '1');
    });

    it('increments the count when some questions have been used', () => {
      localStorageMock.getItem.mockReturnValueOnce('5');
      
      incrementFreeQuestionsUsed();
      
      expect(localStorageMock.setItem).toHaveBeenCalledWith('free_questions_used', '6');
    });

    it('handles localStorage errors gracefully', () => {
      localStorageMock.setItem.mockImplementationOnce(() => {
        throw new Error('localStorage error');
      });
      
      // Spy on console.error to prevent error output in tests
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      incrementFreeQuestionsUsed();
      
      expect(consoleErrorSpy).toHaveBeenCalled();
      
      // Restore console.error
      consoleErrorSpy.mockRestore();
    });
  });

  describe('resetFreeQuestionsUsed', () => {
    it('resets the count to 0', () => {
      resetFreeQuestionsUsed();
      
      expect(localStorageMock.setItem).toHaveBeenCalledWith('free_questions_used', '0');
    });

    it('handles localStorage errors gracefully', () => {
      localStorageMock.setItem.mockImplementationOnce(() => {
        throw new Error('localStorage error');
      });
      
      // Spy on console.error to prevent error output in tests
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      resetFreeQuestionsUsed();
      
      expect(consoleErrorSpy).toHaveBeenCalled();
      
      // Restore console.error
      consoleErrorSpy.mockRestore();
    });
  });
});
