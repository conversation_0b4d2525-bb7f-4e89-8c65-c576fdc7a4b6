
import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";

type TopicFormProps = {
  initialData?: {
    id?: string;
    title: string;
    description: string;
    icon: string;
    difficulty: string;
    is_active: boolean;
  };
  onSuccess: () => void;
  onCancel: () => void;
};

const defaultTopic = {
  title: "",
  description: "",
  icon: "",
  difficulty: "medium",
  is_active: true,
};

const AdminTopicForm = ({ initialData = defaultTopic, onSuccess, onCancel }: TopicFormProps) => {
  const { toast } = useToast();
  const [formData, setFormData] = useState(initialData);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const isEditing = !!initialData.id;

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      if (isEditing) {
        // Update existing topic
        const { error } = await supabase
          .from("topics")
          .update({
            title: formData.title,
            description: formData.description,
            icon: formData.icon,
            difficulty: formData.difficulty,
            is_active: formData.is_active,
          })
          .eq("id", initialData.id);

        if (error) throw error;
        toast({ title: "Topic updated successfully" });
      } else {
        // Create new topic
        const { error } = await supabase
          .from("topics")
          .insert({
            title: formData.title,
            description: formData.description,
            icon: formData.icon,
            difficulty: formData.difficulty,
            is_active: formData.is_active,
          });

        if (error) throw error;
        toast({ title: "Topic created successfully" });
      }

      onSuccess();
    } catch (error) {
      console.error("Error saving topic:", error);
      toast({ 
        title: "Error saving topic", 
        description: error.message,
        variant: "destructive" 
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="cyber-card p-6">
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="title">Topic Title</Label>
          <Input
            id="title"
            name="title"
            value={formData.title}
            onChange={handleChange}
            placeholder="e.g., CISSP Fundamentals"
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            name="description"
            value={formData.description || ""}
            onChange={handleChange}
            placeholder="Brief description of the topic"
            rows={3}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="icon">Icon (Lucide icon name)</Label>
          <Input
            id="icon"
            name="icon"
            value={formData.icon || ""}
            onChange={handleChange}
            placeholder="e.g., shield"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="difficulty">Difficulty</Label>
          <Select 
            value={formData.difficulty} 
            onValueChange={(value) => handleSelectChange("difficulty", value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select difficulty" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="easy">Easy</SelectItem>
              <SelectItem value="medium">Medium</SelectItem>
              <SelectItem value="hard">Hard</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label>Status</Label>
          <Select 
            value={formData.is_active ? "active" : "inactive"} 
            onValueChange={(value) => handleSelectChange("is_active", value === "active")}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex justify-end space-x-2 pt-4">
          <Button 
            type="button" 
            variant="outline" 
            onClick={onCancel}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button 
            type="submit" 
            className="bg-cyber-primary hover:bg-cyber-primary/90"
            disabled={isSubmitting}
          >
            {isSubmitting ? "Saving..." : isEditing ? "Update Topic" : "Create Topic"}
          </Button>
        </div>
      </form>
    </Card>
  );
};

export default AdminTopicForm;
