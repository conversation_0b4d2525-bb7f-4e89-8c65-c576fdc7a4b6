import { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import BottomNavigation from "@/components/BottomNavigation";
import Navbar from "@/components/Navbar";
import { Timer, Trophy, BookOpen, AlertCircle, Lightbulb as LightbulbIcon, Loader2, ChevronLeft, ChevronRight, GraduationCap } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { Link, useParams, useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { canAccessTopicSync, canAccessTopic, isTopicPremium, isUserPremium, PUBLIC_TOPICS, AUTHENTICATED_TOPICS } from "@/utils/topic-access";
import { featureFlags } from "@/config";
import { fetchLearningMaterialsByTopic, LearningMaterial } from "@/utils/fetch-learning-materials";
import { Input } from "@/components/ui/input";

// Utility function to decode HTML entities
const decodeHtmlEntities = (text: string): string => {
  const textarea = document.createElement('textarea');
  textarea.innerHTML = text;
  return textarea.value;
};

// Sample quiz data for fallback
const cissp_fundamentals: Question[] = [
  {
    id: "cissp1",
    text: "Which of the following best describes the concept of defense in depth?",
    options: [
      "A single strong security control that protects all assets",
      "Multiple layers of security controls throughout the system",
      "Focusing security resources on the most critical assets only",
      "Implementing the most cost-effective security controls"
    ],
    correctAnswer: 1,
    explanation: "Defense in depth involves implementing multiple layers of security controls throughout a system. Rather than relying on a single security measure, this approach uses various mechanisms that complement each other, so if one fails, others still provide protection."
  },
  {
    id: "cissp2",
    text: "What is the primary goal of risk management in information security?",
    options: [
      "Eliminating all security risks",
      "Identifying all potential threats",
      "Maximizing security spending",
      "Balancing security costs against potential losses"
    ],
    correctAnswer: 3,
    explanation: "The primary goal of risk management is to balance the cost of security controls against the potential losses from security incidents. It's not about eliminating all risks (which is impossible) but managing them effectively."
  }
];

// Empty arrays for other quiz types - in production these would have actual questions
const cyber_foundation_hard: Question[] = [];
const cyber_foundation_medium: Question[] = [];
const cyber_foundation_easy: Question[] = [];
const web_security: Question[] = [];

// Define types
interface Question {
  id: string;
  text: string;
  options: string[];
  correctAnswer: number;
  explanation: string;
  is_premium?: boolean;
}

// Define a type for Supabase question data
type SupabaseQuestion = {
  id: string;
  question_text: string;
  options: any; // Using any here because Supabase can return various formats
  correct_answer: string | number;
  explanation?: string;
  topic_id: string;
  is_premium?: boolean;
};

interface Quiz {
  id: string;
  title: string;
  description: string;
  is_premium: boolean;
  questions: Question[];
}

// Helper function to check if user is premium - using the utility function for consistency
const isPremiumUser = (user: any) => {
  return isUserPremium(user);
};

// Map of quiz topics to questions
const premiumQuizzesByTopic: Record<string, Question[]> = {
  "1": cissp_fundamentals,
  "2": cyber_foundation_hard,
  "3": cyber_foundation_medium,
  "4": cyber_foundation_easy,
  "5": web_security
};

// Helper functions to get quiz title and description
const getQuizTitle = (id: string) => {
  const titles: Record<string, string> = {
    "1": "CISSP Fundamentals",
    "2": "Cybersecurity Foundation - Hard",
    "3": "Cybersecurity Foundation - Medium",
    "4": "Cybersecurity Foundation - Easy",
    "5": "Web Security"
  };
  return titles[id] || "Cybersecurity Quiz";
};

const getQuizDescription = (id: string) => {
  const descriptions: Record<string, string> = {
    "1": "Core concepts and principles of the CISSP certification",
    "2": "Check your knowledge level. Attempt the hard level questions in Cybersecurity Foundations.",
    "3": "Check your knowledge level. Try the medium level questions in Cybersecurity Foundations.",
    "4": "Get ready to test your knowledge and skills in cybersecurity with our comprehensive quiz app!",
    "5": "Learn about common web vulnerabilities, OWASP Top 10, and secure coding practices."
  };
  return descriptions[id] || "Test your cybersecurity knowledge";
};

// This function is now replaced by checking the difficulty field in the database

const QuizPage = () => {
  const { topicId } = useParams<{ topicId: string }>();
  const { user } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  // Function to save quiz attempt to database
  const saveQuizAttempt = async (finalScore: number, totalQuestions: number, timeTaken: number) => {
    if (!user || !quiz) return;

    try {
      const { error } = await supabase
        .from('quiz_attempts')
        .insert({
          user_id: user.id,
          topic_id: quiz?.id,
          score: finalScore,
          total_questions: totalQuestions,
          answers: userAnswers,
          time_taken: timeTaken
        });

      if (error) {
        console.error('Error saving quiz attempt:', error);
      } else {
        console.log('Quiz attempt saved successfully');
      }
    } catch (error) {
      console.error('Error in saveQuizAttempt:', error);
    }
  };

  // State variables
  const [quiz, setQuiz] = useState<Quiz | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedOption, setSelectedOption] = useState<number | null>(null);
  const [showAnswer, setShowAnswer] = useState(false);
  const [score, setScore] = useState(0);
  const [timeLeft, setTimeLeft] = useState(30); // 30 seconds per question
  const [quizCompleted, setQuizCompleted] = useState(false);
  const [freeQuestionsUsed, setFreeQuestionsUsed] = useState(0);
  const [showFreeLimit, setShowFreeLimit] = useState(false);
  const [reviewMode, setReviewMode] = useState(false);
  const [userAnswers, setUserAnswers] = useState<(number | null)[]>([]);
  const startTimeRef = useRef<Date>(new Date());
  const [learningMaterials, setLearningMaterials] = useState<LearningMaterial[]>([]);
  const [showLearningMaterials, setShowLearningMaterials] = useState(false);

  // Coupon UI state
  const [showCouponInput, setShowCouponInput] = useState(false);
  const [couponCode, setCouponCode] = useState("");
  const [couponError, setCouponError] = useState("");

  // Load quiz data based on topic ID
  useEffect(() => {
    const loadQuiz = async () => {
      try {
        setLoading(true);

        // Reset learning materials state
        setLearningMaterials([]);

        // Make sure we have a valid topicId
        if (!topicId) {
          console.error("No topic ID provided");
          navigate("/quizzes");
          return;
        }

        // Convert topicId to string to ensure correct lookup
        const topicIdStr = String(topicId);

        // First, fetch the topic details from Supabase
        const { data: topicData, error: topicError } = await supabase
          .from("topics")
          .select("*")
          .eq("id", topicIdStr)
          .single();

        if (topicError) {
          console.error(`Topic not found: ${topicError.message}`);
          toast({
            title: "Topic Not Found",
            description: "The requested quiz topic could not be found.",
            variant: "destructive",
          });
          navigate("/quizzes");
          return;
        }

        // Debug user information
        console.log('User:', user ? { email: user.email, id: user.id } : 'No user');

        // Check if this is a premium topic based on our custom logic
        const isPremiumTopic = isTopicPremium(topicData.title, topicData.difficulty);

        // First check if this is a free topic (either public or for authenticated users)
        const isFreeForRegistered = PUBLIC_TOPICS.includes(topicData.title) || AUTHENTICATED_TOPICS.includes(topicData.title);

        // If the user is logged in and the topic is free for registered users, they can access it
        if (user && isFreeForRegistered) {
          console.log(`Topic: ${topicData.title} is free for registered users, granting access`);
          // Continue with quiz loading
        }
        // If the topic is public (accessible to everyone), allow access
        else if (PUBLIC_TOPICS.includes(topicData.title)) {
          console.log(`Topic: ${topicData.title} is public, granting access to everyone`);
          // Continue with quiz loading
        }
        // For premium topics, check if user has access
        else {
          // First do a quick synchronous check for immediate UI feedback
          let canAccess = canAccessTopicSync(topicData.title, topicData.id, user);
          console.log(`Topic: ${topicData.title}, Premium: ${isPremiumTopic}, Can Access (sync): ${canAccess}`);

          // If the sync check says user can't access, do a more thorough async check
          if (!canAccess) {
            try {
              // Do the async check that queries the database
              const asyncCanAccess = await canAccessTopic(topicData.title, topicData.id, user);
              console.log(`Topic: ${topicData.title}, Can Access (async): ${asyncCanAccess}`);

              // Update the access status if the async check says user can access
              if (asyncCanAccess) {
                canAccess = true;
              }
            } catch (error) {
              console.error("Error checking async access:", error);
              // Keep the result from the sync check
            }
          }

          // If user cannot access this topic, redirect to quizzes page
          if (!canAccess) {
            // In the section where user is denied access to Cybersecurity Foundation - Easy
            if (topicData.title === "Cybersecurity Foundation - Easy" && user) {
              setShowCouponInput(true);
              // Do NOT redirect away; just return to allow coupon input UI to render
              return;
            }
            // Different messages based on authentication status
            if (!user) {
              toast({
                title: "Authentication Required",
                description: "Please sign in to access this quiz",
                variant: "destructive",
              });
            } else if (isPremiumTopic) {
              toast({
                title: "Premium Content",
                description: "This quiz requires a premium subscription",
                variant: "destructive",
              });
            } else {
              toast({
                title: "Access Denied",
                description: "You don't have permission to access this quiz",
                variant: "destructive",
              });
            }
            navigate("/quizzes");
            return;
          }
        }

        // Fetch questions for this topic from Supabase
        console.log('Fetching questions for topic ID:', topicIdStr);
        const { data: questionsData, error: questionsError } = await supabase
          .from("questions")
          .select("*")
          .eq("topic_id", topicIdStr);

        console.log('Questions data from Supabase:', questionsData);

        if (questionsError) {
          console.error(`Failed to load questions: ${questionsError.message}`);
          toast({
            title: "Error",
            description: "Failed to load questions",
            variant: "destructive",
          });
          navigate("/quizzes");
          return;
        }

        // If no questions found in database, show a message
        if (!questionsData || questionsData.length === 0) {
          console.log('No questions found in database for topic:', topicIdStr);

          // Show message for topics with no questions
          toast({
            title: "No Questions Available",
            description: "This topic doesn't have any questions yet. Please check back later.",
            variant: "destructive",
          });
          navigate("/quizzes");
          return;
        }

        // Transform the questions data to match our Question interface
        const questions: Question[] = (questionsData as SupabaseQuestion[] || []).map((q) => {
          // Handle options parsing more carefully
          let options: string[] = [];
          try {
            // Check if options is already an array
            if (Array.isArray(q.options)) {
              options = q.options.map(opt => String(opt));
            }
            // Check if options is an object with numeric keys
            else if (typeof q.options === 'object' && q.options !== null) {
              const objValues = Object.values(q.options);
              options = objValues.map(opt => String(opt));
            }
            // If options is a string, try to parse it as JSON
            else if (typeof q.options === 'string') {
              const parsedOptions = JSON.parse(q.options);
              const objValues = Object.values(parsedOptions);
              options = objValues.map(opt => String(opt));
            }
          } catch (error) {
            console.error('Error parsing options for question:', q.id, error);
            // Fallback to empty options
            options = ['Option not available'];
          }

          // Parse correct answer safely
          let correctAnswer = 0;
          try {
            correctAnswer = typeof q.correct_answer === 'number'
              ? q.correct_answer
              : parseInt(q.correct_answer);

            // If parsing fails and returns NaN, default to 0
            if (isNaN(correctAnswer)) correctAnswer = 0;
          } catch (error) {
            console.error('Error parsing correct answer for question:', q.id, error);
          }

          return {
            id: q.id,
            text: decodeHtmlEntities(q.question_text || 'Question text not available'),
            options: options.map(option => decodeHtmlEntities(option)),
            correctAnswer: correctAnswer,
            explanation: decodeHtmlEntities(q.explanation || ""),
            is_premium: isPremiumTopic
          };
        });

        console.log('Questions found for this topic:', questions.length);

        // Create quiz object
        const quizData: Quiz = {
          id: topicIdStr,
          title: topicData.title,
          description: topicData.description || "",
          is_premium: isPremiumTopic,
          questions: questions
        };

        console.log('Quiz data:', quizData);

        setQuiz(quizData);

        // Initialize userAnswers array with nulls
        setUserAnswers(new Array(questions.length).fill(null));

        // Fetch learning materials for this topic
        try {
          const materials = await fetchLearningMaterialsByTopic(topicIdStr);
          setLearningMaterials(materials);
          console.log('Learning materials found for this topic:', materials.length);
        } catch (materialError) {
          console.error("Error loading learning materials:", materialError);
        }
      } catch (error) {
        console.error("Error loading quiz:", error);
        toast({
          title: "Error",
          description: "Failed to load quiz data",
          variant: "destructive",
        });
        navigate("/quizzes");
      } finally {
        setLoading(false);
      }
    };

    loadQuiz();
  }, [topicId, user, toast, navigate]);

  // Get free questions used from localStorage
  useEffect(() => {
    // For non-premium users and guests
    if (user && !isPremiumUser(user) && quiz && quiz.is_premium) {
      // Only apply free question limit for premium topics
      const usedQuestions = localStorage.getItem('freeQuestionsUsed');
      if (usedQuestions) {
        const count = parseInt(usedQuestions);
        setFreeQuestionsUsed(count);
        if (count >= 10) {
          setShowFreeLimit(true);
        }
      }
    }
  }, [user, quiz]);

  // Set the selected option when in review mode
  useEffect(() => {
    if (reviewMode && quiz) {
      setSelectedOption(userAnswers[currentQuestionIndex]);
    }
  }, [currentQuestionIndex, reviewMode, userAnswers]);

  // Timer effect
  useEffect(() => {
    if (!quiz || quizCompleted || showAnswer || showFreeLimit || reviewMode) return;

    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          setShowAnswer(true);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [quiz, quizCompleted, showAnswer, showFreeLimit, reviewMode]);

  // Loading state
  if (loading) {
    return (
      <div className="flex flex-col min-h-screen">
        <Navbar />
        <div className="flex-1 flex items-center justify-center">
          <div className="flex flex-col items-center">
            <Loader2 className="h-10 w-10 text-cyber-primary animate-spin mb-4" />
            <p className="text-muted-foreground">Loading quiz...</p>
          </div>
        </div>
      </div>
    );
  }

  // If no quiz data after loading
  // Only log in development mode to avoid console clutter
  if (featureFlags.debug) {
    console.log('Quiz data check:', quiz);
  }
  if (!quiz || !quiz.questions || quiz.questions.length === 0) {
    // Show coupon input and upgrade button for premium topics if access was denied
    if (showCouponInput) {
      return (
        <div className="flex flex-col min-h-screen">
          <Navbar />
          <div className="flex-1 container py-8 px-4 flex flex-col items-center justify-center">
            <div className="w-20 h-20 rounded-full bg-cyber-warning/20 flex items-center justify-center mb-6">
              <AlertCircle className="w-10 h-10 text-cyber-warning" />
            </div>
            <h1 className="text-2xl font-bold mb-2">Access Required</h1>
            <p className="text-center text-muted-foreground mb-8 max-w-md">
              This is a premium quiz. Please upgrade to premium or use a coupon code to access.
            </p>
            <div className="flex flex-col sm:flex-row gap-6 w-full max-w-md items-start justify-center">
              <div className="flex-1 w-full">
                <Input
                  placeholder="Enter coupon code"
                  value={couponCode}
                  onChange={e => setCouponCode(e.target.value)}
                  className="max-w-xs mb-2"
                />
                <Button
                  onClick={() => {
                    // Accept any valid coupon for any premium topic
                    if (couponCode.trim() === "FOUNDATION2025" || couponCode.trim() === "GRC0125") {
                      localStorage.setItem(`coupon_${user.id}_${quiz ? quiz.title : ''}`, "true");
                      setCouponError("");
                      toast({ title: "Coupon applied!", description: "You now have access to this premium quiz." });
                      setShowCouponInput(false);
                      window.location.reload();
                    } else {
                      setCouponError("Invalid coupon code");
                    }
                  }}
                  className="w-full"
                >
                  Redeem Coupon
                </Button>
                {couponError && <div className="text-red-500 text-sm mt-1">{couponError}</div>}
              </div>
              <div className="flex flex-col gap-2 w-full">
                <Button asChild className="bg-cyber-primary hover:bg-cyber-primary/90 w-full">
                  <Link to="/#pricing">Upgrade to Premium</Link>
                </Button>
                <Button asChild variant="outline" className="w-full">
                  <Link to="/quizzes">Back to Quizzes</Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="flex flex-col min-h-screen">
        <Navbar />
        <div className="flex-1 container py-8 px-4 flex flex-col items-center justify-center">
          <div className="w-20 h-20 rounded-full bg-cyber-warning/20 flex items-center justify-center mb-6">
            <AlertCircle className="w-10 h-10 text-cyber-warning" />
          </div>

          <h1 className="text-2xl font-bold mb-2">Quiz Not Found</h1>
          <p className="text-center text-muted-foreground mb-8 max-w-md">
            Sorry, we couldn't find the quiz you're looking for. Please try another topic.
          </p>

          <Button asChild className="bg-cyber-primary hover:bg-cyber-primary/90">
            <Link to="/quizzes">Browse Quizzes</Link>
          </Button>
        </div>
      </div>
    );
  }

  const currentQuestion = quiz.questions[currentQuestionIndex];
  const progress = ((currentQuestionIndex + 1) / quiz.questions.length) * 100;

  const handleOptionSelect = (index: number) => {
    if (showAnswer || reviewMode) return;
    setSelectedOption(index);
  };

  const handleSubmitAnswer = () => {
    if (selectedOption === null) {
      toast({
        title: "No option selected",
        description: "Please select an answer before continuing",
        variant: "destructive",
      });
      return;
    }
    setShowAnswer(true);
  };

  const handleNext = () => {
    // Store the user's answer for this question
    const newUserAnswers = [...userAnswers];
    newUserAnswers[currentQuestionIndex] = selectedOption;
    setUserAnswers(newUserAnswers);

    // Check if answer is correct and update score
    if (selectedOption === currentQuestion.correctAnswer) {
      setScore(score + 1);
    }

    // For non-premium users, track free questions used
    if (!isPremiumUser(user)) {
      // Only apply free question limit for premium topics
      // Free topics (PUBLIC_TOPICS and AUTHENTICATED_TOPICS) don't have a question limit for registered users
      if (quiz.is_premium &&
          !PUBLIC_TOPICS.includes(quiz.title) &&
          !AUTHENTICATED_TOPICS.includes(quiz.title)) {
        const newCount = freeQuestionsUsed + 1;
        localStorage.setItem('freeQuestionsUsed', newCount.toString());
        setFreeQuestionsUsed(newCount);

        // Check if we've hit the limit
        if (newCount >= 10 && currentQuestionIndex < quiz.questions.length - 1) {
          setShowFreeLimit(true);
          return;
        }
      }
    }

    if (currentQuestionIndex < quiz.questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
      setSelectedOption(null);
      setShowAnswer(false);
      setTimeLeft(30);
    } else {
      // Calculate final score, ensuring it doesn't exceed the total number of questions
      const rawFinalScore = score + (selectedOption === currentQuestion.correctAnswer ? 1 : 0);
      const finalScore = Math.min(rawFinalScore, quiz.questions.length);
      setQuizCompleted(true);

      // Save quiz attempt to database if user is logged in
      if (user) {
        const endTime = new Date();
        const timeTaken = Math.round((endTime.getTime() - startTimeRef.current.getTime()) / 1000); // in seconds

        saveQuizAttempt(finalScore, quiz.questions.length, timeTaken);
      }

      toast({
        title: "Quiz completed!",
        description: `You scored ${finalScore} out of ${quiz.questions.length}`,
      });
    }
  };

  // Free limit reached screen
  if (showFreeLimit) {
    return (
      <div className="flex flex-col min-h-screen">
        <Navbar />
        <div className="flex-1 container py-8 px-4 flex flex-col items-center justify-center">
          <div className="w-20 h-20 rounded-full bg-cyber-warning/20 flex items-center justify-center mb-6">
            <AlertCircle className="w-10 h-10 text-cyber-warning" />
          </div>

          <h1 className="text-2xl font-bold mb-2">Free Limit Reached</h1>
          <p className="text-center text-muted-foreground mb-8 max-w-md">
            You've reached the limit of free questions for premium content. Upgrade to premium to continue and access all premium content.
          </p>
          <p className="text-center text-muted-foreground mb-8 max-w-md">
            Note: All registered users have unlimited access to our three free courses: "Cybersecurity Foundation - Easy", "CIA Triad", and "ISC2 Certification".
          </p>

          <div className="flex flex-col sm:flex-row gap-4">
            <Button asChild variant="outline">
              <Link to="/quizzes">Browse Quizzes</Link>
            </Button>
            <Button asChild className="bg-cyber-primary hover:bg-cyber-primary/90">
              <Link to="/#pricing">Upgrade to Premium</Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Results screen
  if (quizCompleted) {
    // Calculate final score, ensuring it doesn't exceed the total number of questions
    const rawFinalScore = score + (selectedOption === currentQuestion.correctAnswer ? 1 : 0);
    const finalScore = Math.min(rawFinalScore, quiz.questions.length);
    // Ensure percentage doesn't exceed 100%
    const percentage = Math.min(100, Math.round((finalScore / quiz.questions.length) * 100));

    return (
      <div className="flex flex-col min-h-screen cyber-grid-bg pb-16">
        <Navbar />
        <div className="flex-1 container py-8 px-4">
          <div className="max-w-3xl mx-auto">
            <div className="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-6 mb-6">
              <div className="flex flex-col items-center mb-6">
                <div className="w-24 h-24 rounded-full bg-cyber-success/20 flex items-center justify-center mb-4">
                  <Trophy className="w-12 h-12 text-cyber-success" />
                </div>
                <h1 className="text-2xl font-bold mb-2">Quiz Completed!</h1>
                <p className="text-center text-muted-foreground mb-4">
                  You've completed the {quiz.title} quiz.
                </p>
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-3xl font-bold">{finalScore}</span>
                  <span className="text-muted-foreground">/ {quiz.questions.length}</span>
                </div>
                <div className="w-full max-w-md mb-4">
                  <Progress value={percentage} className="h-3" />
                </div>
                <div className={`px-2 py-1 rounded text-sm font-medium ${percentage >= 70 ? 'bg-green-100 text-green-800' : percentage >= 50 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}`}>
                  {percentage}% Score
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  variant="outline"
                  className="flex-1 sm:flex-initial"
                  onClick={() => {
                    setReviewMode(true);
                    setCurrentQuestionIndex(0);
                    setQuizCompleted(false);
                  }}
                >
                  <BookOpen className="h-4 w-4 mr-2" /> Review Answers
                </Button>
                <Button
                  asChild
                  className="flex-1 sm:flex-initial bg-cyber-primary hover:bg-cyber-primary/90"
                >
                  <Link to="/quizzes">More Quizzes</Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
        <BottomNavigation />
      </div>
    );
  }

  // Review mode or normal quiz mode
  return (
    <div className="flex flex-col min-h-screen cyber-grid-bg pb-16">
      <Navbar />
      <div className="flex-1 container py-8 px-4">
        <div className="max-w-3xl mx-auto">
          {/* Quiz header */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-2">
              <h1 className="text-xl font-bold">{quiz.title}</h1>
              <div className="flex items-center gap-2">
                {reviewMode && (
                  <Badge variant="outline" className="mr-2">Review Mode</Badge>
                )}
                <div className={`px-2 py-1 rounded text-sm font-medium ${quiz.is_premium ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800'}`}>
                  {quiz.is_premium ? "Premium" : "Free"}
                </div>

                {/* Learning materials button */}
                {learningMaterials.length > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-1"
                    onClick={() => setShowLearningMaterials(!showLearningMaterials)}
                  >
                    <GraduationCap className="h-4 w-4" />
                    <span>{showLearningMaterials ? "Hide Materials" : "Study Materials"}</span>
                  </Button>
                )}
              </div>
            </div>
            <p className="text-muted-foreground text-sm mb-4">{quiz.description}</p>

            {/* Learning materials section */}
            {showLearningMaterials && learningMaterials.length > 0 && (
              <div className="mb-6 bg-indigo-50 dark:bg-indigo-900/30 p-4 rounded-lg border border-indigo-100 dark:border-indigo-800">
                <h3 className="text-lg font-medium mb-3 flex items-center">
                  <GraduationCap className="h-5 w-5 mr-2 text-indigo-600 dark:text-indigo-400" />
                  <span>Learning Materials</span>
                </h3>
                <div className="space-y-3">
                  {learningMaterials.map((material) => (
                    <div key={material.id} className="flex items-start">
                      <div className="bg-white dark:bg-indigo-800 p-2 rounded-md shadow-sm flex-1">
                        <h4 className="font-medium mb-1">{material.title}</h4>
                        <p className="text-sm text-muted-foreground line-clamp-2">{material.summary || "No summary available"}</p>
                        <div className="mt-2">
                          <Button asChild variant="link" size="sm" className="p-0 h-auto text-indigo-600 dark:text-indigo-400">
                            <Link to={`/learn/${material.id}`}>Read Material</Link>
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <div className="w-full bg-gray-200 dark:bg-gray-700 h-2 rounded-full overflow-hidden">
              <div
                className="bg-cyber-primary h-full rounded-full"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
            <div className="flex justify-between text-xs text-muted-foreground mt-1">
              <span>Question {currentQuestionIndex + 1} of {quiz.questions.length}</span>
              {!reviewMode && (
                <div className="flex items-center">
                  <Timer className="h-3 w-3 mr-1" />
                  <span>{timeLeft}s</span>
                </div>
              )}
            </div>
          </div>

          {/* Question card */}
          <Card className="mb-6 p-6">
            <h2 className="text-lg font-medium mb-6">{currentQuestion.text}</h2>

            <div className="space-y-3">
              {currentQuestion.options.map((option, index) => (
                <div
                  key={index}
                  className={`
                    p-4 rounded-lg border cursor-pointer transition-colors
                    ${selectedOption === index ? 'border-cyber-primary bg-cyber-primary/10' : 'border-gray-200 dark:border-gray-700 hover:border-cyber-primary/50'}
                    ${showAnswer && index === currentQuestion.correctAnswer ? 'border-green-500 bg-green-500/10' : ''}
                    ${showAnswer && selectedOption === index && index !== currentQuestion.correctAnswer ? 'border-red-500 bg-red-500/10' : ''}
                  `}
                  onClick={() => handleOptionSelect(index)}
                >
                  <div className="flex items-start">
                    <div className={`
                      flex-shrink-0 w-6 h-6 rounded-full mr-3 flex items-center justify-center
                      ${selectedOption === index ? 'bg-cyber-primary text-white' : 'bg-gray-200 dark:bg-gray-700'}
                      ${showAnswer && index === currentQuestion.correctAnswer ? 'bg-green-500 text-white' : ''}
                      ${showAnswer && selectedOption === index && index !== currentQuestion.correctAnswer ? 'bg-red-500 text-white' : ''}
                    `}>
                      {String.fromCharCode(65 + index)}
                    </div>
                    <span className="flex-1">{option}</span>
                  </div>
                </div>
              ))}
            </div>

            {/* Explanation (shown after answering) */}
            {showAnswer && currentQuestion.explanation && (
              <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                <div className="flex items-start">
                  <LightbulbIcon className="h-5 w-5 text-blue-500 mr-2 mt-0.5" />
                  <div>
                    <h3 className="font-medium text-blue-700 dark:text-blue-300 mb-1">Explanation</h3>
                    <p className="text-sm text-blue-600 dark:text-blue-400">{currentQuestion.explanation}</p>
                  </div>
                </div>
              </div>
            )}
          </Card>

          {/* Action buttons */}
          <div className="flex justify-between">
            {reviewMode ? (
              <>
                <Button
                  variant="outline"
                  onClick={() => {
                    if (currentQuestionIndex > 0) {
                      setCurrentQuestionIndex(currentQuestionIndex - 1);
                    }
                  }}
                  disabled={currentQuestionIndex === 0}
                  className="flex items-center gap-1"
                >
                  <ChevronLeft className="h-4 w-4" /> Previous
                </Button>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setReviewMode(false);
                      setQuizCompleted(true);
                    }}
                  >
                    Back to Results
                  </Button>
                  <Button
                    onClick={() => {
                      if (currentQuestionIndex < quiz.questions.length - 1) {
                        setCurrentQuestionIndex(currentQuestionIndex + 1);
                      }
                    }}
                    disabled={currentQuestionIndex === quiz.questions.length - 1}
                    className="flex items-center gap-1"
                  >
                    Next <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </>
            ) : (
              <>
                {!showAnswer ? (
                  <Button
                    className="ml-auto bg-cyber-primary hover:bg-cyber-primary/90"
                    onClick={handleSubmitAnswer}
                  >
                    Submit Answer
                  </Button>
                ) : (
                  <Button
                    className="ml-auto bg-cyber-primary hover:bg-cyber-primary/90"
                    onClick={handleNext}
                  >
                    {currentQuestionIndex < quiz.questions.length - 1 ? "Next Question" : "See Results"}
                  </Button>
                )}
              </>
            )}
          </div>

          {/* Coupon input UI (show when showCouponInput is true) */}
          {showCouponInput && (
            <div className="mb-6 flex flex-col items-center">
              <Input
                placeholder="Enter coupon code"
                value={couponCode}
                onChange={e => setCouponCode(e.target.value)}
                className="max-w-xs mb-2"
              />
              <Button
                onClick={() => {
                  if (couponCode.trim() === "FOUNDATION2025") {
                    localStorage.setItem(`coupon_${user.id}_Cybersecurity Foundation - Easy`, "true");
                    setCouponError("");
                    toast({ title: "Coupon applied!", description: "You now have access to Cybersecurity Foundation - Easy." });
                    setShowCouponInput(false);
                    window.location.reload();
                  } else {
                    setCouponError("Invalid coupon code");
                  }
                }}
                className="w-full"
              >
                Redeem Coupon
              </Button>
              {couponError && <div className="text-red-500 text-sm mt-1">{couponError}</div>}
              <Button asChild variant="outline" className="mt-6">
                <Link to="/quizzes">Back to Quizzes</Link>
              </Button>
            </div>
          )}
        </div>
      </div>
      <BottomNavigation />
    </div>
  );
};



export default QuizPage;
