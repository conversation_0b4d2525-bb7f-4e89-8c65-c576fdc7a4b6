import { supabase } from '../lib/supabase';

// Define subscription plans and their durations
const SUBSCRIPTION_DURATIONS = {
  'basic': 7, // 7 days (weekly)
  'pro': 7,   // 7 days (weekly)
  'elite': 365 // 365 days (effectively lifetime or one-time)
};

export async function updateUserSubscription(email, planId, amount, reference) {
  try {
    // First, get the user ID from the email
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('email', email)
      .single();

    if (userError || !userData) {
      return {
        success: false,
        error: userError || 'User not found'
      };
    }

    const userId = userData.id;

    // Calculate subscription end date based on plan
    const startDate = new Date();
    const endDate = new Date();
    endDate.setDate(startDate.getDate() + SUBSCRIPTION_DURATIONS[planId]);

    // Check if user already has a subscription
    const { data: existingSubscription, error: subError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .single();

    let result;

    if (existingSubscription) {
      // Update existing subscription
      result = await supabase
        .from('subscriptions')
        .update({
          plan_id: planId,
          amount_paid: amount / 100, // Convert from kobo to naira
          start_date: startDate.toISOString(),
          end_date: endDate.toISOString(),
          is_active: true,
          last_payment_reference: reference,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId);
    } else {
      // Create new subscription
      result = await supabase
        .from('subscriptions')
        .insert({
          user_id: userId,
          plan_id: planId,
          amount_paid: amount / 100, // Convert from kobo to naira
          start_date: startDate.toISOString(),
          end_date: endDate.toISOString(),
          is_active: true,
          last_payment_reference: reference,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
    }

    // Also log the payment in a transactions table
    const transactionResult = await supabase
      .from('payment_transactions')
      .insert({
        user_id: userId,
        reference: reference,
        amount: amount / 100, // Convert from kobo to naira
        plan_id: planId,
        status: 'success',
        payment_method: 'paystack',
        created_at: new Date().toISOString()
      });

    // Update user_profiles table to ensure subscription status is consistent
    const { data: userProfile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('user_id', userId)
      .maybeSingle();

    if (userProfile) {
      // Update existing profile
      await supabase
        .from('user_profiles')
        .update({
          is_subscribed: true,
          subscription_expires_at: endDate.toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId);
    } else {
      // Create new profile
      await supabase
        .from('user_profiles')
        .insert({
          user_id: userId,
          is_subscribed: true,
          subscription_expires_at: endDate.toISOString(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
    }

    if (result.error) {
      return {
        success: false,
        error: result.error
      };
    }

    return {
      success: true,
      data: {
        userId,
        planId,
        endDate: endDate.toISOString()
      }
    };
  } catch (error) {
    console.error('Error updating subscription:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

export async function isUserSubscribed(userId) {
  try {
    if (!userId) return false;

    // First check user_profiles table
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('is_subscribed, subscription_expires_at')
      .eq('user_id', userId)
      .maybeSingle();

    if (profile && profile.is_subscribed) {
      // If there's an expiration date, check if it's still valid
      if (profile.subscription_expires_at) {
        const expirationDate = new Date(profile.subscription_expires_at);
        const now = new Date();

        // If subscription has expired, update the status in the database
        if (expirationDate <= now) {
          // Update the profile to mark subscription as expired
          await supabase
            .from('user_profiles')
            .update({
              is_subscribed: false,
              updated_at: new Date().toISOString()
            })
            .eq('user_id', userId);

          return false; // Subscription has expired
        }
      }

      return true; // Valid subscription
    }

    // Check if user has an active subscription in subscriptions table
    const { data, error } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .eq('is_active', true)
      .gte('end_date', new Date().toISOString())
      .single();

    if (!error && data) {
      // Also update the user_profiles table for consistency
      await supabase
        .from('user_profiles')
        .upsert({
          user_id: userId,
          is_subscribed: true,
          subscription_expires_at: data.end_date,
          updated_at: new Date().toISOString()
        }, { onConflict: 'user_id' });

      return true;
    }

    // Check for expired subscriptions and update status if needed
    const { data: expiredSub, error: expiredError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .eq('is_active', true)
      .lt('end_date', new Date().toISOString())
      .single();

    if (!expiredError && expiredSub) {
      // Mark subscription as inactive
      await supabase
        .from('subscriptions')
        .update({
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('id', expiredSub.id);

      // Update user_profiles table
      await supabase
        .from('user_profiles')
        .upsert({
          user_id: userId,
          is_subscribed: false,
          subscription_expires_at: expiredSub.end_date,
          updated_at: new Date().toISOString()
        }, { onConflict: 'user_id' });
    }

    return false;
  } catch (error) {
    console.error('Error checking subscription:', error);
    return false;
  }
}

export async function getUserSubscriptionDetails(userId) {
  try {
    if (!userId) return null;

    // First check user_profiles table
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('user_id', userId)
      .maybeSingle();

    if (!profileError && profile) {
      // Check if subscription has expired
      let isExpired = false;
      if (profile.subscription_expires_at) {
        const expirationDate = new Date(profile.subscription_expires_at);
        const now = new Date();
        isExpired = expirationDate <= now;
      }

      if (profile.is_subscribed && !isExpired) {
        // Get the subscription details from subscriptions table
        const { data: subscription } = await supabase
          .from('subscriptions')
          .select('*')
          .eq('user_id', userId)
          .order('end_date', { ascending: false })
          .limit(1);

        if (subscription && subscription.length > 0) {
          return {
            ...subscription[0],
            status: 'active',
            isExpired: false
          };
        }

        return {
          status: 'active',
          isExpired: false,
          expiresAt: profile.subscription_expires_at
        };
      } else if (profile.subscription_expires_at) {
        // Get the expired subscription details
        const { data: expiredSub } = await supabase
          .from('subscriptions')
          .select('*')
          .eq('user_id', userId)
          .order('end_date', { ascending: false })
          .limit(1);

        if (expiredSub && expiredSub.length > 0) {
          return {
            ...expiredSub[0],
            status: 'expired',
            isExpired: true
          };
        }

        return {
          status: 'expired',
          isExpired: true,
          expiresAt: profile.subscription_expires_at
        };
      }
    }

    // Check for active subscription
    const { data: activeSub, error: activeError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .eq('is_active', true)
      .gte('end_date', new Date().toISOString())
      .order('end_date', { ascending: false })
      .single();

    if (!activeError && activeSub) {
      return {
        ...activeSub,
        status: 'active',
        isExpired: false
      };
    }

    // Check for expired subscription
    const { data: expiredSub, error: expiredError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .lt('end_date', new Date().toISOString())
      .order('end_date', { ascending: false })
      .single();

    if (!expiredError && expiredSub) {
      return {
        ...expiredSub,
        status: 'expired',
        isExpired: true
      };
    }

    return {
      status: 'free',
      isExpired: false
    };
  } catch (error) {
    console.error('Error getting subscription details:', error);
    return null;
  }
}

/**
 * Check for expired subscriptions and update their status
 */
export async function checkAndUpdateExpiredSubscriptions() {
  try {
    const now = new Date().toISOString();

    // Find all active subscriptions that have expired
    const { data: expiredSubscriptions, error } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('is_active', true)
      .lt('end_date', now);

    if (error) {
      console.error('Error finding expired subscriptions:', error);
      return { success: false, error };
    }

    if (!expiredSubscriptions || expiredSubscriptions.length === 0) {
      return { success: true, updated: 0 };
    }

    // Update each expired subscription
    const updatePromises = expiredSubscriptions.map(async (sub) => {
      // Update subscription status
      const { error: updateError } = await supabase
        .from('subscriptions')
        .update({
          is_active: false,
          updated_at: now
        })
        .eq('id', sub.id);

      if (updateError) {
        console.error(`Error updating subscription ${sub.id}:`, updateError);
        return false;
      }

      // Update user_profiles table
      const { error: profileError } = await supabase
        .from('user_profiles')
        .update({
          is_subscribed: false,
          updated_at: now
        })
        .eq('user_id', sub.user_id);

      if (profileError) {
        console.error(`Error updating profile for user ${sub.user_id}:`, profileError);
      }

      return true;
    });

    const results = await Promise.all(updatePromises);
    const successCount = results.filter(result => result).length;

    return {
      success: true,
      updated: successCount,
      total: expiredSubscriptions.length
    };
  } catch (error) {
    console.error('Error checking expired subscriptions:', error);
    return { success: false, error: error.message };
  }
}
