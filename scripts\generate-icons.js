import fs from 'fs';
import path from 'path';
import { createCanvas, loadImage } from 'canvas';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create directory if it doesn't exist
const iconsDir = path.join(__dirname, '../public/icons');
if (!fs.existsSync(iconsDir)) {
  fs.mkdirSync(iconsDir, { recursive: true });
}

// Icon sizes to generate
const sizes = [72, 96, 128, 144, 152, 192, 384, 512];

async function generateIcons() {
  try {
    // Load the source image
    const sourceImage = await loadImage(path.join(__dirname, '../public/secquiz-logo.svg'));

    // Generate icons for each size
    for (const size of sizes) {
      const canvas = createCanvas(size, size);
      const ctx = canvas.getContext('2d');

      // Fill with white background
      ctx.fillStyle = '#ffffff';
      ctx.fillRect(0, 0, size, size);

      // Calculate dimensions to maintain aspect ratio
      const aspectRatio = sourceImage.width / sourceImage.height;
      let drawWidth, drawHeight;

      if (aspectRatio > 1) {
        drawWidth = size * 0.8;
        drawHeight = drawWidth / aspectRatio;
      } else {
        drawHeight = size * 0.8;
        drawWidth = drawHeight * aspectRatio;
      }

      // Center the image
      const x = (size - drawWidth) / 2;
      const y = (size - drawHeight) / 2;

      // Draw the image
      ctx.drawImage(sourceImage, x, y, drawWidth, drawHeight);

      // Save the image
      const buffer = canvas.toBuffer('image/png');
      fs.writeFileSync(path.join(iconsDir, `icon-${size}x${size}.png`), buffer);

      console.log(`Generated icon-${size}x${size}.png`);
    }

    console.log('All icons generated successfully!');
  } catch (error) {
    console.error('Error generating icons:', error);
  }
}

generateIcons();
