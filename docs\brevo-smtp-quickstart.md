# Brevo SMTP Quick Start Guide

This guide provides simple steps to implement Brevo SMTP for Supabase Auth to fix email verification delivery issues.

## What's Already Done

1. ✅ Created configuration files with your Brevo SMTP credentials
2. ✅ Added custom email templates for verification and password reset
3. ✅ Created scripts to apply the configuration and test email delivery
4. ✅ Added npm scripts to package.json for easy execution

## What You Need to Do Manually

### 1. Apply the SMTP Configuration

Run the following command to apply the Brevo SMTP configuration:

```bash
npm run apply:smtp
```

This will:
- Copy the direct configuration to Supabase's config file
- Restart Supabase to apply the changes

### 2. Test Email Delivery

Run the following command to test if email delivery is working:

```bash
npm run test:email
```

This will send a test email to `<EMAIL>`. Check your inbox (and spam folder) to verify it was received.

### 3. Verify in Supabase Dashboard (Optional)

If you have access to the Supabase dashboard:

1. Go to Authentication > Email Templates
2. Verify that the custom email templates are being used
3. Check the Authentication logs to see if emails are being sent successfully

## Troubleshooting

If you encounter issues:

1. **Emails not being sent**:
   - Check Brevo dashboard to see if there are any sending issues
   - Verify that your Brevo account is active and not suspended

2. **Configuration not applying**:
   - Try manually copying `supabase/config.direct.toml` to `supabase/config.toml`
   - Restart Supabase manually: `supabase stop` then `supabase start`

3. **Test script failing**:
   - Check that your Supabase URL and key are correctly set in your environment
   - Try running the script with Node directly: `node scripts/test-email-delivery.js`

## Next Steps

Once email verification is working:

1. Monitor email delivery in the Brevo dashboard
2. Consider setting up a custom domain for better deliverability
3. Implement email tracking to see open and click rates

## Security Note

The SMTP password is stored in configuration files. For production:

1. Use environment variables instead of hardcoded values
2. Regularly rotate your SMTP keys
3. Ensure config files with credentials are not committed to version control
