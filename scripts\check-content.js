// Simple script to check learning material content
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkContent() {
  try {
    console.log('Checking learning material content...');
    
    // Get the specific material ID from the URL
    const materialId = 'd19b40dc-6b1b-432a-855e-641f36480af9'; // DPO and DPIA in NDPR
    
    // Fetch the material
    const { data, error } = await supabase
      .from('learning_materials')
      .select('*')
      .eq('id', materialId)
      .single();
    
    if (error) {
      console.error('Error fetching material:', error);
      return;
    }
    
    console.log('Material found:');
    console.log(`Title: ${data.title}`);
    console.log(`Is Premium: ${data.is_premium}`);
    console.log(`Content length: ${data.content.length} characters`);
    console.log(`Content preview: ${data.content.substring(0, 200)}...`);
    
  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the function
checkContent();
