import { motion } from "framer-motion";
import QuizCard from "./QuizCard";
import { QuizTopicForUI } from "@/utils/fetch-topics";

interface QuizListProps {
  title: string;
  quizzes: QuizTopicForUI[];
  showViewAll?: boolean;
}

const QuizList = ({ title, quizzes, showViewAll = true }: QuizListProps) => {
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5 }
    }
  };

  return (
    <div className="py-8">
      <div className="flex justify-between items-center mb-6">
        <motion.h2 
          className="text-2xl font-bold text-white"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
        >
          {title}
        </motion.h2>
        
        {showViewAll && (
          <motion.a
            href="/quizzes"
            className="text-cyan-400 hover:text-cyan-300 text-sm font-medium"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            View all
          </motion.a>
        )}
      </div>
      
      <motion.div
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {quizzes.map((quiz) => (
          <motion.div key={quiz.id} variants={itemVariants}>
            <QuizCard
              id={quiz.id}
              title={quiz.title}
              description={quiz.description}
              questionCount={quiz.questionCount}
              points={quiz.points}
              difficulty={quiz.difficulty}
              isPremium={quiz.isPremium}
            />
          </motion.div>
        ))}
      </motion.div>
    </div>
  );
};

export default QuizList;
