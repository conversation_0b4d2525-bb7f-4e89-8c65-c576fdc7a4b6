import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { createInterface } from 'readline';

dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

// Topic ID for Web Security
const WEB_SECURITY_TOPIC_ID = 'c2d4bd4b-b4a0-4bb9-8a96-22a0388edd28';

// Manual corrections for specific questions
// Format: questionText (partial match) -> correct answer key
const MANUAL_CORRECTIONS = [
  // Add your corrections here as needed
  {
    questionPartialText: "What is the primary risk of Cross-Site Scripting (XSS)?",
    correctAnswer: "C", // Client-side code execution in user browsers
    reason: "The explanation states that XSS allows attackers to inject malicious scripts that execute in victims' browsers."
  },
  // Add more corrections as needed
];

async function getQuestionsForTopic(topicId) {
  const { data, error } = await supabase
    .from('questions')
    .select('*')
    .eq('topic_id', topicId);
  
  if (error) {
    console.error('Error fetching questions:', error);
    return [];
  }
  
  return data;
}

async function updateQuestionCorrectAnswer(questionId, correctAnswer) {
  const { data, error } = await supabase
    .from('questions')
    .update({ correct_answer: correctAnswer })
    .eq('id', questionId)
    .select();
  
  if (error) {
    console.error(`Error updating question ${questionId}:`, error);
    return false;
  }
  
  return true;
}

async function main() {
  try {
    console.log('Fetching Web Security questions...');
    const questions = await getQuestionsForTopic(WEB_SECURITY_TOPIC_ID);
    console.log(`Found ${questions.length} questions.`);
    
    const questionsToFix = [];
    
    // Find questions that need fixing based on manual corrections
    for (const question of questions) {
      for (const correction of MANUAL_CORRECTIONS) {
        if (question.question_text.includes(correction.questionPartialText)) {
          questionsToFix.push({
            question,
            newAnswer: correction.correctAnswer,
            reason: correction.reason
          });
          break;
        }
      }
    }
    
    console.log(`\n${questionsToFix.length} questions need fixing.`);
    
    // Display all questions that will be fixed
    for (const item of questionsToFix) {
      const { question, newAnswer, reason } = item;
      
      console.log('\n' + '-'.repeat(80));
      console.log(`QUESTION: ${question.question_text}`);
      
      // Display all options
      console.log('\nOPTIONS:');
      const options = question.options;
      let optionKeys = [];
      
      if (typeof options === 'object') {
        // Handle both numeric and letter keys
        if ('A' in options || 'B' in options) {
          // Letter keys (A, B, C, D)
          optionKeys = Object.keys(options).filter(key => ['A', 'B', 'C', 'D'].includes(key));
        } else {
          // Numeric keys (0, 1, 2, 3)
          optionKeys = Object.keys(options).filter(key => ['0', '1', '2', '3'].includes(key));
        }
      }
      
      for (const key of optionKeys) {
        const isCurrentAnswer = key === question.correct_answer;
        const isNewAnswer = key === newAnswer;
        let marker = '';
        if (isCurrentAnswer) marker = '(Current Answer)';
        if (isNewAnswer && !isCurrentAnswer) marker = '(Will be set as correct)';
        if (isCurrentAnswer && isNewAnswer) marker = '(Already correct)';
        
        console.log(`  ${key}: ${options[key]} ${marker}`);
      }
      
      console.log(`\nCURRENT ANSWER: ${question.correct_answer} - "${options[question.correct_answer]}"`);
      if (question.correct_answer !== newAnswer) {
        console.log(`NEW ANSWER: ${newAnswer} - "${options[newAnswer]}"`);
      } else {
        console.log('Answer is already correct.');
      }
      console.log(`EXPLANATION: ${question.explanation}`);
      console.log(`REASON FOR CHANGE: ${reason}`);
    }
    
    // Ask for confirmation before updating
    if (questionsToFix.length > 0) {
      const readline = createInterface({
        input: process.stdin,
        output: process.stdout
      });
      
      const answer = await new Promise(resolve => {
        readline.question(`\nUpdate all ${questionsToFix.length} questions? (y/n): `, resolve);
      });
      
      if (answer.toLowerCase() === 'y') {
        console.log('Updating questions...');
        
        let successCount = 0;
        let failCount = 0;
        
        for (const item of questionsToFix) {
          const { question, newAnswer } = item;
          
          // Skip if the answer is already correct
          if (question.correct_answer === newAnswer) {
            console.log(`Question ${question.id} already has the correct answer.`);
            continue;
          }
          
          const success = await updateQuestionCorrectAnswer(question.id, newAnswer);
          if (success) {
            successCount++;
            console.log(`Updated question: ${question.id}`);
          } else {
            failCount++;
            console.log(`Failed to update question: ${question.id}`);
          }
        }
        
        console.log(`\nUpdate complete. ${successCount} questions updated successfully, ${failCount} failed.`);
      } else {
        console.log('Update cancelled.');
      }
      
      readline.close();
    }
    
    // If no questions need fixing, show all questions for review
    if (questionsToFix.length === 0) {
      console.log('\nNo questions need fixing based on the manual corrections.');
      console.log('Here are all the Web Security questions for review:');
      
      for (const question of questions) {
        console.log('\n' + '-'.repeat(80));
        console.log(`QUESTION: ${question.question_text}`);
        
        // Display all options
        console.log('\nOPTIONS:');
        const options = question.options;
        let optionKeys = [];
        
        if (typeof options === 'object') {
          // Handle both numeric and letter keys
          if ('A' in options || 'B' in options) {
            // Letter keys (A, B, C, D)
            optionKeys = Object.keys(options).filter(key => ['A', 'B', 'C', 'D'].includes(key));
          } else {
            // Numeric keys (0, 1, 2, 3)
            optionKeys = Object.keys(options).filter(key => ['0', '1', '2', '3'].includes(key));
          }
        }
        
        for (const key of optionKeys) {
          const isCurrentAnswer = key === question.correct_answer;
          const marker = isCurrentAnswer ? '(Current Answer)' : '';
          console.log(`  ${key}: ${options[key]} ${marker}`);
        }
        
        console.log(`\nCURRENT ANSWER: ${question.correct_answer} - "${options[question.correct_answer]}"`);
        console.log(`EXPLANATION: ${question.explanation}`);
      }
    }
    
  } catch (error) {
    console.error('Error:', error);
  }
}

main();
