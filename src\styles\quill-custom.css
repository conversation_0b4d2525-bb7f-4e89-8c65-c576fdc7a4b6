/* Custom Quill Editor Icons and Styles */

/* Custom toolbar buttons */
.ql-info-box, .ql-warning-box, .ql-success-box, .ql-tip-box, .ql-example-box {
  width: 28px;
  height: 24px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
}

.ql-info-box:after {
  content: "i";
  font-family: serif;
  font-style: italic;
  font-weight: bold;
  color: #6366F1;
  font-size: 16px;
}

.ql-warning-box:after {
  content: "!";
  font-family: sans-serif;
  font-weight: bold;
  color: #F59E0B;
  font-size: 16px;
}

.ql-success-box:after {
  content: "✓";
  font-family: sans-serif;
  font-weight: bold;
  color: #10B981;
  font-size: 14px;
}

.ql-tip-box:after {
  content: "💡";
  font-family: sans-serif;
  font-size: 14px;
}

.ql-example-box:after {
  content: "Ex";
  font-family: monospace;
  font-weight: bold;
  color: #6B7280;
  font-size: 12px;
}

/* Tooltip for custom buttons */
.ql-info-box:hover:before,
.ql-warning-box:hover:before,
.ql-success-box:hover:before,
.ql-tip-box:hover:before,
.ql-example-box:hover:before {
  position: absolute;
  background-color: #1F2937;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
  z-index: 10;
}

.ql-info-box:hover:before {
  content: "Insert Info Box";
}

.ql-warning-box:hover:before {
  content: "Insert Warning Box";
}

.ql-success-box:hover:before {
  content: "Insert Best Practice Box";
}

.ql-tip-box:hover:before {
  content: "Insert Tip Box";
}

.ql-example-box:hover:before {
  content: "Insert Example Box";
}

/* Custom HTML embed styling */
.html-embed {
  margin: 1rem 0;
}

/* Make sure the editor can display our custom elements properly */
.ql-editor .info-box,
.ql-editor .warning-box,
.ql-editor .success-box,
.ql-editor .tip-box,
.ql-editor .example-box {
  margin: 1rem 0;
  padding: 1rem;
  border-radius: 0.375rem;
}

.ql-editor .info-box {
  background-color: #EEF2FF;
  border-left: 4px solid #6366F1;
}

.ql-editor .warning-box {
  background-color: #FEF3C7;
  border-left: 4px solid #F59E0B;
}

.ql-editor .success-box {
  background-color: #ECFDF5;
  border-left: 4px solid #10B981;
}

.ql-editor .tip-box {
  background-color: #F5F3FF;
  border-left: 4px solid #8B5CF6;
}

.ql-editor .example-box {
  background-color: #F3F4F6;
  border-left: 4px solid #9CA3AF;
}

.ql-editor .box-title {
  font-weight: 600;
  margin-top: 0;
  margin-bottom: 0.5rem;
}

/* Separator for custom buttons */
.ql-formats:last-child {
  border-left: 1px solid #ccc;
  padding-left: 8px;
  margin-left: 8px;
}
