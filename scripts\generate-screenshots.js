import fs from 'fs';
import path from 'path';
import { createCanvas, loadImage } from 'canvas';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create directory if it doesn't exist
const screenshotsDir = path.join(__dirname, '../public/screenshots');
if (!fs.existsSync(screenshotsDir)) {
  fs.mkdirSync(screenshotsDir, { recursive: true });
}

// Screenshot dimensions (mobile)
const width = 1080;
const height = 1920;

async function generateScreenshots() {
  try {
    // Load the source image
    const logoImage = await loadImage(path.join(__dirname, '../public/secquiz-logo.svg'));
    
    // Generate home screenshot
    const homeCanvas = createCanvas(width, height);
    const homeCtx = homeCanvas.getContext('2d');
    
    // Fill with gradient background
    const homeGradient = homeCtx.createLinearGradient(0, 0, 0, height);
    homeGradient.addColorStop(0, '#4f46e5');
    homeGradient.addColorStop(1, '#3730a3');
    homeCtx.fillStyle = homeGradient;
    homeCtx.fillRect(0, 0, width, height);
    
    // Draw logo
    const logoSize = width * 0.4;
    const logoX = (width - logoSize) / 2;
    const logoY = height * 0.2;
    homeCtx.drawImage(logoImage, logoX, logoY, logoSize, logoSize);
    
    // Add text
    homeCtx.fillStyle = '#ffffff';
    homeCtx.font = 'bold 60px Arial';
    homeCtx.textAlign = 'center';
    homeCtx.fillText('SecQuiz', width / 2, logoY + logoSize + 100);
    
    homeCtx.font = '40px Arial';
    homeCtx.fillText('Master Cybersecurity', width / 2, logoY + logoSize + 180);
    homeCtx.fillText('Through Interactive Quizzes', width / 2, logoY + logoSize + 240);
    
    // Save the image
    const homeBuffer = homeCanvas.toBuffer('image/png');
    fs.writeFileSync(path.join(screenshotsDir, 'home.png'), homeBuffer);
    console.log('Generated home.png');
    
    // Generate quiz screenshot
    const quizCanvas = createCanvas(width, height);
    const quizCtx = quizCanvas.getContext('2d');
    
    // Fill with white background
    quizCtx.fillStyle = '#ffffff';
    quizCtx.fillRect(0, 0, width, height);
    
    // Draw header
    quizCtx.fillStyle = '#4f46e5';
    quizCtx.fillRect(0, 0, width, 200);
    
    // Draw logo in header
    const headerLogoSize = 80;
    const headerLogoX = 40;
    const headerLogoY = 60;
    quizCtx.drawImage(logoImage, headerLogoX, headerLogoY, headerLogoSize, headerLogoSize);
    
    // Add header text
    quizCtx.fillStyle = '#ffffff';
    quizCtx.font = 'bold 40px Arial';
    quizCtx.textAlign = 'left';
    quizCtx.fillText('SecQuiz', headerLogoX + headerLogoSize + 20, headerLogoY + 50);
    
    // Draw question card
    quizCtx.fillStyle = '#f8fafc';
    quizCtx.shadowColor = 'rgba(0, 0, 0, 0.1)';
    quizCtx.shadowBlur = 10;
    quizCtx.shadowOffsetX = 0;
    quizCtx.shadowOffsetY = 4;
    quizCtx.fillRect(40, 250, width - 80, 400);
    
    // Add question text
    quizCtx.shadowBlur = 0;
    quizCtx.fillStyle = '#1e293b';
    quizCtx.font = 'bold 36px Arial';
    quizCtx.textAlign = 'left';
    quizCtx.fillText('What is the primary goal of', 80, 320);
    quizCtx.fillText('cybersecurity?', 80, 370);
    
    // Draw answer options
    const options = [
      'A. Prevent all attacks',
      'B. Protect CIA triad',
      'C. Encrypt all data',
      'D. Monitor network traffic'
    ];
    
    options.forEach((option, index) => {
      const y = 700 + index * 120;
      
      // Option box
      quizCtx.fillStyle = index === 1 ? '#4f46e5' : '#ffffff';
      quizCtx.shadowColor = 'rgba(0, 0, 0, 0.1)';
      quizCtx.shadowBlur = 10;
      quizCtx.shadowOffsetX = 0;
      quizCtx.shadowOffsetY = 4;
      quizCtx.fillRect(40, y, width - 80, 100);
      
      // Option text
      quizCtx.shadowBlur = 0;
      quizCtx.fillStyle = index === 1 ? '#ffffff' : '#1e293b';
      quizCtx.font = '32px Arial';
      quizCtx.textAlign = 'left';
      quizCtx.fillText(option, 80, y + 60);
    });
    
    // Save the image
    const quizBuffer = quizCanvas.toBuffer('image/png');
    fs.writeFileSync(path.join(screenshotsDir, 'quiz.png'), quizBuffer);
    console.log('Generated quiz.png');
    
    console.log('All screenshots generated successfully!');
  } catch (error) {
    console.error('Error generating screenshots:', error);
  }
}

generateScreenshots();
